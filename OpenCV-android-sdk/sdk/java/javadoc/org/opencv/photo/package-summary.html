<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.photo (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.photo">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.photo" class="title">Package org.opencv.photo</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.photo</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AlignExposures.html" title="class in org.opencv.photo">AlignExposures</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The base class for algorithms that align images of the same scene with different exposures</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This algorithm converts images to median threshold bitmaps (1 for pixels brighter than median
 luminance and 0 otherwise) and than aligns the resulting bitmaps using bit operations.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CalibrateCRF.html" title="class in org.opencv.photo">CalibrateCRF</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The base class for camera response calibration algorithms.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Inverse camera response function is extracted for each brightness value by minimizing an objective
 function as linear system.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Inverse camera response function is extracted for each brightness value by minimizing an objective
 function as linear system.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The resulting HDR image is calculated as weighted average of the exposures considering exposure
 values and camera response.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MergeExposures.html" title="class in org.opencv.photo">MergeExposures</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The base class algorithms that can merge exposure sequence to a single image.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Pixels are weighted using contrast, saturation and well-exposedness measures, than images are
 combined using laplacian pyramids.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The resulting HDR image is calculated as weighted average of the exposures considering exposure
 values and camera response.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Photo.html" title="class in org.opencv.photo">Photo</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Tonemap.html" title="class in org.opencv.photo">Tonemap</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for tonemapping algorithms - tools that are used to map HDR image to 8-bit range.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adaptive logarithmic mapping is a fast global tonemapping algorithm that scales the image in
 logarithmic domain.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This algorithm transforms image to contrast using gradients on all levels of gaussian pyramid,
 transforms contrast values to HVS response and scales the response.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a global tonemapping operator that models human visual system.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
