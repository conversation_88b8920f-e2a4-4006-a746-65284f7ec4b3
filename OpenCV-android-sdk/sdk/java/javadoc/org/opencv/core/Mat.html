<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Mat (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.core, class: Mat">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.core</a></div>
<h1 title="Class Mat" class="title">Class Mat</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.core.Mat</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="MatOfByte.html" title="class in org.opencv.core">MatOfByte</a></code>, <code><a href="MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a></code>, <code><a href="MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a></code>, <code><a href="MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code>, <code><a href="MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a></code>, <code><a href="MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a></code>, <code><a href="MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></code>, <code><a href="MatOfInt4.html" title="class in org.opencv.core">MatOfInt4</a></code>, <code><a href="MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a></code>, <code><a href="MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a></code>, <code><a href="MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a></code>, <code><a href="MatOfPoint3.html" title="class in org.opencv.core">MatOfPoint3</a></code>, <code><a href="MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a></code>, <code><a href="MatOfRect.html" title="class in org.opencv.core">MatOfRect</a></code>, <code><a href="MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a></code>, <code><a href="MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Mat</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Mat.Atable.html" class="type-name-link" title="interface in org.opencv.core">Mat.Atable</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Mat.Tuple2.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Tuple2.html" title="type parameter in Mat.Tuple2">T</a>&gt;</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Mat.Tuple3.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Tuple3.html" title="type parameter in Mat.Tuple3">T</a>&gt;</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Mat.Tuple4.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Tuple4.html" title="type parameter in Mat.Tuple4">T</a>&gt;</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final long</code></div>
<div class="col-second even-row-color"><code><a href="#nativeObj" class="member-name-link">nativeObj</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Mat</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int%5B%5D,int)" class="member-name-link">Mat</a><wbr>(int[]&nbsp;sizes,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int%5B%5D,int,org.opencv.core.Scalar)" class="member-name-link">Mat</a><wbr>(int[]&nbsp;sizes,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int,int)" class="member-name-link">Mat</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int,int,java.nio.ByteBuffer)" class="member-name-link">Mat</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="class or interface in java.nio" class="external-link">ByteBuffer</a>&nbsp;data)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int,int,java.nio.ByteBuffer,long)" class="member-name-link">Mat</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="class or interface in java.nio" class="external-link">ByteBuffer</a>&nbsp;data,
 long&nbsp;step)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int,int,org.opencv.core.Scalar)" class="member-name-link">Mat</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(long)" class="member-name-link">Mat</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,org.opencv.core.Range)" class="member-name-link">Mat</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,org.opencv.core.Range%5B%5D)" class="member-name-link">Mat</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>[]&nbsp;ranges)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,org.opencv.core.Range,org.opencv.core.Range)" class="member-name-link">Mat</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,org.opencv.core.Rect)" class="member-name-link">Mat</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,int)" class="member-name-link">Mat</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,int,org.opencv.core.Scalar)" class="member-name-link">Mat</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#adjustROI(int,int,int,int)" class="member-name-link">adjustROI</a><wbr>(int&nbsp;dtop,
 int&nbsp;dbottom,
 int&nbsp;dleft,
 int&nbsp;dright)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assignTo(org.opencv.core.Mat)" class="member-name-link">assignTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assignTo(org.opencv.core.Mat,int)" class="member-name-link">assignTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;<a href="Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#at(java.lang.Class,int%5B%5D)" class="member-name-link">at</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz,
 int[]&nbsp;idx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;<a href="Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#at(java.lang.Class,int,int)" class="member-name-link">at</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz,
 int&nbsp;row,
 int&nbsp;col)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#channels()" class="member-name-link">channels</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkVector(int)" class="member-name-link">checkVector</a><wbr>(int&nbsp;elemChannels)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkVector(int,int)" class="member-name-link">checkVector</a><wbr>(int&nbsp;elemChannels,
 int&nbsp;depth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkVector(int,int,boolean)" class="member-name-link">checkVector</a><wbr>(int&nbsp;elemChannels,
 int&nbsp;depth,
 boolean&nbsp;requireContinuous)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#col(int)" class="member-name-link">col</a><wbr>(int&nbsp;x)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#colRange(int,int)" class="member-name-link">colRange</a><wbr>(int&nbsp;startcol,
 int&nbsp;endcol)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#colRange(org.opencv.core.Range)" class="member-name-link">colRange</a><wbr>(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cols()" class="member-name-link">cols</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#convertTo(org.opencv.core.Mat,int)" class="member-name-link">convertTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#convertTo(org.opencv.core.Mat,int,double)" class="member-name-link">convertTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype,
 double&nbsp;alpha)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#convertTo(org.opencv.core.Mat,int,double,double)" class="member-name-link">convertTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype,
 double&nbsp;alpha,
 double&nbsp;beta)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copySize(org.opencv.core.Mat)" class="member-name-link">copySize</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyTo(org.opencv.core.Mat)" class="member-name-link">copyTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyTo(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">copyTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#create(int%5B%5D,int)" class="member-name-link">create</a><wbr>(int[]&nbsp;sizes,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#create(int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Size,int)" class="member-name-link">create</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cross(org.opencv.core.Mat)" class="member-name-link">cross</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dataAddr()" class="member-name-link">dataAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#depth()" class="member-name-link">depth</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#diag()" class="member-name-link">diag</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#diag(int)" class="member-name-link">diag</a><wbr>(int&nbsp;d)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#diag(org.opencv.core.Mat)" class="member-name-link">diag</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;d)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dims()" class="member-name-link">dims</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dot(org.opencv.core.Mat)" class="member-name-link">dot</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dump()" class="member-name-link">dump</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#elemSize()" class="member-name-link">elemSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#elemSize1()" class="member-name-link">elemSize1</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#eye(int,int,int)" class="member-name-link">eye</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#eye(org.opencv.core.Size,int)" class="member-name-link">eye</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D,byte%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx,
 byte[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D,double%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx,
 double[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D,float%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx,
 float[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D,int%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx,
 int[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int%5B%5D,short%5B%5D)" class="member-name-link">get</a><wbr>(int[]&nbsp;idx,
 short[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int,byte%5B%5D)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int,double%5B%5D)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 double[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int,float%5B%5D)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 float[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int,int%5B%5D)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 int[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int,int,short%5B%5D)" class="member-name-link">get</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 short[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#height()" class="member-name-link">height</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#inv()" class="member-name-link">inv</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#inv(int)" class="member-name-link">inv</a><wbr>(int&nbsp;method)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isContinuous()" class="member-name-link">isContinuous</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSubmatrix()" class="member-name-link">isSubmatrix</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#locateROI(org.opencv.core.Size,org.opencv.core.Point)" class="member-name-link">locateROI</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;wholeSize,
 <a href="Point.html" title="class in org.opencv.core">Point</a>&nbsp;ofs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#matMul(org.opencv.core.Mat)" class="member-name-link">matMul</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Matrix multiplication</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mul(org.opencv.core.Mat)" class="member-name-link">mul</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Element-wise multiplication</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mul(org.opencv.core.Mat,double)" class="member-name-link">mul</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 double&nbsp;scale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Element-wise multiplication with scale factor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#ones(int%5B%5D,int)" class="member-name-link">ones</a><wbr>(int[]&nbsp;sizes,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#ones(int,int,int)" class="member-name-link">ones</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#ones(org.opencv.core.Size,int)" class="member-name-link">ones</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#push_back(org.opencv.core.Mat)" class="member-name-link">push_back</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,byte%5B%5D)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,byte%5B%5D,int,int)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,double...)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 double...&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,float%5B%5D)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 float[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,int%5B%5D)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 int[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int%5B%5D,short%5B%5D)" class="member-name-link">put</a><wbr>(int[]&nbsp;idx,
 short[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,byte%5B%5D)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,byte%5B%5D,int,int)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,double...)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 double...&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,float%5B%5D)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 float[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,int%5B%5D)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 int[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(int,int,short%5B%5D)" class="member-name-link">put</a><wbr>(int&nbsp;row,
 int&nbsp;col,
 short[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reshape(int)" class="member-name-link">reshape</a><wbr>(int&nbsp;cn)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reshape(int,int)" class="member-name-link">reshape</a><wbr>(int&nbsp;cn,
 int&nbsp;rows)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reshape(int,int%5B%5D)" class="member-name-link">reshape</a><wbr>(int&nbsp;cn,
 int[]&nbsp;newshape)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#row(int)" class="member-name-link">row</a><wbr>(int&nbsp;y)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rowRange(int,int)" class="member-name-link">rowRange</a><wbr>(int&nbsp;startrow,
 int&nbsp;endrow)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rowRange(org.opencv.core.Range)" class="member-name-link">rowRange</a><wbr>(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rows()" class="member-name-link">rows</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTo(org.opencv.core.Mat)" class="member-name-link">setTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTo(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">setTo</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTo(org.opencv.core.Scalar)" class="member-name-link">setTo</a><wbr>(<a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTo(org.opencv.core.Scalar,org.opencv.core.Mat)" class="member-name-link">setTo</a><wbr>(<a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#size(int)" class="member-name-link">size</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#step1()" class="member-name-link">step1</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#step1(int)" class="member-name-link">step1</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#submat(int,int,int,int)" class="member-name-link">submat</a><wbr>(int&nbsp;rowStart,
 int&nbsp;rowEnd,
 int&nbsp;colStart,
 int&nbsp;colEnd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#submat(org.opencv.core.Range%5B%5D)" class="member-name-link">submat</a><wbr>(<a href="Range.html" title="class in org.opencv.core">Range</a>[]&nbsp;ranges)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#submat(org.opencv.core.Range,org.opencv.core.Range)" class="member-name-link">submat</a><wbr>(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#submat(org.opencv.core.Rect)" class="member-name-link">submat</a><wbr>(<a href="Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#t()" class="member-name-link">t</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#total()" class="member-name-link">total</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#type()" class="member-name-link">type</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#width()" class="member-name-link">width</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#zeros(int%5B%5D,int)" class="member-name-link">zeros</a><wbr>(int[]&nbsp;sizes,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#zeros(int,int,int)" class="member-name-link">zeros</a><wbr>(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#zeros(org.opencv.core.Size,int)" class="member-name-link">zeros</a><wbr>(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="nativeObj">
<h3>nativeObj</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">nativeObj</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(long)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,int)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,int,java.nio.ByteBuffer)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="class or interface in java.nio" class="external-link">ByteBuffer</a>&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,int,java.nio.ByteBuffer,long)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="class or interface in java.nio" class="external-link">ByteBuffer</a>&nbsp;data,
 long&nbsp;step)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,int)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int[],int)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int[]&nbsp;sizes,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,int,org.opencv.core.Scalar)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,int,org.opencv.core.Scalar)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int[],int,org.opencv.core.Scalar)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(int[]&nbsp;sizes,
 int&nbsp;type,
 <a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,org.opencv.core.Range,org.opencv.core.Range)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,org.opencv.core.Range)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,org.opencv.core.Range[])">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Range.html" title="class in org.opencv.core">Range</a>[]&nbsp;ranges)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,org.opencv.core.Rect)">
<h3>Mat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Mat</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="adjustROI(int,int,int,int)">
<h3>adjustROI</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">adjustROI</span><wbr><span class="parameters">(int&nbsp;dtop,
 int&nbsp;dbottom,
 int&nbsp;dleft,
 int&nbsp;dright)</span></div>
</section>
</li>
<li>
<section class="detail" id="assignTo(org.opencv.core.Mat,int)">
<h3>assignTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">assignTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="assignTo(org.opencv.core.Mat)">
<h3>assignTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">assignTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="channels()">
<h3>channels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">channels</span>()</div>
</section>
</li>
<li>
<section class="detail" id="checkVector(int,int,boolean)">
<h3>checkVector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">checkVector</span><wbr><span class="parameters">(int&nbsp;elemChannels,
 int&nbsp;depth,
 boolean&nbsp;requireContinuous)</span></div>
</section>
</li>
<li>
<section class="detail" id="checkVector(int,int)">
<h3>checkVector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">checkVector</span><wbr><span class="parameters">(int&nbsp;elemChannels,
 int&nbsp;depth)</span></div>
</section>
</li>
<li>
<section class="detail" id="checkVector(int)">
<h3>checkVector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">checkVector</span><wbr><span class="parameters">(int&nbsp;elemChannels)</span></div>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">clone</span>()</div>
</section>
</li>
<li>
<section class="detail" id="col(int)">
<h3>col</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">col</span><wbr><span class="parameters">(int&nbsp;x)</span></div>
</section>
</li>
<li>
<section class="detail" id="colRange(int,int)">
<h3>colRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">colRange</span><wbr><span class="parameters">(int&nbsp;startcol,
 int&nbsp;endcol)</span></div>
</section>
</li>
<li>
<section class="detail" id="colRange(org.opencv.core.Range)">
<h3>colRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">colRange</span><wbr><span class="parameters">(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</span></div>
</section>
</li>
<li>
<section class="detail" id="dims()">
<h3>dims</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">dims</span>()</div>
</section>
</li>
<li>
<section class="detail" id="cols()">
<h3>cols</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">cols</span>()</div>
</section>
</li>
<li>
<section class="detail" id="convertTo(org.opencv.core.Mat,int,double,double)">
<h3>convertTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">convertTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype,
 double&nbsp;alpha,
 double&nbsp;beta)</span></div>
</section>
</li>
<li>
<section class="detail" id="convertTo(org.opencv.core.Mat,int,double)">
<h3>convertTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">convertTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype,
 double&nbsp;alpha)</span></div>
</section>
</li>
<li>
<section class="detail" id="convertTo(org.opencv.core.Mat,int)">
<h3>convertTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">convertTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 int&nbsp;rtype)</span></div>
</section>
</li>
<li>
<section class="detail" id="copyTo(org.opencv.core.Mat)">
<h3>copyTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="copyTo(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>copyTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Size,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int[],int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int[]&nbsp;sizes,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="copySize(org.opencv.core.Mat)">
<h3>copySize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copySize</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="cross(org.opencv.core.Mat)">
<h3>cross</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">cross</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="dataAddr()">
<h3>dataAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">dataAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="depth()">
<h3>depth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">depth</span>()</div>
</section>
</li>
<li>
<section class="detail" id="diag(int)">
<h3>diag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">diag</span><wbr><span class="parameters">(int&nbsp;d)</span></div>
</section>
</li>
<li>
<section class="detail" id="diag()">
<h3>diag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">diag</span>()</div>
</section>
</li>
<li>
<section class="detail" id="diag(org.opencv.core.Mat)">
<h3>diag</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">diag</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;d)</span></div>
</section>
</li>
<li>
<section class="detail" id="dot(org.opencv.core.Mat)">
<h3>dot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">dot</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="elemSize()">
<h3>elemSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">elemSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="elemSize1()">
<h3>elemSize1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">elemSize1</span>()</div>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">empty</span>()</div>
</section>
</li>
<li>
<section class="detail" id="eye(int,int,int)">
<h3>eye</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">eye</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="eye(org.opencv.core.Size,int)">
<h3>eye</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">eye</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="inv(int)">
<h3>inv</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">inv</span><wbr><span class="parameters">(int&nbsp;method)</span></div>
</section>
</li>
<li>
<section class="detail" id="inv()">
<h3>inv</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">inv</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isContinuous()">
<h3>isContinuous</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isContinuous</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isSubmatrix()">
<h3>isSubmatrix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSubmatrix</span>()</div>
</section>
</li>
<li>
<section class="detail" id="locateROI(org.opencv.core.Size,org.opencv.core.Point)">
<h3>locateROI</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">locateROI</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;wholeSize,
 <a href="Point.html" title="class in org.opencv.core">Point</a>&nbsp;ofs)</span></div>
</section>
</li>
<li>
<section class="detail" id="mul(org.opencv.core.Mat,double)">
<h3>mul</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">mul</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
 double&nbsp;scale)</span></div>
<div class="block">Element-wise multiplication with scale factor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>m</code> - operand with with which to perform element-wise multiplication</dd>
<dd><code>scale</code> - scale factor</dd>
<dt>Returns:</dt>
<dd>reference to a new Mat object</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="mul(org.opencv.core.Mat)">
<h3>mul</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">mul</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
<div class="block">Element-wise multiplication</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>m</code> - operand with with which to perform element-wise multiplication</dd>
<dt>Returns:</dt>
<dd>reference to a new Mat object</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matMul(org.opencv.core.Mat)">
<h3>matMul</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">matMul</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
<div class="block">Matrix multiplication</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>m</code> - operand with with which to perform matrix multiplication</dd>
<dt>Returns:</dt>
<dd>reference to a new Mat object</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list-long">
<li><a href="Core.html#gemm(org.opencv.core.Mat,org.opencv.core.Mat,double,org.opencv.core.Mat,double,org.opencv.core.Mat,int)"><code>Core.gemm(Mat, Mat, double, Mat, double, Mat, int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ones(int,int,int)">
<h3>ones</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">ones</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="ones(org.opencv.core.Size,int)">
<h3>ones</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">ones</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="ones(int[],int)">
<h3>ones</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">ones</span><wbr><span class="parameters">(int[]&nbsp;sizes,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="push_back(org.opencv.core.Mat)">
<h3>push_back</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">push_back</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
</section>
</li>
<li>
<section class="detail" id="reshape(int,int)">
<h3>reshape</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">reshape</span><wbr><span class="parameters">(int&nbsp;cn,
 int&nbsp;rows)</span></div>
</section>
</li>
<li>
<section class="detail" id="reshape(int)">
<h3>reshape</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">reshape</span><wbr><span class="parameters">(int&nbsp;cn)</span></div>
</section>
</li>
<li>
<section class="detail" id="reshape(int,int[])">
<h3>reshape</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">reshape</span><wbr><span class="parameters">(int&nbsp;cn,
 int[]&nbsp;newshape)</span></div>
</section>
</li>
<li>
<section class="detail" id="row(int)">
<h3>row</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">row</span><wbr><span class="parameters">(int&nbsp;y)</span></div>
</section>
</li>
<li>
<section class="detail" id="rowRange(int,int)">
<h3>rowRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">rowRange</span><wbr><span class="parameters">(int&nbsp;startrow,
 int&nbsp;endrow)</span></div>
</section>
</li>
<li>
<section class="detail" id="rowRange(org.opencv.core.Range)">
<h3>rowRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">rowRange</span><wbr><span class="parameters">(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</span></div>
</section>
</li>
<li>
<section class="detail" id="rows()">
<h3>rows</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">rows</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setTo(org.opencv.core.Scalar)">
<h3>setTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">setTo</span><wbr><span class="parameters">(<a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</span></div>
</section>
</li>
<li>
<section class="detail" id="setTo(org.opencv.core.Scalar,org.opencv.core.Mat)">
<h3>setTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">setTo</span><wbr><span class="parameters">(<a href="Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
</section>
</li>
<li>
<section class="detail" id="setTo(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>setTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">setTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value,
 <a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
</section>
</li>
<li>
<section class="detail" id="setTo(org.opencv.core.Mat)">
<h3>setTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">setTo</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value)</span></div>
</section>
</li>
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">size</span>()</div>
</section>
</li>
<li>
<section class="detail" id="size(int)">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">size</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
</section>
</li>
<li>
<section class="detail" id="step1(int)">
<h3>step1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">step1</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
</section>
</li>
<li>
<section class="detail" id="step1()">
<h3>step1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">step1</span>()</div>
</section>
</li>
<li>
<section class="detail" id="submat(int,int,int,int)">
<h3>submat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">submat</span><wbr><span class="parameters">(int&nbsp;rowStart,
 int&nbsp;rowEnd,
 int&nbsp;colStart,
 int&nbsp;colEnd)</span></div>
</section>
</li>
<li>
<section class="detail" id="submat(org.opencv.core.Range,org.opencv.core.Range)">
<h3>submat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">submat</span><wbr><span class="parameters">(<a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
 <a href="Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</span></div>
</section>
</li>
<li>
<section class="detail" id="submat(org.opencv.core.Range[])">
<h3>submat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">submat</span><wbr><span class="parameters">(<a href="Range.html" title="class in org.opencv.core">Range</a>[]&nbsp;ranges)</span></div>
</section>
</li>
<li>
<section class="detail" id="submat(org.opencv.core.Rect)">
<h3>submat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">submat</span><wbr><span class="parameters">(<a href="Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</span></div>
</section>
</li>
<li>
<section class="detail" id="t()">
<h3>t</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">t</span>()</div>
</section>
</li>
<li>
<section class="detail" id="total()">
<h3>total</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">total</span>()</div>
</section>
</li>
<li>
<section class="detail" id="type()">
<h3>type</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">type</span>()</div>
</section>
</li>
<li>
<section class="detail" id="zeros(int,int,int)">
<h3>zeros</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">zeros</span><wbr><span class="parameters">(int&nbsp;rows,
 int&nbsp;cols,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="zeros(org.opencv.core.Size,int)">
<h3>zeros</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">zeros</span><wbr><span class="parameters">(<a href="Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="zeros(int[],int)">
<h3>zeros</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">zeros</span><wbr><span class="parameters">(int[]&nbsp;sizes,
 int&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dump()">
<h3>dump</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">dump</span>()</div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,double...)">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 double...&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],double...)">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 double...&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,float[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 float[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],float[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 float[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,int[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 int[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],int[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 int[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,short[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 short[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],short[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 short[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,byte[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],byte[])">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 byte[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int,int,byte[],int,int)">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
</section>
</li>
<li>
<section class="detail" id="put(int[],byte[],int,int)">
<h3>put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(int[]&nbsp;idx,
 byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int,byte[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 byte[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[],byte[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx,
 byte[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int,short[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 short[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[],short[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx,
 short[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int,int[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 int[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[],int[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx,
 int[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int,float[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 float[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[],float[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx,
 float[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int,double[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col,
 double[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[],double[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx,
 double[]&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int,int)">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double[]</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;row,
 int&nbsp;col)</span></div>
</section>
</li>
<li>
<section class="detail" id="get(int[])">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double[]</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int[]&nbsp;idx)</span></div>
</section>
</li>
<li>
<section class="detail" id="height()">
<h3>height</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">height</span>()</div>
</section>
</li>
<li>
<section class="detail" id="width()">
<h3>width</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">width</span>()</div>
</section>
</li>
<li>
<section class="detail" id="at(java.lang.Class,int,int)">
<h3>at</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;T&gt;</span>&nbsp;<span class="element-name">at</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz,
 int&nbsp;row,
 int&nbsp;col)</span></div>
</section>
</li>
<li>
<section class="detail" id="at(java.lang.Class,int[])">
<h3>at</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;T&gt;</span>&nbsp;<span class="element-name">at</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz,
 int[]&nbsp;idx)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
