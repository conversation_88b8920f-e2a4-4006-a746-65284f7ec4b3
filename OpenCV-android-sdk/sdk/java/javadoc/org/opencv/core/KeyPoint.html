<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>KeyPoint (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.core, class: KeyPoint">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.core</a></div>
<h1 title="Class KeyPoint" class="title">Class KeyPoint</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.core.KeyPoint</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">KeyPoint</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>float</code></div>
<div class="col-second even-row-color"><code><a href="#angle" class="member-name-link">angle</a></code></div>
<div class="col-last even-row-color">
<div class="block">Computed orientation of the keypoint (-1 if not applicable).</div>
</div>
<div class="col-first odd-row-color"><code>int</code></div>
<div class="col-second odd-row-color"><code><a href="#class_id" class="member-name-link">class_id</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Object ID, that can be used to cluster keypoints by an object they
 belong to.</div>
</div>
<div class="col-first even-row-color"><code>int</code></div>
<div class="col-second even-row-color"><code><a href="#octave" class="member-name-link">octave</a></code></div>
<div class="col-last even-row-color">
<div class="block">Octave (pyramid layer), from which the keypoint has been extracted.</div>
</div>
<div class="col-first odd-row-color"><code><a href="Point.html" title="class in org.opencv.core">Point</a></code></div>
<div class="col-second odd-row-color"><code><a href="#pt" class="member-name-link">pt</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Coordinates of the keypoint.</div>
</div>
<div class="col-first even-row-color"><code>float</code></div>
<div class="col-second even-row-color"><code><a href="#response" class="member-name-link">response</a></code></div>
<div class="col-last even-row-color">
<div class="block">The response, by which the strongest keypoints have been selected.</div>
</div>
<div class="col-first odd-row-color"><code>float</code></div>
<div class="col-second odd-row-color"><code><a href="#size" class="member-name-link">size</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Diameter of the useful keypoint adjacent area.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">KeyPoint</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(float,float,float)" class="member-name-link">KeyPoint</a><wbr>(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(float,float,float,float)" class="member-name-link">KeyPoint</a><wbr>(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(float,float,float,float,float)" class="member-name-link">KeyPoint</a><wbr>(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(float,float,float,float,float,int)" class="member-name-link">KeyPoint</a><wbr>(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response,
 int&nbsp;_octave)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(float,float,float,float,float,int,int)" class="member-name-link">KeyPoint</a><wbr>(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response,
 int&nbsp;_octave,
 int&nbsp;_class_id)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="pt">
<h3>pt</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Point.html" title="class in org.opencv.core">Point</a></span>&nbsp;<span class="element-name">pt</span></div>
<div class="block">Coordinates of the keypoint.</div>
</section>
</li>
<li>
<section class="detail" id="size">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">size</span></div>
<div class="block">Diameter of the useful keypoint adjacent area.</div>
</section>
</li>
<li>
<section class="detail" id="angle">
<h3>angle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">angle</span></div>
<div class="block">Computed orientation of the keypoint (-1 if not applicable).</div>
</section>
</li>
<li>
<section class="detail" id="response">
<h3>response</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">response</span></div>
<div class="block">The response, by which the strongest keypoints have been selected. Can
 be used for further sorting or subsampling.</div>
</section>
</li>
<li>
<section class="detail" id="octave">
<h3>octave</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">octave</span></div>
<div class="block">Octave (pyramid layer), from which the keypoint has been extracted.</div>
</section>
</li>
<li>
<section class="detail" id="class_id">
<h3>class_id</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">class_id</span></div>
<div class="block">Object ID, that can be used to cluster keypoints by an object they
 belong to.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(float,float,float,float,float,int,int)">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span><wbr><span class="parameters">(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response,
 int&nbsp;_octave,
 int&nbsp;_class_id)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(float,float,float,float,float,int)">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span><wbr><span class="parameters">(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response,
 int&nbsp;_octave)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(float,float,float,float,float)">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span><wbr><span class="parameters">(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle,
 float&nbsp;_response)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(float,float,float,float)">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span><wbr><span class="parameters">(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size,
 float&nbsp;_angle)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(float,float,float)">
<h3>KeyPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">KeyPoint</span><wbr><span class="parameters">(float&nbsp;x,
 float&nbsp;y,
 float&nbsp;_size)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
