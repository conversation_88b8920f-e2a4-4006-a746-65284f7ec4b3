<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.core Class Hierarchy (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="tree: package: org.opencv.core">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.core</h1>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.opencv.core.<a href="Algorithm.html" class="type-name-link" title="class in org.opencv.core">Algorithm</a></li>
<li class="circle">org.opencv.core.<a href="Core.html" class="type-name-link" title="class in org.opencv.core">Core</a></li>
<li class="circle">org.opencv.core.<a href="Core.MinMaxLocResult.html" class="type-name-link" title="class in org.opencv.core">Core.MinMaxLocResult</a></li>
<li class="circle">org.opencv.core.<a href="CvType.html" class="type-name-link" title="class in org.opencv.core">CvType</a></li>
<li class="circle">org.opencv.core.<a href="DMatch.html" class="type-name-link" title="class in org.opencv.core">DMatch</a></li>
<li class="circle">org.opencv.core.<a href="KeyPoint.html" class="type-name-link" title="class in org.opencv.core">KeyPoint</a></li>
<li class="circle">org.opencv.core.<a href="Mat.html" class="type-name-link" title="class in org.opencv.core">Mat</a>
<ul>
<li class="circle">org.opencv.core.<a href="MatOfByte.html" class="type-name-link" title="class in org.opencv.core">MatOfByte</a></li>
<li class="circle">org.opencv.core.<a href="MatOfDMatch.html" class="type-name-link" title="class in org.opencv.core">MatOfDMatch</a></li>
<li class="circle">org.opencv.core.<a href="MatOfDouble.html" class="type-name-link" title="class in org.opencv.core">MatOfDouble</a></li>
<li class="circle">org.opencv.core.<a href="MatOfFloat.html" class="type-name-link" title="class in org.opencv.core">MatOfFloat</a></li>
<li class="circle">org.opencv.core.<a href="MatOfFloat4.html" class="type-name-link" title="class in org.opencv.core">MatOfFloat4</a></li>
<li class="circle">org.opencv.core.<a href="MatOfFloat6.html" class="type-name-link" title="class in org.opencv.core">MatOfFloat6</a></li>
<li class="circle">org.opencv.core.<a href="MatOfInt.html" class="type-name-link" title="class in org.opencv.core">MatOfInt</a></li>
<li class="circle">org.opencv.core.<a href="MatOfInt4.html" class="type-name-link" title="class in org.opencv.core">MatOfInt4</a></li>
<li class="circle">org.opencv.core.<a href="MatOfKeyPoint.html" class="type-name-link" title="class in org.opencv.core">MatOfKeyPoint</a></li>
<li class="circle">org.opencv.core.<a href="MatOfPoint.html" class="type-name-link" title="class in org.opencv.core">MatOfPoint</a></li>
<li class="circle">org.opencv.core.<a href="MatOfPoint2f.html" class="type-name-link" title="class in org.opencv.core">MatOfPoint2f</a></li>
<li class="circle">org.opencv.core.<a href="MatOfPoint3.html" class="type-name-link" title="class in org.opencv.core">MatOfPoint3</a></li>
<li class="circle">org.opencv.core.<a href="MatOfPoint3f.html" class="type-name-link" title="class in org.opencv.core">MatOfPoint3f</a></li>
<li class="circle">org.opencv.core.<a href="MatOfRect.html" class="type-name-link" title="class in org.opencv.core">MatOfRect</a></li>
<li class="circle">org.opencv.core.<a href="MatOfRect2d.html" class="type-name-link" title="class in org.opencv.core">MatOfRect2d</a></li>
<li class="circle">org.opencv.core.<a href="MatOfRotatedRect.html" class="type-name-link" title="class in org.opencv.core">MatOfRotatedRect</a></li>
</ul>
</li>
<li class="circle">org.opencv.core.<a href="Mat.Tuple2.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple2</a>&lt;T&gt;</li>
<li class="circle">org.opencv.core.<a href="Mat.Tuple3.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple3</a>&lt;T&gt;</li>
<li class="circle">org.opencv.core.<a href="Mat.Tuple4.html" class="type-name-link" title="class in org.opencv.core">Mat.Tuple4</a>&lt;T&gt;</li>
<li class="circle">org.opencv.core.<a href="Point.html" class="type-name-link" title="class in org.opencv.core">Point</a></li>
<li class="circle">org.opencv.core.<a href="Point3.html" class="type-name-link" title="class in org.opencv.core">Point3</a></li>
<li class="circle">org.opencv.core.<a href="Range.html" class="type-name-link" title="class in org.opencv.core">Range</a></li>
<li class="circle">org.opencv.core.<a href="Rect.html" class="type-name-link" title="class in org.opencv.core">Rect</a></li>
<li class="circle">org.opencv.core.<a href="Rect2d.html" class="type-name-link" title="class in org.opencv.core">Rect2d</a></li>
<li class="circle">org.opencv.core.<a href="RotatedRect.html" class="type-name-link" title="class in org.opencv.core">RotatedRect</a></li>
<li class="circle">org.opencv.core.<a href="Scalar.html" class="type-name-link" title="class in org.opencv.core">Scalar</a></li>
<li class="circle">org.opencv.core.<a href="Size.html" class="type-name-link" title="class in org.opencv.core">Size</a></li>
<li class="circle">org.opencv.core.<a href="TermCriteria.html" class="type-name-link" title="class in org.opencv.core">TermCriteria</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">org.opencv.core.<a href="CvException.html" class="type-name-link" title="class in org.opencv.core">CvException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.opencv.core.<a href="TickMeter.html" class="type-name-link" title="class in org.opencv.core">TickMeter</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.opencv.core.<a href="Mat.Atable.html" class="type-name-link" title="interface in org.opencv.core">Mat.Atable</a>&lt;T&gt;</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
