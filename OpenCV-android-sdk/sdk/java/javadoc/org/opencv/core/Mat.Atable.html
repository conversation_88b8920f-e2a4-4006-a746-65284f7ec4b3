<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Mat.Atable (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.core, class: Mat, interface: Atable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.core</a></div>
<h1 title="Interface Mat.Atable" class="title">Interface Mat.Atable&lt;T&gt;</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><a href="Mat.html" title="class in org.opencv.core">Mat</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static interface </span><span class="element-name type-name-label">Mat.Atable&lt;T&gt;</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getV()" class="member-name-link">getV</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getV2c()" class="member-name-link">getV2c</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getV3c()" class="member-name-link">getV3c</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getV4c()" class="member-name-link">getV4c</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setV(T)" class="member-name-link">setV</a><wbr>(<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&nbsp;v)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setV2c(org.opencv.core.Mat.Tuple2)" class="member-name-link">setV2c</a><wbr>(<a href="Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setV3c(org.opencv.core.Mat.Tuple3)" class="member-name-link">setV3c</a><wbr>(<a href="Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setV4c(org.opencv.core.Mat.Tuple4)" class="member-name-link">setV4c</a><wbr>(<a href="Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getV()">
<h3>getV</h3>
<div class="member-signature"><span class="return-type"><a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a></span>&nbsp;<span class="element-name">getV</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setV(T)">
<h3 id="setV(java.lang.Object)">setV</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setV</span><wbr><span class="parameters">(<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&nbsp;v)</span></div>
</section>
</li>
<li>
<section class="detail" id="getV2c()">
<h3>getV2c</h3>
<div class="member-signature"><span class="return-type"><a href="Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</span>&nbsp;<span class="element-name">getV2c</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setV2c(org.opencv.core.Mat.Tuple2)">
<h3>setV2c</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setV2c</span><wbr><span class="parameters">(<a href="Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</span></div>
</section>
</li>
<li>
<section class="detail" id="getV3c()">
<h3>getV3c</h3>
<div class="member-signature"><span class="return-type"><a href="Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</span>&nbsp;<span class="element-name">getV3c</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setV3c(org.opencv.core.Mat.Tuple3)">
<h3>setV3c</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setV3c</span><wbr><span class="parameters">(<a href="Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</span></div>
</section>
</li>
<li>
<section class="detail" id="getV4c()">
<h3>getV4c</h3>
<div class="member-signature"><span class="return-type"><a href="Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</span>&nbsp;<span class="element-name">getV4c</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setV4c(org.opencv.core.Mat.Tuple4)">
<h3>setV4c</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setV4c</span><wbr><span class="parameters">(<a href="Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
