<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Net (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: Net">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class Net" class="title">Class Net</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.dnn.Net</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Net</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">This class allows to create and manipulate comprehensive artificial neural networks.

 Neural network is presented as directed acyclic graph (DAG), where vertices are Layer instances,
 and edges specify relationships between layers inputs and outputs.

 Each network layer has unique integer id and unique string name inside its network.
 LayerId can store either layer name or layer id.

 This class supports reference counting of its instances, i. e. copies point to the same instance.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Net</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#connect(java.lang.String,java.lang.String)" class="member-name-link">connect</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outPin,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inpPin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Connects output of the first layer to input of the second layer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dump()" class="member-name-link">dump</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Dump net to String</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dumpToFile(java.lang.String)" class="member-name-link">dumpToFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to dot file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dumpToPbtxt(java.lang.String)" class="member-name-link">dumpToPbtxt</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to pbtxt file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if there are no layers in the network.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enableFusion(boolean)" class="member-name-link">enableFusion</a><wbr>(boolean&nbsp;fusion)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables or disables layer fusion in the network.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enableWinograd(boolean)" class="member-name-link">enableWinograd</a><wbr>(boolean&nbsp;useWinograd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables or disables the Winograd compute branch.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forward()" class="member-name-link">forward</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forward(java.lang.String)" class="member-name-link">forward</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forward(java.util.List)" class="member-name-link">forward</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forward(java.util.List,java.lang.String)" class="member-name-link">forward</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forward(java.util.List,java.util.List)" class="member-name-link">forward</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;outBlobNames)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs forward pass to compute outputs of layers listed in <code>outBlobNames</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFLOPS(int,java.util.List)" class="member-name-link">getFLOPS</a><wbr>(int&nbsp;layerId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFLOPS(int,org.opencv.core.MatOfInt)" class="member-name-link">getFLOPS</a><wbr>(int&nbsp;layerId,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFLOPS(java.util.List)" class="member-name-link">getFLOPS</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes FLOP for whole loaded model with specified input shapes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFLOPS(org.opencv.core.MatOfInt)" class="member-name-link">getFLOPS</a><wbr>(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputDetails(org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt)" class="member-name-link">getInputDetails</a><wbr>(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns input scale and zeropoint for a quantized Net.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Layer.html" title="class in org.opencv.dnn">Layer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayer(int)" class="member-name-link">getLayer</a><wbr>(int&nbsp;layerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns pointer to layer with specified id or name which the network use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayerId(java.lang.String)" class="member-name-link">getLayerId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Converts string name of the layer to the integer identifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayerNames()" class="member-name-link">getLayerNames</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayersCount(java.lang.String)" class="member-name-link">getLayersCount</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns count of layers of specified type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayerTypes(java.util.List)" class="member-name-link">getLayerTypes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;layersTypes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns list of types for layer used in model.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMemoryConsumption(int,java.util.List,long%5B%5D,long%5B%5D)" class="member-name-link">getMemoryConsumption</a><wbr>(int&nbsp;layerId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMemoryConsumption(int,org.opencv.core.MatOfInt,long%5B%5D,long%5B%5D)" class="member-name-link">getMemoryConsumption</a><wbr>(int&nbsp;layerId,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMemoryConsumption(org.opencv.core.MatOfInt,long%5B%5D,long%5B%5D)" class="member-name-link">getMemoryConsumption</a><wbr>(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputDetails(org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt)" class="member-name-link">getOutputDetails</a><wbr>(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns output scale and zeropoint for a quantized Net.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParam(int)" class="member-name-link">getParam</a><wbr>(int&nbsp;layer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns parameter blob of the layer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParam(int,int)" class="member-name-link">getParam</a><wbr>(int&nbsp;layer,
 int&nbsp;numParam)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns parameter blob of the layer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParam(java.lang.String)" class="member-name-link">getParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParam(java.lang.String,int)" class="member-name-link">getParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName,
 int&nbsp;numParam)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPerfProfile(org.opencv.core.MatOfDouble)" class="member-name-link">getPerfProfile</a><wbr>(<a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;timings)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns overall time for inference and timings (in ticks) for layers.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUnconnectedOutLayers()" class="member-name-link">getUnconnectedOutLayers</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns indexes of layers with unconnected outputs.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUnconnectedOutLayersNames()" class="member-name-link">getUnconnectedOutLayersNames</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns names of layers with unconnected outputs.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#quantize(java.util.List,int,int)" class="member-name-link">quantize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
 int&nbsp;inputsDtype,
 int&nbsp;outputsDtype)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a quantized Net from a floating-point Net.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#quantize(java.util.List,int,int,boolean)" class="member-name-link">quantize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
 int&nbsp;inputsDtype,
 int&nbsp;outputsDtype,
 boolean&nbsp;perChannel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a quantized Net from a floating-point Net.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readFromModelOptimizer(java.lang.String,java.lang.String)" class="member-name-link">readFromModelOptimizer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;bin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create a network from Intel's Model Optimizer intermediate representation (IR).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readFromModelOptimizer(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readFromModelOptimizer</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create a network from Intel's Model Optimizer in-memory buffers with intermediate representation (IR).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHalideScheduler(java.lang.String)" class="member-name-link">setHalideScheduler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scheduler)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Compile Halide layers.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(org.opencv.core.Mat)" class="member-name-link">setInput</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the new input value for the network</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(org.opencv.core.Mat,java.lang.String)" class="member-name-link">setInput</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the new input value for the network</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(org.opencv.core.Mat,java.lang.String,double)" class="member-name-link">setInput</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 double&nbsp;scalefactor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the new input value for the network</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(org.opencv.core.Mat,java.lang.String,double,org.opencv.core.Scalar)" class="member-name-link">setInput</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 double&nbsp;scalefactor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the new input value for the network</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputShape(java.lang.String,org.opencv.core.MatOfInt)" class="member-name-link">setInputShape</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputName,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;shape)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify shape of network input.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputsNames(java.util.List)" class="member-name-link">setInputsNames</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;inputBlobNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets outputs names of the network input pseudo layer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParam(int,int,org.opencv.core.Mat)" class="member-name-link">setParam</a><wbr>(int&nbsp;layer,
 int&nbsp;numParam,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the new value for the learned param of the layer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParam(java.lang.String,int,org.opencv.core.Mat)" class="member-name-link">setParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName,
 int&nbsp;numParam,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreferableBackend(int)" class="member-name-link">setPreferableBackend</a><wbr>(int&nbsp;backendId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ask network to use specific computation backend where it supported.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreferableTarget(int)" class="member-name-link">setPreferableTarget</a><wbr>(int&nbsp;targetId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ask network to make computations on specific target device.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Net</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Net</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="readFromModelOptimizer(java.lang.String,java.lang.String)">
<h3>readFromModelOptimizer</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readFromModelOptimizer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;bin)</span></div>
<div class="block">Create a network from Intel's Model Optimizer intermediate representation (IR).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xml</code> - XML configuration file with network's topology.</dd>
<dd><code>bin</code> - Binary file with trained weights.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readFromModelOptimizer(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readFromModelOptimizer</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readFromModelOptimizer</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</span></div>
<div class="block">Create a network from Intel's Model Optimizer in-memory buffers with intermediate representation (IR).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferModelConfig</code> - buffer with model's configuration.</dd>
<dd><code>bufferWeights</code> - buffer with model's trained weights.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">empty</span>()</div>
<div class="block">Returns true if there are no layers in the network.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dump()">
<h3>dump</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">dump</span>()</div>
<div class="block">Dump net to String</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String with structure, hyperparameters, backend, target and fusion
 Call method after setInput(). To see correct backend, target and fusion run after forward().</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dumpToFile(java.lang.String)">
<h3>dumpToFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dumpToFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to dot file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path to output file with .dot extension
 SEE: dump()</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dumpToPbtxt(java.lang.String)">
<h3>dumpToPbtxt</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dumpToPbtxt</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to pbtxt file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path to output file with .pbtxt extension

 Use Netron (https://netron.app) to open the target file to visualize the model.
 Call method after setInput(). To see correct backend, target and fusion run after forward().</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLayerId(java.lang.String)">
<h3>getLayerId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getLayerId</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layer)</span></div>
<div class="block">Converts string name of the layer to the integer identifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layer</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>id of the layer, or -1 if the layer wasn't found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLayerNames()">
<h3>getLayerNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getLayerNames</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getLayer(int)">
<h3>getLayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Layer.html" title="class in org.opencv.dnn">Layer</a></span>&nbsp;<span class="element-name">getLayer</span><wbr><span class="parameters">(int&nbsp;layerId)</span></div>
<div class="block">Returns pointer to layer with specified id or name which the network use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layerId</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="connect(java.lang.String,java.lang.String)">
<h3>connect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">connect</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outPin,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inpPin)</span></div>
<div class="block">Connects output of the first layer to input of the second layer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outPin</code> - descriptor of the first layer output.</dd>
<dd><code>inpPin</code> - descriptor of the second layer input.

 Descriptors have the following template &lt;DFN&gt;&amp;lt;layer_name&amp;gt;[.input_number]&lt;/DFN&gt;:
 - the first part of the template &lt;DFN&gt;layer_name&lt;/DFN&gt; is string name of the added layer.
 If this part is empty then the network input pseudo layer will be used;
 - the second optional part of the template &lt;DFN&gt;input_number&lt;/DFN&gt;
 is either number of the layer input, either label one.
 If this part is omitted then the first layer input will be used.

 SEE: setNetInputs(), Layer::inputNameToIndex(), Layer::outputNameToIndex()</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputsNames(java.util.List)">
<h3>setInputsNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputsNames</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;inputBlobNames)</span></div>
<div class="block">Sets outputs names of the network input pseudo layer.

 Each net always has special own the network input pseudo layer with id=0.
 This layer stores the user blobs only and don't make any computations.
 In fact, this layer provides the only way to pass user data into the network.
 As any other layer, this layer can label its outputs and this function provides an easy way to do this.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputBlobNames</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputShape(java.lang.String,org.opencv.core.MatOfInt)">
<h3>setInputShape</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputShape</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputName,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;shape)</span></div>
<div class="block">Specify shape of network input.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputName</code> - automatically generated</dd>
<dd><code>shape</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forward(java.lang.String)">
<h3>forward</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">forward</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputName)</span></div>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputName</code> - name for layer which output is needed to get</dd>
<dt>Returns:</dt>
<dd>blob for first output of specified layer.
 By default runs forward pass for the whole network.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forward()">
<h3>forward</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">forward</span>()</div>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>blob for first output of specified layer.
 By default runs forward pass for the whole network.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forward(java.util.List,java.lang.String)">
<h3>forward</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">forward</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputName)</span></div>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputBlobs</code> - contains all output blobs for specified layer.</dd>
<dd><code>outputName</code> - name for layer which output is needed to get
 If <code>outputName</code> is empty, runs forward pass for the whole network.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forward(java.util.List)">
<h3>forward</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">forward</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs)</span></div>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputBlobs</code> - contains all output blobs for specified layer.
 If <code>outputName</code> is empty, runs forward pass for the whole network.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forward(java.util.List,java.util.List)">
<h3>forward</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">forward</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;outBlobNames)</span></div>
<div class="block">Runs forward pass to compute outputs of layers listed in <code>outBlobNames</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputBlobs</code> - contains blobs for first outputs of specified layers.</dd>
<dd><code>outBlobNames</code> - names for layers which outputs are needed to get</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="quantize(java.util.List,int,int,boolean)">
<h3>quantize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">quantize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
 int&nbsp;inputsDtype,
 int&nbsp;outputsDtype,
 boolean&nbsp;perChannel)</span></div>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calibData</code> - Calibration data to compute the quantization parameters.</dd>
<dd><code>inputsDtype</code> - Datatype of quantized net's inputs. Can be CV_32F or CV_8S.</dd>
<dd><code>outputsDtype</code> - Datatype of quantized net's outputs. Can be CV_32F or CV_8S.</dd>
<dd><code>perChannel</code> - Quantization granularity of quantized Net. The default is true, that means quantize model
 in per-channel way (channel-wise). Set it false to quantize model in per-tensor way (or tensor-wise).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="quantize(java.util.List,int,int)">
<h3>quantize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">quantize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
 int&nbsp;inputsDtype,
 int&nbsp;outputsDtype)</span></div>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calibData</code> - Calibration data to compute the quantization parameters.</dd>
<dd><code>inputsDtype</code> - Datatype of quantized net's inputs. Can be CV_32F or CV_8S.</dd>
<dd><code>outputsDtype</code> - Datatype of quantized net's outputs. Can be CV_32F or CV_8S.
 in per-channel way (channel-wise). Set it false to quantize model in per-tensor way (or tensor-wise).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInputDetails(org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt)">
<h3>getInputDetails</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getInputDetails</span><wbr><span class="parameters">(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</span></div>
<div class="block">Returns input scale and zeropoint for a quantized Net.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scales</code> - output parameter for returning input scales.</dd>
<dd><code>zeropoints</code> - output parameter for returning input zeropoints.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputDetails(org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt)">
<h3>getOutputDetails</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getOutputDetails</span><wbr><span class="parameters">(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</span></div>
<div class="block">Returns output scale and zeropoint for a quantized Net.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scales</code> - output parameter for returning output scales.</dd>
<dd><code>zeropoints</code> - output parameter for returning output zeropoints.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHalideScheduler(java.lang.String)">
<h3>setHalideScheduler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHalideScheduler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scheduler)</span></div>
<div class="block">Compile Halide layers.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scheduler</code> - Path to YAML file with scheduling directives.
 SEE: setPreferableBackend

 Schedule layers that support Halide backend. Then compile them for
 specific target. For layers that not represented in scheduling file
 or if no manual scheduling used at all, automatic scheduling will be applied.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreferableBackend(int)">
<h3>setPreferableBackend</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreferableBackend</span><wbr><span class="parameters">(int&nbsp;backendId)</span></div>
<div class="block">Ask network to use specific computation backend where it supported.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>backendId</code> - backend identifier.
 SEE: Backend</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreferableTarget(int)">
<h3>setPreferableTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreferableTarget</span><wbr><span class="parameters">(int&nbsp;targetId)</span></div>
<div class="block">Ask network to make computations on specific target device.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetId</code> - target identifier.
 SEE: Target

 List of supported combinations backend / target:
 |                        | DNN_BACKEND_OPENCV | DNN_BACKEND_INFERENCE_ENGINE | DNN_BACKEND_HALIDE |  DNN_BACKEND_CUDA |
 |------------------------|--------------------|------------------------------|--------------------|-------------------|
 | DNN_TARGET_CPU         |                  + |                            + |                  + |                   |
 | DNN_TARGET_OPENCL      |                  + |                            + |                  + |                   |
 | DNN_TARGET_OPENCL_FP16 |                  + |                            + |                    |                   |
 | DNN_TARGET_MYRIAD      |                    |                            + |                    |                   |
 | DNN_TARGET_FPGA        |                    |                            + |                    |                   |
 | DNN_TARGET_CUDA        |                    |                              |                    |                 + |
 | DNN_TARGET_CUDA_FP16   |                    |                              |                    |                 + |
 | DNN_TARGET_HDDL        |                    |                            + |                    |                   |</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInput(org.opencv.core.Mat,java.lang.String,double,org.opencv.core.Scalar)">
<h3>setInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 double&nbsp;scalefactor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
<div class="block">Sets the new input value for the network</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.</dd>
<dd><code>scalefactor</code> - An optional normalization scale.</dd>
<dd><code>mean</code> - An optional mean subtraction values.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInput(org.opencv.core.Mat,java.lang.String,double)">
<h3>setInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 double&nbsp;scalefactor)</span></div>
<div class="block">Sets the new input value for the network</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.</dd>
<dd><code>scalefactor</code> - An optional normalization scale.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInput(org.opencv.core.Mat,java.lang.String)">
<h3>setInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Sets the new input value for the network</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInput(org.opencv.core.Mat)">
<h3>setInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</span></div>
<div class="block">Sets the new input value for the network</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParam(int,int,org.opencv.core.Mat)">
<h3>setParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParam</span><wbr><span class="parameters">(int&nbsp;layer,
 int&nbsp;numParam,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</span></div>
<div class="block">Sets the new value for the learned param of the layer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layer</code> - name or id of the layer.</dd>
<dd><code>numParam</code> - index of the layer parameter in the Layer::blobs array.</dd>
<dd><code>blob</code> - the new value.
 SEE: Layer::blobs
 <b>Note:</b> If shape of the new blob differs from the previous shape,
 then the following forward pass may fail.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParam(java.lang.String,int,org.opencv.core.Mat)">
<h3>setParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName,
 int&nbsp;numParam,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</span></div>
</section>
</li>
<li>
<section class="detail" id="getParam(int,int)">
<h3>getParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getParam</span><wbr><span class="parameters">(int&nbsp;layer,
 int&nbsp;numParam)</span></div>
<div class="block">Returns parameter blob of the layer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layer</code> - name or id of the layer.</dd>
<dd><code>numParam</code> - index of the layer parameter in the Layer::blobs array.
 SEE: Layer::blobs</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParam(int)">
<h3>getParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getParam</span><wbr><span class="parameters">(int&nbsp;layer)</span></div>
<div class="block">Returns parameter blob of the layer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layer</code> - name or id of the layer.
 SEE: Layer::blobs</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParam(java.lang.String,int)">
<h3>getParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName,
 int&nbsp;numParam)</span></div>
</section>
</li>
<li>
<section class="detail" id="getParam(java.lang.String)">
<h3>getParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerName)</span></div>
</section>
</li>
<li>
<section class="detail" id="getUnconnectedOutLayers()">
<h3>getUnconnectedOutLayers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></span>&nbsp;<span class="element-name">getUnconnectedOutLayers</span>()</div>
<div class="block">Returns indexes of layers with unconnected outputs.

 FIXIT: Rework API to registerOutput() approach, deprecate this call</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUnconnectedOutLayersNames()">
<h3>getUnconnectedOutLayersNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getUnconnectedOutLayersNames</span>()</div>
<div class="block">Returns names of layers with unconnected outputs.

 FIXIT: Rework API to registerOutput() approach, deprecate this call</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFLOPS(java.util.List)">
<h3>getFLOPS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getFLOPS</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</span></div>
<div class="block">Computes FLOP for whole loaded model with specified input shapes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>netInputShapes</code> - vector of shapes for all net inputs.</dd>
<dt>Returns:</dt>
<dd>computed FLOP.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFLOPS(org.opencv.core.MatOfInt)">
<h3>getFLOPS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getFLOPS</span><wbr><span class="parameters">(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFLOPS(int,java.util.List)">
<h3>getFLOPS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getFLOPS</span><wbr><span class="parameters">(int&nbsp;layerId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFLOPS(int,org.opencv.core.MatOfInt)">
<h3>getFLOPS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getFLOPS</span><wbr><span class="parameters">(int&nbsp;layerId,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</span></div>
</section>
</li>
<li>
<section class="detail" id="getLayerTypes(java.util.List)">
<h3>getLayerTypes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getLayerTypes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;layersTypes)</span></div>
<div class="block">Returns list of types for layer used in model.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layersTypes</code> - output parameter for returning types.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLayersCount(java.lang.String)">
<h3>getLayersCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getLayersCount</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layerType)</span></div>
<div class="block">Returns count of layers of specified type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layerType</code> - type.</dd>
<dt>Returns:</dt>
<dd>count of layers</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMemoryConsumption(org.opencv.core.MatOfInt,long[],long[])">
<h3>getMemoryConsumption</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getMemoryConsumption</span><wbr><span class="parameters">(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMemoryConsumption(int,java.util.List,long[],long[])">
<h3>getMemoryConsumption</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getMemoryConsumption</span><wbr><span class="parameters">(int&nbsp;layerId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMemoryConsumption(int,org.opencv.core.MatOfInt,long[],long[])">
<h3>getMemoryConsumption</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getMemoryConsumption</span><wbr><span class="parameters">(int&nbsp;layerId,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
 long[]&nbsp;weights,
 long[]&nbsp;blobs)</span></div>
</section>
</li>
<li>
<section class="detail" id="enableFusion(boolean)">
<h3>enableFusion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">enableFusion</span><wbr><span class="parameters">(boolean&nbsp;fusion)</span></div>
<div class="block">Enables or disables layer fusion in the network.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fusion</code> - true to enable the fusion, false to disable. The fusion is enabled by default.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="enableWinograd(boolean)">
<h3>enableWinograd</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">enableWinograd</span><wbr><span class="parameters">(boolean&nbsp;useWinograd)</span></div>
<div class="block">Enables or disables the Winograd compute branch. The Winograd compute branch can speed up
 3x3 Convolution at a small loss of accuracy.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>useWinograd</code> - true to enable the Winograd compute branch. The default is true.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPerfProfile(org.opencv.core.MatOfDouble)">
<h3>getPerfProfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getPerfProfile</span><wbr><span class="parameters">(<a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;timings)</span></div>
<div class="block">Returns overall time for inference and timings (in ticks) for layers.

 Indexes in returned vector correspond to layers ids. Some layers can be fused with others,
 in this case zero ticks count will be return for that skipped layers. Supported by DNN_BACKEND_OPENCV on DNN_TARGET_CPU only.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timings</code> - vector for tick timings for all layers.</dd>
<dt>Returns:</dt>
<dd>overall ticks for model inference.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
