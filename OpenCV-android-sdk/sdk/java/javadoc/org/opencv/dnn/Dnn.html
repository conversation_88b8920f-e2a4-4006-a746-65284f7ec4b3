<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Dnn (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: Dnn">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class Dnn" class="title">Class Dnn</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.dnn.Dnn</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Dnn</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_BACKEND_CANN" class="member-name-link">DNN_BACKEND_CANN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_BACKEND_CUDA" class="member-name-link">DNN_BACKEND_CUDA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_BACKEND_DEFAULT" class="member-name-link">DNN_BACKEND_DEFAULT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_BACKEND_HALIDE" class="member-name-link">DNN_BACKEND_HALIDE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_BACKEND_INFERENCE_ENGINE" class="member-name-link">DNN_BACKEND_INFERENCE_ENGINE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_BACKEND_OPENCV" class="member-name-link">DNN_BACKEND_OPENCV</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_BACKEND_TIMVX" class="member-name-link">DNN_BACKEND_TIMVX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_BACKEND_VKCOM" class="member-name-link">DNN_BACKEND_VKCOM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_BACKEND_WEBNN" class="member-name-link">DNN_BACKEND_WEBNN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_LAYOUT_NCDHW" class="member-name-link">DNN_LAYOUT_NCDHW</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_LAYOUT_NCHW" class="member-name-link">DNN_LAYOUT_NCHW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_LAYOUT_ND" class="member-name-link">DNN_LAYOUT_ND</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_LAYOUT_NDHWC" class="member-name-link">DNN_LAYOUT_NDHWC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_LAYOUT_NHWC" class="member-name-link">DNN_LAYOUT_NHWC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_LAYOUT_PLANAR" class="member-name-link">DNN_LAYOUT_PLANAR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_LAYOUT_UNKNOWN" class="member-name-link">DNN_LAYOUT_UNKNOWN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_PMODE_CROP_CENTER" class="member-name-link">DNN_PMODE_CROP_CENTER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_PMODE_LETTERBOX" class="member-name-link">DNN_PMODE_LETTERBOX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_PMODE_NULL" class="member-name-link">DNN_PMODE_NULL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_CPU" class="member-name-link">DNN_TARGET_CPU</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_TARGET_CPU_FP16" class="member-name-link">DNN_TARGET_CPU_FP16</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_CUDA" class="member-name-link">DNN_TARGET_CUDA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_TARGET_CUDA_FP16" class="member-name-link">DNN_TARGET_CUDA_FP16</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_FPGA" class="member-name-link">DNN_TARGET_FPGA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_TARGET_HDDL" class="member-name-link">DNN_TARGET_HDDL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_MYRIAD" class="member-name-link">DNN_TARGET_MYRIAD</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_TARGET_NPU" class="member-name-link">DNN_TARGET_NPU</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_OPENCL" class="member-name-link">DNN_TARGET_OPENCL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DNN_TARGET_OPENCL_FP16" class="member-name-link">DNN_TARGET_OPENCL_FP16</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DNN_TARGET_VULKAN" class="member-name-link">DNN_TARGET_VULKAN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SoftNMSMethod_SOFTNMS_GAUSSIAN" class="member-name-link">SoftNMSMethod_SOFTNMS_GAUSSIAN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SoftNMSMethod_SOFTNMS_LINEAR" class="member-name-link">SoftNMSMethod_SOFTNMS_LINEAR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Dnn</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean,int)" class="member-name-link">blobFromImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop,
 int&nbsp;ddepth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double,org.opencv.core.Size)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean,int)" class="member-name-link">blobFromImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop,
 int&nbsp;ddepth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImagesWithParams(java.util.List)" class="member-name-link">blobFromImagesWithParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images with given params.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImagesWithParams(java.util.List,org.opencv.core.Mat)" class="member-name-link">blobFromImagesWithParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImagesWithParams(java.util.List,org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)" class="member-name-link">blobFromImagesWithParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImagesWithParams(java.util.List,org.opencv.dnn.Image2BlobParams)" class="member-name-link">blobFromImagesWithParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from series of images with given params.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImageWithParams(org.opencv.core.Mat)" class="member-name-link">blobFromImageWithParams</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image with given params.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImageWithParams(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">blobFromImageWithParams</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImageWithParams(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)" class="member-name-link">blobFromImageWithParams</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#blobFromImageWithParams(org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)" class="member-name-link">blobFromImageWithParams</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates 4-dimensional blob from image with given params.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAvailableTargets(int)" class="member-name-link">getAvailableTargets</a><wbr>(int&nbsp;be)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInferenceEngineCPUType()" class="member-name-link">getInferenceEngineCPUType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns Inference Engine CPU type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInferenceEngineVPUType()" class="member-name-link">getInferenceEngineVPUType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns Inference Engine VPU type.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imagesFromBlob(org.opencv.core.Mat,java.util.List)" class="member-name-link">imagesFromBlob</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob_,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images_)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parse a 4D blob and output the images it contains as 2D arrays through a simpler data structure
 (std::vector&lt;cv::Mat&gt;).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)" class="member-name-link">NMSBoxes</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float)" class="member-name-link">NMSBoxes</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float,int)" class="member-name-link">NMSBoxes</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt)" class="member-name-link">NMSBoxesBatched</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt,float)" class="member-name-link">NMSBoxesBatched</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt,float,int)" class="member-name-link">NMSBoxesBatched</a><wbr>(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)" class="member-name-link">NMSBoxesRotated</a><wbr>(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float)" class="member-name-link">NMSBoxesRotated</a><wbr>(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float,int)" class="member-name-link">NMSBoxesRotated</a><wbr>(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNet(java.lang.String)" class="member-name-link">readNet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNet(java.lang.String,java.lang.String)" class="member-name-link">readNet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNet(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">readNet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNet(java.lang.String,org.opencv.core.MatOfByte)" class="member-name-link">readNet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNet(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readNet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromCaffe(java.lang.String)" class="member-name-link">readNetFromCaffe</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromCaffe(java.lang.String,java.lang.String)" class="member-name-link">readNetFromCaffe</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;caffeModel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromCaffe(org.opencv.core.MatOfByte)" class="member-name-link">readNetFromCaffe</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in Caffe model in memory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromCaffe(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readNetFromCaffe</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in Caffe model in memory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromDarknet(java.lang.String)" class="member-name-link">readNetFromDarknet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cfgFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromDarknet(java.lang.String,java.lang.String)" class="member-name-link">readNetFromDarknet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cfgFile,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;darknetModel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromDarknet(org.opencv.core.MatOfByte)" class="member-name-link">readNetFromDarknet</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromDarknet(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readNetFromDarknet</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromModelOptimizer(java.lang.String)" class="member-name-link">readNetFromModelOptimizer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromModelOptimizer(java.lang.String,java.lang.String)" class="member-name-link">readNetFromModelOptimizer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;bin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromModelOptimizer(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readNetFromModelOptimizer</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromONNX(java.lang.String)" class="member-name-link">readNetFromONNX</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;onnxFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromONNX(org.opencv.core.MatOfByte)" class="member-name-link">readNetFromONNX</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buffer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model from &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;
 in-memory buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTensorflow(java.lang.String)" class="member-name-link">readNetFromTensorflow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTensorflow(java.lang.String,java.lang.String)" class="member-name-link">readNetFromTensorflow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTensorflow(org.opencv.core.MatOfByte)" class="member-name-link">readNetFromTensorflow</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTensorflow(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">readNetFromTensorflow</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTFLite(java.lang.String)" class="member-name-link">readNetFromTFLite</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/lite"&gt;TFLite&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTFLite(org.opencv.core.MatOfByte)" class="member-name-link">readNetFromTFLite</a><wbr>(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/lite"&gt;TFLite&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTorch(java.lang.String)" class="member-name-link">readNetFromTorch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTorch(java.lang.String,boolean)" class="member-name-link">readNetFromTorch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 boolean&nbsp;isBinary)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Net.html" title="class in org.opencv.dnn">Net</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readNetFromTorch(java.lang.String,boolean,boolean)" class="member-name-link">readNetFromTorch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 boolean&nbsp;isBinary,
 boolean&nbsp;evaluate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readTensorFromONNX(java.lang.String)" class="member-name-link">readTensorFromONNX</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates blob from .pb file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readTorchBlob(java.lang.String)" class="member-name-link">readTorchBlob</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readTorchBlob(java.lang.String,boolean)" class="member-name-link">readTorchBlob</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 boolean&nbsp;isBinary)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#releaseHDDLPlugin()" class="member-name-link">releaseHDDLPlugin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Release a HDDL plugin.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#resetMyriadDevice()" class="member-name-link">resetMyriadDevice</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Release a Myriad device (binded by OpenCV).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#shrinkCaffeModel(java.lang.String,java.lang.String)" class="member-name-link">shrinkCaffeModel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#shrinkCaffeModel(java.lang.String,java.lang.String,java.util.List)" class="member-name-link">shrinkCaffeModel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dst,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;layersTypes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)" class="member-name-link">softNMSBoxes</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,long)" class="member-name-link">softNMSBoxes</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 long&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,long,float)" class="member-name-link">softNMSBoxes</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 long&nbsp;top_k,
 float&nbsp;sigma)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#writeTextGraph(java.lang.String,java.lang.String)" class="member-name-link">writeTextGraph</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create a text representation for a binary network stored in protocol buffer format.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DNN_BACKEND_DEFAULT">
<h3>DNN_BACKEND_DEFAULT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_DEFAULT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_DEFAULT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_HALIDE">
<h3>DNN_BACKEND_HALIDE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_HALIDE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_HALIDE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_INFERENCE_ENGINE">
<h3>DNN_BACKEND_INFERENCE_ENGINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_INFERENCE_ENGINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_INFERENCE_ENGINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_OPENCV">
<h3>DNN_BACKEND_OPENCV</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_OPENCV</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_OPENCV">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_VKCOM">
<h3>DNN_BACKEND_VKCOM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_VKCOM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_VKCOM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_CUDA">
<h3>DNN_BACKEND_CUDA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_CUDA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_CUDA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_WEBNN">
<h3>DNN_BACKEND_WEBNN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_WEBNN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_WEBNN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_TIMVX">
<h3>DNN_BACKEND_TIMVX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_TIMVX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_TIMVX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_BACKEND_CANN">
<h3>DNN_BACKEND_CANN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_BACKEND_CANN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_CANN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_UNKNOWN">
<h3>DNN_LAYOUT_UNKNOWN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_UNKNOWN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_UNKNOWN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_ND">
<h3>DNN_LAYOUT_ND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_ND</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_ND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_NCHW">
<h3>DNN_LAYOUT_NCHW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_NCHW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_NCHW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_NCDHW">
<h3>DNN_LAYOUT_NCDHW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_NCDHW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_NCDHW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_NHWC">
<h3>DNN_LAYOUT_NHWC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_NHWC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_NHWC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_NDHWC">
<h3>DNN_LAYOUT_NDHWC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_NDHWC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_NDHWC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_LAYOUT_PLANAR">
<h3>DNN_LAYOUT_PLANAR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_LAYOUT_PLANAR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_LAYOUT_PLANAR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_PMODE_NULL">
<h3>DNN_PMODE_NULL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_PMODE_NULL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_PMODE_NULL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_PMODE_CROP_CENTER">
<h3>DNN_PMODE_CROP_CENTER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_PMODE_CROP_CENTER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_PMODE_CROP_CENTER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_PMODE_LETTERBOX">
<h3>DNN_PMODE_LETTERBOX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_PMODE_LETTERBOX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_PMODE_LETTERBOX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SoftNMSMethod_SOFTNMS_LINEAR">
<h3>SoftNMSMethod_SOFTNMS_LINEAR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SoftNMSMethod_SOFTNMS_LINEAR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_LINEAR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SoftNMSMethod_SOFTNMS_GAUSSIAN">
<h3>SoftNMSMethod_SOFTNMS_GAUSSIAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SoftNMSMethod_SOFTNMS_GAUSSIAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_GAUSSIAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_CPU">
<h3>DNN_TARGET_CPU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_CPU</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CPU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_OPENCL">
<h3>DNN_TARGET_OPENCL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_OPENCL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_OPENCL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_OPENCL_FP16">
<h3>DNN_TARGET_OPENCL_FP16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_OPENCL_FP16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_OPENCL_FP16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_MYRIAD">
<h3>DNN_TARGET_MYRIAD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_MYRIAD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_MYRIAD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_VULKAN">
<h3>DNN_TARGET_VULKAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_VULKAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_VULKAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_FPGA">
<h3>DNN_TARGET_FPGA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_FPGA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_FPGA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_CUDA">
<h3>DNN_TARGET_CUDA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_CUDA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CUDA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_CUDA_FP16">
<h3>DNN_TARGET_CUDA_FP16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_CUDA_FP16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CUDA_FP16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_HDDL">
<h3>DNN_TARGET_HDDL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_HDDL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_HDDL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_NPU">
<h3>DNN_TARGET_NPU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_NPU</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_NPU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DNN_TARGET_CPU_FP16">
<h3>DNN_TARGET_CPU_FP16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DNN_TARGET_CPU_FP16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CPU_FP16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Dnn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Dnn</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getAvailableTargets(int)">
<h3>getAvailableTargets</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">getAvailableTargets</span><wbr><span class="parameters">(int&nbsp;be)</span></div>
</section>
</li>
<li>
<section class="detail" id="readNetFromDarknet(java.lang.String,java.lang.String)">
<h3>readNetFromDarknet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromDarknet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cfgFile,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;darknetModel)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cfgFile</code> - path to the .cfg file with text description of the network architecture.</dd>
<dd><code>darknetModel</code> - path to the .weights file with learned network.</dd>
<dt>Returns:</dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromDarknet(java.lang.String)">
<h3>readNetFromDarknet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromDarknet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cfgFile)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cfgFile</code> - path to the .cfg file with text description of the network architecture.</dd>
<dt>Returns:</dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromDarknet(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readNetFromDarknet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromDarknet</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferCfg</code> - A buffer contains a content of .cfg file with text description of the network architecture.</dd>
<dd><code>bufferModel</code> - A buffer contains a content of .weights file with learned network.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromDarknet(org.opencv.core.MatOfByte)">
<h3>readNetFromDarknet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromDarknet</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferCfg</code> - A buffer contains a content of .cfg file with text description of the network architecture.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromCaffe(java.lang.String,java.lang.String)">
<h3>readNetFromCaffe</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromCaffe</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;caffeModel)</span></div>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prototxt</code> - path to the .prototxt file with text description of the network architecture.</dd>
<dd><code>caffeModel</code> - path to the .caffemodel file with learned network.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromCaffe(java.lang.String)">
<h3>readNetFromCaffe</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromCaffe</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt)</span></div>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prototxt</code> - path to the .prototxt file with text description of the network architecture.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromCaffe(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readNetFromCaffe</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromCaffe</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</span></div>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferProto</code> - buffer containing the content of the .prototxt file</dd>
<dd><code>bufferModel</code> - buffer containing the content of the .caffemodel file</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromCaffe(org.opencv.core.MatOfByte)">
<h3>readNetFromCaffe</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromCaffe</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto)</span></div>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferProto</code> - buffer containing the content of the .prototxt file</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTensorflow(java.lang.String,java.lang.String)">
<h3>readNetFromTensorflow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTensorflow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the .pb file with binary protobuf description of the network architecture</dd>
<dd><code>config</code> - path to the .pbtxt file that contains text graph definition in protobuf format.
 Resulting Net object is built by text graph using weights from a binary one that
 let us make it more flexible.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTensorflow(java.lang.String)">
<h3>readNetFromTensorflow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTensorflow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the .pb file with binary protobuf description of the network architecture
 Resulting Net object is built by text graph using weights from a binary one that
 let us make it more flexible.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTensorflow(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readNetFromTensorflow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTensorflow</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferModel</code> - buffer containing the content of the pb file</dd>
<dd><code>bufferConfig</code> - buffer containing the content of the pbtxt file</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTensorflow(org.opencv.core.MatOfByte)">
<h3>readNetFromTensorflow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTensorflow</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferModel</code> - buffer containing the content of the pb file</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTFLite(java.lang.String)">
<h3>readNetFromTFLite</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTFLite</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/lite"&gt;TFLite&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the .tflite file with binary flatbuffers description of the network architecture</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTFLite(org.opencv.core.MatOfByte)">
<h3>readNetFromTFLite</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTFLite</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</span></div>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/lite"&gt;TFLite&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferModel</code> - buffer containing the content of the tflite file</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTorch(java.lang.String,boolean,boolean)">
<h3>readNetFromTorch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTorch</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 boolean&nbsp;isBinary,
 boolean&nbsp;evaluate)</span></div>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dd><code>isBinary</code> - specifies whether the network was serialized in ascii mode or binary.</dd>
<dd><code>evaluate</code> - specifies testing phase of network. If true, it's similar to evaluate() method in Torch.</dd>
<dt>Returns:</dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTorch(java.lang.String,boolean)">
<h3>readNetFromTorch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTorch</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 boolean&nbsp;isBinary)</span></div>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dd><code>isBinary</code> - specifies whether the network was serialized in ascii mode or binary.</dd>
<dt>Returns:</dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromTorch(java.lang.String)">
<h3>readNetFromTorch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromTorch</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dt>Returns:</dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNet(java.lang.String,java.lang.String,java.lang.String)">
<h3>readNet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework)</span></div>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> | <code>*.onnx</code> (OpenVINO, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)</dd>
<dd><code>config</code> - Text file contains network configuration. It could be a
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (OpenVINO, https://software.intel.com/openvino-toolkit)</dd>
<dd><code>framework</code> - Explicit framework name tag to determine a format.</dd>
<dt>Returns:</dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNet(java.lang.String,java.lang.String)">
<h3>readNet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> | <code>*.onnx</code> (OpenVINO, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)</dd>
<dd><code>config</code> - Text file contains network configuration. It could be a
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (OpenVINO, https://software.intel.com/openvino-toolkit)</dd>
<dt>Returns:</dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNet(java.lang.String)">
<h3>readNet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> | <code>*.onnx</code> (OpenVINO, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (OpenVINO, https://software.intel.com/openvino-toolkit)</dd>
<dt>Returns:</dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNet(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readNet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</span></div>
<div class="block">Read deep learning network represented in one of the supported formats.
 This is an overloaded member function, provided for convenience.
 It differs from the above function only in what argument(s) it accepts.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework.</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration.</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNet(java.lang.String,org.opencv.core.MatOfByte)">
<h3>readNet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</span></div>
<div class="block">Read deep learning network represented in one of the supported formats.
 This is an overloaded member function, provided for convenience.
 It differs from the above function only in what argument(s) it accepts.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework.</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dt>Returns:</dt>
<dd>Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readTorchBlob(java.lang.String,boolean)">
<h3>readTorchBlob</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">readTorchBlob</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 boolean&nbsp;isBinary)</span></div>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.
 WARNING: This function has the same limitations as readNetFromTorch().</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>isBinary</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readTorchBlob(java.lang.String)">
<h3>readTorchBlob</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">readTorchBlob</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.
 WARNING: This function has the same limitations as readNetFromTorch().</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromModelOptimizer(java.lang.String,java.lang.String)">
<h3>readNetFromModelOptimizer</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromModelOptimizer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;bin)</span></div>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xml</code> - XML configuration file with network's topology.</dd>
<dd><code>bin</code> - Binary file with trained weights.</dd>
<dt>Returns:</dt>
<dd>Net object.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromModelOptimizer(java.lang.String)">
<h3>readNetFromModelOptimizer</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromModelOptimizer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xml)</span></div>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xml</code> - XML configuration file with network's topology.</dd>
<dt>Returns:</dt>
<dd>Net object.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromModelOptimizer(org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>readNetFromModelOptimizer</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromModelOptimizer</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</span></div>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferModelConfig</code> - Buffer contains XML configuration with network's topology.</dd>
<dd><code>bufferWeights</code> - Buffer contains binary data with trained weights.</dd>
<dt>Returns:</dt>
<dd>Net object.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromONNX(java.lang.String)">
<h3>readNetFromONNX</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromONNX</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;onnxFile)</span></div>
<div class="block">Reads a network model &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>onnxFile</code> - path to the .onnx file with text description of the network architecture.</dd>
<dt>Returns:</dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readNetFromONNX(org.opencv.core.MatOfByte)">
<h3>readNetFromONNX</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Net.html" title="class in org.opencv.dnn">Net</a></span>&nbsp;<span class="element-name">readNetFromONNX</span><wbr><span class="parameters">(<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buffer)</span></div>
<div class="block">Reads a network model from &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;
 in-memory buffer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - in-memory buffer that stores the ONNX model bytes.</dd>
<dt>Returns:</dt>
<dd>Network object that ready to do forward, throw an exception
 in failure cases.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="readTensorFromONNX(java.lang.String)">
<h3>readTensorFromONNX</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">readTensorFromONNX</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Creates blob from .pb file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - to the .pb file with input tensor.</dd>
<dt>Returns:</dt>
<dd>Mat.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean,int)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop,
 int&nbsp;ddepth)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not</dd>
<dd><code>ddepth</code> - Depth of output blob. Choose CV_32F or CV_8U.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size,org.opencv.core.Scalar)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double,org.opencv.core.Size)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>size</code> - spatial size for output image
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat,double)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 double&nbsp;scalefactor)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImage(org.opencv.core.Mat)">
<h3>blobFromImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean,int)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop,
 int&nbsp;ddepth)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not</dd>
<dd><code>ddepth</code> - Depth of output blob. Choose CV_32F or CV_8U.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double,org.opencv.core.Size,org.opencv.core.Scalar)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double,org.opencv.core.Size)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List,double)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 double&nbsp;scalefactor)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImages(java.util.List)">
<h3>blobFromImages</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImages</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</span></div>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat with NCHW dimensions order.

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImageWithParams(org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)">
<h3>blobFromImageWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImageWithParams</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</span></div>
<div class="block">Creates 4-dimensional blob from image with given params.

 This function is an extension of REF: blobFromImage to meet more image preprocess needs.
 Given input image and preprocessing parameters, and function outputs the blob.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (all with 1-, 3- or 4-channels).</dd>
<dd><code>param</code> - struct of Image2BlobParams, contains all parameters needed by processing of image to blob.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImageWithParams(org.opencv.core.Mat)">
<h3>blobFromImageWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImageWithParams</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Creates 4-dimensional blob from image with given params.

 This function is an extension of REF: blobFromImage to meet more image preprocess needs.
 Given input image and preprocessing parameters, and function outputs the blob.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (all with 1-, 3- or 4-channels).</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImageWithParams(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)">
<h3>blobFromImageWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">blobFromImageWithParams</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</span></div>
</section>
</li>
<li>
<section class="detail" id="blobFromImageWithParams(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>blobFromImageWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">blobFromImageWithParams</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</span></div>
</section>
</li>
<li>
<section class="detail" id="blobFromImagesWithParams(java.util.List,org.opencv.dnn.Image2BlobParams)">
<h3>blobFromImagesWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImagesWithParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</span></div>
<div class="block">Creates 4-dimensional blob from series of images with given params.

 This function is an extension of REF: blobFromImages to meet more image preprocess needs.
 Given input image and preprocessing parameters, and function outputs the blob.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input image (all with 1-, 3- or 4-channels).</dd>
<dd><code>param</code> - struct of Image2BlobParams, contains all parameters needed by processing of image to blob.</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImagesWithParams(java.util.List)">
<h3>blobFromImagesWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">blobFromImagesWithParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</span></div>
<div class="block">Creates 4-dimensional blob from series of images with given params.

 This function is an extension of REF: blobFromImages to meet more image preprocess needs.
 Given input image and preprocessing parameters, and function outputs the blob.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - input image (all with 1-, 3- or 4-channels).</dd>
<dt>Returns:</dt>
<dd>4-dimensional Mat.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobFromImagesWithParams(java.util.List,org.opencv.core.Mat,org.opencv.dnn.Image2BlobParams)">
<h3>blobFromImagesWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">blobFromImagesWithParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
 <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;param)</span></div>
</section>
</li>
<li>
<section class="detail" id="blobFromImagesWithParams(java.util.List,org.opencv.core.Mat)">
<h3>blobFromImagesWithParams</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">blobFromImagesWithParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</span></div>
</section>
</li>
<li>
<section class="detail" id="imagesFromBlob(org.opencv.core.Mat,java.util.List)">
<h3>imagesFromBlob</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">imagesFromBlob</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob_,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images_)</span></div>
<div class="block">Parse a 4D blob and output the images it contains as 2D arrays through a simpler data structure
 (std::vector&lt;cv::Mat&gt;).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>blob_</code> - 4 dimensional array (images, channels, height, width) in floating point precision (CV_32F) from
 which you would like to extract the images.</dd>
<dd><code>images_</code> - array of 2D Mat containing the images extracted from the blob in floating point precision
 (CV_32F). They are non normalized neither mean added. The number of returned images equals the first dimension
 of the blob (batch size). Every image has a number of channels equals to the second dimension of the blob (depth).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shrinkCaffeModel(java.lang.String,java.lang.String,java.util.List)">
<h3>shrinkCaffeModel</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">shrinkCaffeModel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dst,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;layersTypes)</span></div>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Path to origin model from Caffe framework contains single
 precision floating point weights (usually has <code>.caffemodel</code> extension).</dd>
<dd><code>dst</code> - Path to destination model with updated weights.</dd>
<dd><code>layersTypes</code> - Set of layers types which parameters will be converted.
 By default, converts only Convolutional and Fully-Connected layers'
 weights.

 <b>Note:</b> Shrinked model has no origin float32 weights so it can't be used
 in origin Caffe framework anymore. However the structure of data
 is taken from NVidia's Caffe fork: https://github.com/NVIDIA/caffe.
 So the resulting model may be used there.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shrinkCaffeModel(java.lang.String,java.lang.String)">
<h3>shrinkCaffeModel</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">shrinkCaffeModel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dst)</span></div>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Path to origin model from Caffe framework contains single
 precision floating point weights (usually has <code>.caffemodel</code> extension).</dd>
<dd><code>dst</code> - Path to destination model with updated weights.
 By default, converts only Convolutional and Fully-Connected layers'
 weights.

 <b>Note:</b> Shrinked model has no origin float32 weights so it can't be used
 in origin Caffe framework anymore. However the structure of data
 is taken from NVidia's Caffe fork: https://github.com/NVIDIA/caffe.
 So the resulting model may be used there.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeTextGraph(java.lang.String,java.lang.String)">
<h3>writeTextGraph</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeTextGraph</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Create a text representation for a binary network stored in protocol buffer format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - A path to binary network.</dd>
<dd><code>output</code> - A path to output text file to be created.

 <b>Note:</b> To reduce output file size, trained weights are not included.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float,int)">
<h3>NMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</span></div>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
<dd><code>top_k</code> - if <code>&amp;gt;0</code>, keep at most <code>top_k</code> picked indices.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float)">
<h3>NMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</span></div>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxes(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)">
<h3>NMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</span></div>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float,int)">
<h3>NMSBoxesRotated</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesRotated</span><wbr><span class="parameters">(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</span></div>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,float)">
<h3>NMSBoxesRotated</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesRotated</span><wbr><span class="parameters">(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</span></div>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesRotated(org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)">
<h3>NMSBoxesRotated</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesRotated</span><wbr><span class="parameters">(<a href="../core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</span></div>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt,float,int)">
<h3>NMSBoxesBatched</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesBatched</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta,
 int&nbsp;top_k)</span></div>
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>class_ids</code> - a set of corresponding class ids. Ids are integer and usually start from 0.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
<dd><code>top_k</code> - if <code>&amp;gt;0</code>, keep at most <code>top_k</code> picked indices.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt,float)">
<h3>NMSBoxesBatched</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesBatched</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 float&nbsp;eta)</span></div>
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>class_ids</code> - a set of corresponding class ids. Ids are integer and usually start from 0.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NMSBoxesBatched(org.opencv.core.MatOfRect2d,org.opencv.core.MatOfFloat,org.opencv.core.MatOfInt,float,float,org.opencv.core.MatOfInt)">
<h3>NMSBoxesBatched</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">NMSBoxesBatched</span><wbr><span class="parameters">(<a href="../core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;class_ids,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</span></div>
<div class="block">Performs batched non maximum suppression on given boxes and corresponding scores across different classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>class_ids</code> - a set of corresponding class ids. Ids are integer and usually start from 0.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,long,float)">
<h3>softNMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">softNMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 long&nbsp;top_k,
 float&nbsp;sigma)</span></div>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>top_k</code> - keep at most <code>top_k</code> picked indices.</dd>
<dd><code>sigma</code> - parameter of Gaussian weighting.
 SEE: SoftNMSMethod</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt,long)">
<h3>softNMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">softNMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
 long&nbsp;top_k)</span></div>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>top_k</code> - keep at most <code>top_k</code> picked indices.
 SEE: SoftNMSMethod</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="softNMSBoxes(org.opencv.core.MatOfRect,org.opencv.core.MatOfFloat,org.opencv.core.MatOfFloat,float,float,org.opencv.core.MatOfInt)">
<h3>softNMSBoxes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">softNMSBoxes</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</span></div>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.
 SEE: SoftNMSMethod</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resetMyriadDevice()">
<h3>resetMyriadDevice</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resetMyriadDevice</span>()</div>
<div class="block">Release a Myriad device (binded by OpenCV).

 Single Myriad device cannot be shared across multiple processes which uses
 Inference Engine's Myriad plugin.</div>
</section>
</li>
<li>
<section class="detail" id="getInferenceEngineVPUType()">
<h3>getInferenceEngineVPUType</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getInferenceEngineVPUType</span>()</div>
<div class="block">Returns Inference Engine VPU type.

 See values of <code>CV_DNN_INFERENCE_ENGINE_VPU_TYPE_*</code> macros.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInferenceEngineCPUType()">
<h3>getInferenceEngineCPUType</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getInferenceEngineCPUType</span>()</div>
<div class="block">Returns Inference Engine CPU type.

 Specify OpenVINO plugin: CPU or ARM.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="releaseHDDLPlugin()">
<h3>releaseHDDLPlugin</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">releaseHDDLPlugin</span>()</div>
<div class="block">Release a HDDL plugin.</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
