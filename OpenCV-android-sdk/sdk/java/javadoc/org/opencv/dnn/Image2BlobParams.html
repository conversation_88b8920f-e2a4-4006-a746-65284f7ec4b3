<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Image2BlobParams (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: Image2BlobParams">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class Image2BlobParams" class="title">Class Image2BlobParams</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.dnn.Image2BlobParams</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Image2BlobParams</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Processing params of image to blob.

 It includes all possible image processing operations and corresponding parameters.

 SEE: blobFromImageWithParams

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.
 The order and usage of <code>scalefactor</code>, <code>size</code>, <code>mean</code>, <code>swapRB</code>, and <code>ddepth</code> are consistent
 with the function of REF: blobFromImage.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Image2BlobParams</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar,org.opencv.core.Size)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean,int)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 int&nbsp;ddepth)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean,int,org.opencv.core.Scalar)" class="member-name-link">Image2BlobParams</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 int&nbsp;ddepth,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderValue)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#blobRectsToImageRects(org.opencv.core.MatOfRect,org.opencv.core.MatOfRect,org.opencv.core.Size)" class="member-name-link">blobRectsToImageRects</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rBlob,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rImg,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get rectangle coordinates in original image system from rectangle in blob coordinates.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#blobRectToImageRect(org.opencv.core.Rect,org.opencv.core.Size)" class="member-name-link">blobRectToImageRect</a><wbr>(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rBlob,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get rectangle coordinates in original image system from rectangle in blob coordinates.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_borderValue()" class="member-name-link">get_borderValue</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_ddepth()" class="member-name-link">get_ddepth</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_mean()" class="member-name-link">get_mean</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_scalefactor()" class="member-name-link">get_scalefactor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_size()" class="member-name-link">get_size</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_swapRB()" class="member-name-link">get_swapRB</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_borderValue(org.opencv.core.Scalar)" class="member-name-link">set_borderValue</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_ddepth(int)" class="member-name-link">set_ddepth</a><wbr>(int&nbsp;ddepth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_mean(org.opencv.core.Scalar)" class="member-name-link">set_mean</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_scalefactor(org.opencv.core.Scalar)" class="member-name-link">set_scalefactor</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_size(org.opencv.core.Size)" class="member-name-link">set_size</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_swapRB(boolean)" class="member-name-link">set_swapRB</a><wbr>(boolean&nbsp;swapRB)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean,int,org.opencv.core.Scalar)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 int&nbsp;ddepth,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderValue)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean,int)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 int&nbsp;ddepth)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar,org.opencv.core.Size,org.opencv.core.Scalar)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar,org.opencv.core.Size)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Scalar)">
<h3>Image2BlobParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Image2BlobParams</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="blobRectToImageRect(org.opencv.core.Rect,org.opencv.core.Size)">
<h3>blobRectToImageRect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></span>&nbsp;<span class="element-name">blobRectToImageRect</span><wbr><span class="parameters">(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rBlob,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Get rectangle coordinates in original image system from rectangle in blob coordinates.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rBlob</code> - rect in blob coordinates.</dd>
<dd><code>size</code> - original input image size.</dd>
<dt>Returns:</dt>
<dd>rectangle in original image coordinates.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blobRectsToImageRects(org.opencv.core.MatOfRect,org.opencv.core.MatOfRect,org.opencv.core.Size)">
<h3>blobRectsToImageRects</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">blobRectsToImageRects</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rBlob,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rImg,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Get rectangle coordinates in original image system from rectangle in blob coordinates.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rBlob</code> - rect in blob coordinates.</dd>
<dd><code>rImg</code> - result rect in image coordinates.</dd>
<dd><code>size</code> - original input image size.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="get_scalefactor()">
<h3>get_scalefactor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></span>&nbsp;<span class="element-name">get_scalefactor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_scalefactor(org.opencv.core.Scalar)">
<h3>set_scalefactor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_scalefactor</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_size()">
<h3>get_size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">get_size</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_size(org.opencv.core.Size)">
<h3>set_size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_size</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_mean()">
<h3>get_mean</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></span>&nbsp;<span class="element-name">get_mean</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_mean(org.opencv.core.Scalar)">
<h3>set_mean</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_mean</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_swapRB()">
<h3>get_swapRB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">get_swapRB</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_swapRB(boolean)">
<h3>set_swapRB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_swapRB</span><wbr><span class="parameters">(boolean&nbsp;swapRB)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_ddepth()">
<h3>get_ddepth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_ddepth</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_ddepth(int)">
<h3>set_ddepth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_ddepth</span><wbr><span class="parameters">(int&nbsp;ddepth)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_borderValue()">
<h3>get_borderValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a></span>&nbsp;<span class="element-name">get_borderValue</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_borderValue(org.opencv.core.Scalar)">
<h3>set_borderValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_borderValue</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderValue)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
