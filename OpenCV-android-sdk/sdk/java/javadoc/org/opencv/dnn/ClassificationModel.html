<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>ClassificationModel (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: ClassificationModel">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class ClassificationModel" class="title">Class ClassificationModel</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="Model.html" title="class in org.opencv.dnn">org.opencv.dnn.Model</a>
<div class="inheritance">org.opencv.dnn.ClassificationModel</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ClassificationModel</span>
<span class="extends-implements">extends <a href="Model.html" title="class in org.opencv.dnn">Model</a></span></div>
<div class="block">This class represents high-level API for classification models.

 ClassificationModel allows to set params for preprocessing input image.
 ClassificationModel creates net from file with trained weights and config,
 sets preprocessing input, runs forward pass and return top-1 prediction.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">ClassificationModel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color">
<div class="block">Create classification model from network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">ClassificationModel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create classification model from network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.dnn.Net)" class="member-name-link">ClassificationModel</a><wbr>(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code></div>
<div class="col-last even-row-color">
<div class="block">Create model from deep learning network.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#classify(org.opencv.core.Mat,int%5B%5D,float%5B%5D)" class="member-name-link">classify</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
 int[]&nbsp;classId,
 float[]&nbsp;conf)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnableSoftmaxPostProcessing()" class="member-name-link">getEnableSoftmaxPostProcessing</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get enable/disable softmax post processing option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnableSoftmaxPostProcessing(boolean)" class="member-name-link">setEnableSoftmaxPostProcessing</a><wbr>(boolean&nbsp;enable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set enable/disable softmax post processing option.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.dnn.Model">Methods inherited from class&nbsp;org.opencv.dnn.<a href="Model.html" title="class in org.opencv.dnn">Model</a></h3>
<code><a href="Model.html#enableWinograd(boolean)">enableWinograd</a>, <a href="Model.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="Model.html#predict(org.opencv.core.Mat,java.util.List)">predict</a>, <a href="Model.html#setInputCrop(boolean)">setInputCrop</a>, <a href="Model.html#setInputMean(org.opencv.core.Scalar)">setInputMean</a>, <a href="Model.html#setInputParams()">setInputParams</a>, <a href="Model.html#setInputParams(double)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)">setInputParams</a>, <a href="Model.html#setInputScale(org.opencv.core.Scalar)">setInputScale</a>, <a href="Model.html#setInputSize(int,int)">setInputSize</a>, <a href="Model.html#setInputSize(org.opencv.core.Size)">setInputSize</a>, <a href="Model.html#setInputSwapRB(boolean)">setInputSwapRB</a>, <a href="Model.html#setOutputNames(java.util.List)">setOutputNames</a>, <a href="Model.html#setPreferableBackend(int)">setPreferableBackend</a>, <a href="Model.html#setPreferableTarget(int)">setPreferableTarget</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>ClassificationModel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ClassificationModel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Create classification model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>ClassificationModel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ClassificationModel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Create classification model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.dnn.Net)">
<h3>ClassificationModel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ClassificationModel</span><wbr><span class="parameters">(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</span></div>
<div class="block">Create model from deep learning network.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>network</code> - Net object.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setEnableSoftmaxPostProcessing(boolean)">
<h3>setEnableSoftmaxPostProcessing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></span>&nbsp;<span class="element-name">setEnableSoftmaxPostProcessing</span><wbr><span class="parameters">(boolean&nbsp;enable)</span></div>
<div class="block">Set enable/disable softmax post processing option.

 If this option is true, softmax is applied after forward inference within the classify() function
 to convert the confidences range to [0.0-1.0].
 This function allows you to toggle this behavior.
 Please turn true when not contain softmax layer in model.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enable</code> - Set enable softmax post processing within the classify() function.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEnableSoftmaxPostProcessing()">
<h3>getEnableSoftmaxPostProcessing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getEnableSoftmaxPostProcessing</span>()</div>
<div class="block">Get enable/disable softmax post processing option.

 This option defaults to false, softmax post processing is not applied within the classify() function.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="classify(org.opencv.core.Mat,int[],float[])">
<h3>classify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">classify</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
 int[]&nbsp;classId,
 float[]&nbsp;conf)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
