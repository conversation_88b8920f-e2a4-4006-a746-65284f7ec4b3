<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Model (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: Model">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class Model" class="title">Class Model</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.dnn.Model</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></code>, <code><a href="DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a></code>, <code><a href="KeypointsModel.html" title="class in org.opencv.dnn">KeypointsModel</a></code>, <code><a href="SegmentationModel.html" title="class in org.opencv.dnn">SegmentationModel</a></code>, <code><a href="TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></code>, <code><a href="TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Model</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">This class is presented high-level API for neural networks.

 Model allows to set params for preprocessing input image.
 Model creates net from file with trained weights and config,
 sets preprocessing input and runs forward pass.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">Model</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color">
<div class="block">Create model from deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">Model</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create model from deep learning network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.dnn.Net)" class="member-name-link">Model</a><wbr>(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code></div>
<div class="col-last even-row-color">
<div class="block">Create model from deep learning network.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enableWinograd(boolean)" class="member-name-link">enableWinograd</a><wbr>(boolean&nbsp;useWinograd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predict(org.opencv.core.Mat,java.util.List)" class="member-name-link">predict</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given the <code>input</code> frame, create input blob, run net and return the output <code>blobs</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputCrop(boolean)" class="member-name-link">setInputCrop</a><wbr>(boolean&nbsp;crop)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set flag crop for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputMean(org.opencv.core.Scalar)" class="member-name-link">setInputMean</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set mean value for frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams()" class="member-name-link">setInputParams</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams(double)" class="member-name-link">setInputParams</a><wbr>(double&nbsp;scale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams(double,org.opencv.core.Size)" class="member-name-link">setInputParams</a><wbr>(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar)" class="member-name-link">setInputParams</a><wbr>(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)" class="member-name-link">setInputParams</a><wbr>(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)" class="member-name-link">setInputParams</a><wbr>(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set preprocessing parameters for frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputScale(org.opencv.core.Scalar)" class="member-name-link">setInputScale</a><wbr>(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set scalefactor value for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputSize(int,int)" class="member-name-link">setInputSize</a><wbr>(int&nbsp;width,
 int&nbsp;height)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputSize(org.opencv.core.Size)" class="member-name-link">setInputSize</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set input size for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputSwapRB(boolean)" class="member-name-link">setInputSwapRB</a><wbr>(boolean&nbsp;swapRB)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set flag swapRB for frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputNames(java.util.List)" class="member-name-link">setOutputNames</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;outNames)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set output names for frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreferableBackend(int)" class="member-name-link">setPreferableBackend</a><wbr>(int&nbsp;backendId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Model.html" title="class in org.opencv.dnn">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreferableTarget(int)" class="member-name-link">setPreferableTarget</a><wbr>(int&nbsp;targetId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>Model</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Model</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Create model from deep learning network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>Model</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Model</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Create model from deep learning network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.dnn.Net)">
<h3>Model</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Model</span><wbr><span class="parameters">(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</span></div>
<div class="block">Create model from deep learning network.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>network</code> - Net object.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setInputSize(org.opencv.core.Size)">
<h3>setInputSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputSize</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Set input size for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - New input size.
 <b>Note:</b> If shape of the new blob less than 0, then frame size not change.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputSize(int,int)">
<h3>setInputSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputSize</span><wbr><span class="parameters">(int&nbsp;width,
 int&nbsp;height)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>width</code> - New input width.</dd>
<dd><code>height</code> - New input height.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputMean(org.opencv.core.Scalar)">
<h3>setInputMean</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputMean</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
<div class="block">Set mean value for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputScale(org.opencv.core.Scalar)">
<h3>setInputScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputScale</span><wbr><span class="parameters">(<a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scale)</span></div>
<div class="block">Set scalefactor value for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputCrop(boolean)">
<h3>setInputCrop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputCrop</span><wbr><span class="parameters">(boolean&nbsp;crop)</span></div>
<div class="block">Set flag crop for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>crop</code> - Flag which indicates whether image will be cropped after resize or not.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputSwapRB(boolean)">
<h3>setInputSwapRB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setInputSwapRB</span><wbr><span class="parameters">(boolean&nbsp;swapRB)</span></div>
<div class="block">Set flag swapRB for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputNames(java.util.List)">
<h3>setOutputNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setOutputNames</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;outNames)</span></div>
<div class="block">Set output names for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outNames</code> - Names for output layers.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span><wbr><span class="parameters">(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB,
 boolean&nbsp;crop)</span></div>
<div class="block">Set preprocessing parameters for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.</dd>
<dd><code>crop</code> - Flag which indicates whether image will be cropped after resize or not.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span><wbr><span class="parameters">(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
 boolean&nbsp;swapRB)</span></div>
<div class="block">Set preprocessing parameters for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar)">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span><wbr><span class="parameters">(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</span></div>
<div class="block">Set preprocessing parameters for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams(double,org.opencv.core.Size)">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span><wbr><span class="parameters">(double&nbsp;scale,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</span></div>
<div class="block">Set preprocessing parameters for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams(double)">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span><wbr><span class="parameters">(double&nbsp;scale)</span></div>
<div class="block">Set preprocessing parameters for frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputParams()">
<h3>setInputParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputParams</span>()</div>
<div class="block">Set preprocessing parameters for frame.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</div>
</section>
</li>
<li>
<section class="detail" id="predict(org.opencv.core.Mat,java.util.List)">
<h3>predict</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">predict</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outs)</span></div>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return the output <code>blobs</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outs</code> - Allocated output blobs, which will store results of the computation.</dd>
<dd><code>frame</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreferableBackend(int)">
<h3>setPreferableBackend</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setPreferableBackend</span><wbr><span class="parameters">(int&nbsp;backendId)</span></div>
</section>
</li>
<li>
<section class="detail" id="setPreferableTarget(int)">
<h3>setPreferableTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">setPreferableTarget</span><wbr><span class="parameters">(int&nbsp;targetId)</span></div>
</section>
</li>
<li>
<section class="detail" id="enableWinograd(boolean)">
<h3>enableWinograd</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Model.html" title="class in org.opencv.dnn">Model</a></span>&nbsp;<span class="element-name">enableWinograd</span><wbr><span class="parameters">(boolean&nbsp;useWinograd)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
