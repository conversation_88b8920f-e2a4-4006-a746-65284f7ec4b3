<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>TextDetectionModel_DB (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.dnn, class: TextDetectionModel_DB">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.dnn</a></div>
<h1 title="Class TextDetectionModel_DB" class="title">Class TextDetectionModel_DB</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="Model.html" title="class in org.opencv.dnn">org.opencv.dnn.Model</a>
<div class="inheritance"><a href="TextDetectionModel.html" title="class in org.opencv.dnn">org.opencv.dnn.TextDetectionModel</a>
<div class="inheritance">org.opencv.dnn.TextDetectionModel_DB</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TextDetectionModel_DB</span>
<span class="extends-implements">extends <a href="TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></span></div>
<div class="block">This class represents high-level API for text detection DL networks compatible with DB model.

 Related publications: CITE: liao2020real
 Paper: https://arxiv.org/abs/1911.08947
 For more information about the hyper-parameters setting, please refer to https://github.com/MhLiao/DB

 Configurable parameters:
 - (float) binaryThreshold - The threshold of the binary map. It is usually set to 0.3.
 - (float) polygonThreshold - The threshold of text polygons. It is usually set to 0.5, 0.6, and 0.7. Default is 0.5f
 - (double) unclipRatio - The unclip ratio of the detected text region, which determines the output size. It is usually set to 2.0.
 - (int) maxCandidates - The max number of the output results.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">TextDetectionModel_DB</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color">
<div class="block">Create text detection model from network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">TextDetectionModel_DB</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create text detection model from network represented in one of the supported formats.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.dnn.Net)" class="member-name-link">TextDetectionModel_DB</a><wbr>(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code></div>
<div class="col-last even-row-color">
<div class="block">Create text detection algorithm from deep learning network.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBinaryThreshold()" class="member-name-link">getBinaryThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxCandidates()" class="member-name-link">getMaxCandidates</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPolygonThreshold()" class="member-name-link">getPolygonThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUnclipRatio()" class="member-name-link">getUnclipRatio</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBinaryThreshold(float)" class="member-name-link">setBinaryThreshold</a><wbr>(float&nbsp;binaryThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxCandidates(int)" class="member-name-link">setMaxCandidates</a><wbr>(int&nbsp;maxCandidates)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPolygonThreshold(float)" class="member-name-link">setPolygonThreshold</a><wbr>(float&nbsp;polygonThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUnclipRatio(double)" class="member-name-link">setUnclipRatio</a><wbr>(double&nbsp;unclipRatio)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.dnn.TextDetectionModel">Methods inherited from class&nbsp;org.opencv.dnn.<a href="TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></h3>
<code><a href="TextDetectionModel.html#detect(org.opencv.core.Mat,java.util.List)">detect</a>, <a href="TextDetectionModel.html#detect(org.opencv.core.Mat,java.util.List,org.opencv.core.MatOfFloat)">detect</a>, <a href="TextDetectionModel.html#detectTextRectangles(org.opencv.core.Mat,org.opencv.core.MatOfRotatedRect)">detectTextRectangles</a>, <a href="TextDetectionModel.html#detectTextRectangles(org.opencv.core.Mat,org.opencv.core.MatOfRotatedRect,org.opencv.core.MatOfFloat)">detectTextRectangles</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.dnn.Model">Methods inherited from class&nbsp;org.opencv.dnn.<a href="Model.html" title="class in org.opencv.dnn">Model</a></h3>
<code><a href="Model.html#enableWinograd(boolean)">enableWinograd</a>, <a href="Model.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="Model.html#predict(org.opencv.core.Mat,java.util.List)">predict</a>, <a href="Model.html#setInputCrop(boolean)">setInputCrop</a>, <a href="Model.html#setInputMean(org.opencv.core.Scalar)">setInputMean</a>, <a href="Model.html#setInputParams()">setInputParams</a>, <a href="Model.html#setInputParams(double)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean)">setInputParams</a>, <a href="Model.html#setInputParams(double,org.opencv.core.Size,org.opencv.core.Scalar,boolean,boolean)">setInputParams</a>, <a href="Model.html#setInputScale(org.opencv.core.Scalar)">setInputScale</a>, <a href="Model.html#setInputSize(int,int)">setInputSize</a>, <a href="Model.html#setInputSize(org.opencv.core.Size)">setInputSize</a>, <a href="Model.html#setInputSwapRB(boolean)">setInputSwapRB</a>, <a href="Model.html#setOutputNames(java.util.List)">setOutputNames</a>, <a href="Model.html#setPreferableBackend(int)">setPreferableBackend</a>, <a href="Model.html#setPreferableTarget(int)">setPreferableTarget</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.dnn.Net)">
<h3>TextDetectionModel_DB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TextDetectionModel_DB</span><wbr><span class="parameters">(<a href="Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</span></div>
<div class="block">Create text detection algorithm from deep learning network.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>network</code> - Net object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>TextDetectionModel_DB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TextDetectionModel_DB</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Create text detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>TextDetectionModel_DB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TextDetectionModel_DB</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
<div class="block">Create text detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setBinaryThreshold(float)">
<h3>setBinaryThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></span>&nbsp;<span class="element-name">setBinaryThreshold</span><wbr><span class="parameters">(float&nbsp;binaryThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getBinaryThreshold()">
<h3>getBinaryThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getBinaryThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPolygonThreshold(float)">
<h3>setPolygonThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></span>&nbsp;<span class="element-name">setPolygonThreshold</span><wbr><span class="parameters">(float&nbsp;polygonThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPolygonThreshold()">
<h3>getPolygonThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getPolygonThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setUnclipRatio(double)">
<h3>setUnclipRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></span>&nbsp;<span class="element-name">setUnclipRatio</span><wbr><span class="parameters">(double&nbsp;unclipRatio)</span></div>
</section>
</li>
<li>
<section class="detail" id="getUnclipRatio()">
<h3>getUnclipRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getUnclipRatio</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMaxCandidates(int)">
<h3>setMaxCandidates</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></span>&nbsp;<span class="element-name">setMaxCandidates</span><wbr><span class="parameters">(int&nbsp;maxCandidates)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxCandidates()">
<h3>getMaxCandidates</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxCandidates</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
