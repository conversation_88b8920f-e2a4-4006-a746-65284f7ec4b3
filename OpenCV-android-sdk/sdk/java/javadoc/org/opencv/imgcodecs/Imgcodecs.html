<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Imgcodecs (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.imgcodecs, class: Imgcodecs">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.imgcodecs</a></div>
<h1 title="Class Imgcodecs" class="title">Class Imgcodecs</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.imgcodecs.Imgcodecs</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Imgcodecs</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_ANYCOLOR" class="member-name-link">IMREAD_ANYCOLOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_ANYDEPTH" class="member-name-link">IMREAD_ANYDEPTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_COLOR" class="member-name-link">IMREAD_COLOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_COLOR_BGR" class="member-name-link">IMREAD_COLOR_BGR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_COLOR_RGB" class="member-name-link">IMREAD_COLOR_RGB</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_GRAYSCALE" class="member-name-link">IMREAD_GRAYSCALE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_IGNORE_ORIENTATION" class="member-name-link">IMREAD_IGNORE_ORIENTATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_LOAD_GDAL" class="member-name-link">IMREAD_LOAD_GDAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_REDUCED_COLOR_2" class="member-name-link">IMREAD_REDUCED_COLOR_2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_REDUCED_COLOR_4" class="member-name-link">IMREAD_REDUCED_COLOR_4</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_REDUCED_COLOR_8" class="member-name-link">IMREAD_REDUCED_COLOR_8</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_REDUCED_GRAYSCALE_2" class="member-name-link">IMREAD_REDUCED_GRAYSCALE_2</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_REDUCED_GRAYSCALE_4" class="member-name-link">IMREAD_REDUCED_GRAYSCALE_4</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMREAD_REDUCED_GRAYSCALE_8" class="member-name-link">IMREAD_REDUCED_GRAYSCALE_8</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMREAD_UNCHANGED" class="member-name-link">IMREAD_UNCHANGED</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_AVIF_DEPTH" class="member-name-link">IMWRITE_AVIF_DEPTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_AVIF_QUALITY" class="member-name-link">IMWRITE_AVIF_QUALITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_AVIF_SPEED" class="member-name-link">IMWRITE_AVIF_SPEED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION" class="member-name-link">IMWRITE_EXR_COMPRESSION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_B44" class="member-name-link">IMWRITE_EXR_COMPRESSION_B44</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_B44A" class="member-name-link">IMWRITE_EXR_COMPRESSION_B44A</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_DWAA" class="member-name-link">IMWRITE_EXR_COMPRESSION_DWAA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_DWAB" class="member-name-link">IMWRITE_EXR_COMPRESSION_DWAB</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_NO" class="member-name-link">IMWRITE_EXR_COMPRESSION_NO</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_PIZ" class="member-name-link">IMWRITE_EXR_COMPRESSION_PIZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_PXR24" class="member-name-link">IMWRITE_EXR_COMPRESSION_PXR24</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_RLE" class="member-name-link">IMWRITE_EXR_COMPRESSION_RLE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_ZIP" class="member-name-link">IMWRITE_EXR_COMPRESSION_ZIP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_COMPRESSION_ZIPS" class="member-name-link">IMWRITE_EXR_COMPRESSION_ZIPS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_DWA_COMPRESSION_LEVEL" class="member-name-link">IMWRITE_EXR_DWA_COMPRESSION_LEVEL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_TYPE" class="member-name-link">IMWRITE_EXR_TYPE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_EXR_TYPE_FLOAT" class="member-name-link">IMWRITE_EXR_TYPE_FLOAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_EXR_TYPE_HALF" class="member-name-link">IMWRITE_EXR_TYPE_HALF</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE" class="member-name-link">IMWRITE_GIF_COLORTABLE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_128" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_128</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_16" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_16</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_256" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_256</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_32" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_32</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_64" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_64</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_COLORTABLE_SIZE_8" class="member-name-link">IMWRITE_GIF_COLORTABLE_SIZE_8</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_DITHER" class="member-name-link">IMWRITE_GIF_DITHER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_FAST_FLOYD_DITHER" class="member-name-link">IMWRITE_GIF_FAST_FLOYD_DITHER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_FAST_NO_DITHER" class="member-name-link">IMWRITE_GIF_FAST_NO_DITHER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_LOOP" class="member-name-link">IMWRITE_GIF_LOOP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_QUALITY" class="member-name-link">IMWRITE_GIF_QUALITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_GIF_SPEED" class="member-name-link">IMWRITE_GIF_SPEED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_GIF_TRANSPARENCY" class="member-name-link">IMWRITE_GIF_TRANSPARENCY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_HDR_COMPRESSION" class="member-name-link">IMWRITE_HDR_COMPRESSION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_HDR_COMPRESSION_NONE" class="member-name-link">IMWRITE_HDR_COMPRESSION_NONE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_HDR_COMPRESSION_RLE" class="member-name-link">IMWRITE_HDR_COMPRESSION_RLE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_CHROMA_QUALITY" class="member-name-link">IMWRITE_JPEG_CHROMA_QUALITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_LUMA_QUALITY" class="member-name-link">IMWRITE_JPEG_LUMA_QUALITY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_OPTIMIZE" class="member-name-link">IMWRITE_JPEG_OPTIMIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_PROGRESSIVE" class="member-name-link">IMWRITE_JPEG_PROGRESSIVE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_QUALITY" class="member-name-link">IMWRITE_JPEG_QUALITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_RST_INTERVAL" class="member-name-link">IMWRITE_JPEG_RST_INTERVAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR_411" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR_411</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR_420" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR_420</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR_422" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR_422</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR_440" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR_440</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEG_SAMPLING_FACTOR_444" class="member-name-link">IMWRITE_JPEG_SAMPLING_FACTOR_444</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEG2000_COMPRESSION_X1000" class="member-name-link">IMWRITE_JPEG2000_COMPRESSION_X1000</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEGXL_DECODING_SPEED" class="member-name-link">IMWRITE_JPEGXL_DECODING_SPEED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEGXL_DISTANCE" class="member-name-link">IMWRITE_JPEGXL_DISTANCE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_JPEGXL_EFFORT" class="member-name-link">IMWRITE_JPEGXL_EFFORT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_JPEGXL_QUALITY" class="member-name-link">IMWRITE_JPEGXL_QUALITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PAM_FORMAT_BLACKANDWHITE" class="member-name-link">IMWRITE_PAM_FORMAT_BLACKANDWHITE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PAM_FORMAT_GRAYSCALE" class="member-name-link">IMWRITE_PAM_FORMAT_GRAYSCALE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA" class="member-name-link">IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PAM_FORMAT_NULL" class="member-name-link">IMWRITE_PAM_FORMAT_NULL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PAM_FORMAT_RGB" class="member-name-link">IMWRITE_PAM_FORMAT_RGB</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PAM_FORMAT_RGB_ALPHA" class="member-name-link">IMWRITE_PAM_FORMAT_RGB_ALPHA</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PAM_TUPLETYPE" class="member-name-link">IMWRITE_PAM_TUPLETYPE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PNG_BILEVEL" class="member-name-link">IMWRITE_PNG_BILEVEL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PNG_COMPRESSION" class="member-name-link">IMWRITE_PNG_COMPRESSION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PNG_STRATEGY" class="member-name-link">IMWRITE_PNG_STRATEGY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PNG_STRATEGY_DEFAULT" class="member-name-link">IMWRITE_PNG_STRATEGY_DEFAULT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PNG_STRATEGY_FILTERED" class="member-name-link">IMWRITE_PNG_STRATEGY_FILTERED</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PNG_STRATEGY_FIXED" class="member-name-link">IMWRITE_PNG_STRATEGY_FIXED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY" class="member-name-link">IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_PNG_STRATEGY_RLE" class="member-name-link">IMWRITE_PNG_STRATEGY_RLE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_PXM_BINARY" class="member-name-link">IMWRITE_PXM_BINARY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION" class="member-name-link">IMWRITE_TIFF_COMPRESSION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE" class="member-name-link">IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITT_T4" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITT_T4</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITT_T6" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITT_T6</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITTFAX3" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITTFAX3</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITTFAX4" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITTFAX4</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITTRLE" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITTRLE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_CCITTRLEW" class="member-name-link">IMWRITE_TIFF_COMPRESSION_CCITTRLEW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_DCS" class="member-name-link">IMWRITE_TIFF_COMPRESSION_DCS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_DEFLATE" class="member-name-link">IMWRITE_TIFF_COMPRESSION_DEFLATE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_IT8BL" class="member-name-link">IMWRITE_TIFF_COMPRESSION_IT8BL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_IT8CTPAD" class="member-name-link">IMWRITE_TIFF_COMPRESSION_IT8CTPAD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_IT8LW" class="member-name-link">IMWRITE_TIFF_COMPRESSION_IT8LW</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_IT8MP" class="member-name-link">IMWRITE_TIFF_COMPRESSION_IT8MP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_JBIG" class="member-name-link">IMWRITE_TIFF_COMPRESSION_JBIG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_JP2000" class="member-name-link">IMWRITE_TIFF_COMPRESSION_JP2000</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_JPEG" class="member-name-link">IMWRITE_TIFF_COMPRESSION_JPEG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_JXL" class="member-name-link">IMWRITE_TIFF_COMPRESSION_JXL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_LERC" class="member-name-link">IMWRITE_TIFF_COMPRESSION_LERC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_LZMA" class="member-name-link">IMWRITE_TIFF_COMPRESSION_LZMA</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_LZW" class="member-name-link">IMWRITE_TIFF_COMPRESSION_LZW</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_NEXT" class="member-name-link">IMWRITE_TIFF_COMPRESSION_NEXT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_NONE" class="member-name-link">IMWRITE_TIFF_COMPRESSION_NONE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_OJPEG" class="member-name-link">IMWRITE_TIFF_COMPRESSION_OJPEG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_PACKBITS" class="member-name-link">IMWRITE_TIFF_COMPRESSION_PACKBITS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_PIXARFILM" class="member-name-link">IMWRITE_TIFF_COMPRESSION_PIXARFILM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_PIXARLOG" class="member-name-link">IMWRITE_TIFF_COMPRESSION_PIXARLOG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_SGILOG" class="member-name-link">IMWRITE_TIFF_COMPRESSION_SGILOG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_SGILOG24" class="member-name-link">IMWRITE_TIFF_COMPRESSION_SGILOG24</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_T43" class="member-name-link">IMWRITE_TIFF_COMPRESSION_T43</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_T85" class="member-name-link">IMWRITE_TIFF_COMPRESSION_T85</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_THUNDERSCAN" class="member-name-link">IMWRITE_TIFF_COMPRESSION_THUNDERSCAN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_WEBP" class="member-name-link">IMWRITE_TIFF_COMPRESSION_WEBP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_COMPRESSION_ZSTD" class="member-name-link">IMWRITE_TIFF_COMPRESSION_ZSTD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_PREDICTOR" class="member-name-link">IMWRITE_TIFF_PREDICTOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT" class="member-name-link">IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_PREDICTOR_HORIZONTAL" class="member-name-link">IMWRITE_TIFF_PREDICTOR_HORIZONTAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_PREDICTOR_NONE" class="member-name-link">IMWRITE_TIFF_PREDICTOR_NONE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_RESUNIT" class="member-name-link">IMWRITE_TIFF_RESUNIT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_ROWSPERSTRIP" class="member-name-link">IMWRITE_TIFF_ROWSPERSTRIP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_TIFF_XDPI" class="member-name-link">IMWRITE_TIFF_XDPI</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#IMWRITE_TIFF_YDPI" class="member-name-link">IMWRITE_TIFF_YDPI</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IMWRITE_WEBP_QUALITY" class="member-name-link">IMWRITE_WEBP_QUALITY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Imgcodecs</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#haveImageReader(java.lang.String)" class="member-name-link">haveImageReader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Checks if the specified image file can be decoded by OpenCV.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#haveImageWriter(java.lang.String)" class="member-name-link">haveImageWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Checks if the specified image file or specified file extension can be encoded by OpenCV.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imcount(java.lang.String)" class="member-name-link">imcount</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the number of images inside the given file

 The function imcount returns the number of pages in a multi-page image (e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imcount(java.lang.String,int)" class="member-name-link">imcount</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the number of images inside the given file

 The function imcount returns the number of pages in a multi-page image (e.g.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imdecode(org.opencv.core.Mat,int)" class="member-name-link">imdecode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads an image from a buffer in memory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imdecodemulti(org.opencv.core.Mat,int,java.util.List)" class="member-name-link">imdecodemulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a multi-page image from a buffer in memory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imdecodemulti(org.opencv.core.Mat,int,java.util.List,org.opencv.core.Range)" class="member-name-link">imdecodemulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 <a href="../core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;range)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Reads a multi-page image from a buffer in memory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imencode(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfByte)" class="member-name-link">imencode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Encodes an image into a memory buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imencode(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfByte,org.opencv.core.MatOfInt)" class="member-name-link">imencode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Encodes an image into a memory buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imencodemulti(java.lang.String,java.util.List,org.opencv.core.MatOfByte)" class="member-name-link">imencodemulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgs,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Encodes array of images into a memory buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imencodemulti(java.lang.String,java.util.List,org.opencv.core.MatOfByte,org.opencv.core.MatOfInt)" class="member-name-link">imencodemulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgs,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Encodes array of images into a memory buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imread(java.lang.String)" class="member-name-link">imread</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads an image from a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imread(java.lang.String,int)" class="member-name-link">imread</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads an image from a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imread(java.lang.String,org.opencv.core.Mat)" class="member-name-link">imread</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads an image from a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imread(java.lang.String,org.opencv.core.Mat,int)" class="member-name-link">imread</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads an image from a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation)" class="member-name-link">imreadanimation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads frames from an animated image file into an Animation structure.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation,int)" class="member-name-link">imreadanimation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 int&nbsp;start)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads frames from an animated image file into an Animation structure.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation,int,int)" class="member-name-link">imreadanimation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 int&nbsp;start,
 int&nbsp;count)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads frames from an animated image file into an Animation structure.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadmulti(java.lang.String,java.util.List)" class="member-name-link">imreadmulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads a multi-page image from a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadmulti(java.lang.String,java.util.List,int)" class="member-name-link">imreadmulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads a multi-page image from a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadmulti(java.lang.String,java.util.List,int,int)" class="member-name-link">imreadmulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;start,
 int&nbsp;count)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads images of a multi-page image from a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imreadmulti(java.lang.String,java.util.List,int,int,int)" class="member-name-link">imreadmulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;start,
 int&nbsp;count,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads images of a multi-page image from a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwrite(java.lang.String,org.opencv.core.Mat)" class="member-name-link">imwrite</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Saves an image to a specified file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwrite(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfInt)" class="member-name-link">imwrite</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Saves an image to a specified file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwriteanimation(java.lang.String,org.opencv.imgcodecs.Animation)" class="member-name-link">imwriteanimation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Saves an Animation to a specified file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwriteanimation(java.lang.String,org.opencv.imgcodecs.Animation,org.opencv.core.MatOfInt)" class="member-name-link">imwriteanimation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Saves an Animation to a specified file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwritemulti(java.lang.String,java.util.List)" class="member-name-link">imwritemulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#imwritemulti(java.lang.String,java.util.List,org.opencv.core.MatOfInt)" class="member-name-link">imwritemulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="IMREAD_UNCHANGED">
<h3>IMREAD_UNCHANGED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_UNCHANGED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_UNCHANGED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_GRAYSCALE">
<h3>IMREAD_GRAYSCALE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_GRAYSCALE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_GRAYSCALE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_COLOR_BGR">
<h3>IMREAD_COLOR_BGR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_COLOR_BGR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR_BGR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_COLOR">
<h3>IMREAD_COLOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_COLOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_ANYDEPTH">
<h3>IMREAD_ANYDEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_ANYDEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYDEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_ANYCOLOR">
<h3>IMREAD_ANYCOLOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_ANYCOLOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYCOLOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_LOAD_GDAL">
<h3>IMREAD_LOAD_GDAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_LOAD_GDAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_LOAD_GDAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_GRAYSCALE_2">
<h3>IMREAD_REDUCED_GRAYSCALE_2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_GRAYSCALE_2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_COLOR_2">
<h3>IMREAD_REDUCED_COLOR_2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_COLOR_2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_GRAYSCALE_4">
<h3>IMREAD_REDUCED_GRAYSCALE_4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_GRAYSCALE_4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_COLOR_4">
<h3>IMREAD_REDUCED_COLOR_4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_COLOR_4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_GRAYSCALE_8">
<h3>IMREAD_REDUCED_GRAYSCALE_8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_GRAYSCALE_8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_REDUCED_COLOR_8">
<h3>IMREAD_REDUCED_COLOR_8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_REDUCED_COLOR_8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_IGNORE_ORIENTATION">
<h3>IMREAD_IGNORE_ORIENTATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_IGNORE_ORIENTATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_IGNORE_ORIENTATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMREAD_COLOR_RGB">
<h3>IMREAD_COLOR_RGB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMREAD_COLOR_RGB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR_RGB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_NO">
<h3>IMWRITE_EXR_COMPRESSION_NO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_NO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_NO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_RLE">
<h3>IMWRITE_EXR_COMPRESSION_RLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_RLE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_RLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_ZIPS">
<h3>IMWRITE_EXR_COMPRESSION_ZIPS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_ZIPS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIPS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_ZIP">
<h3>IMWRITE_EXR_COMPRESSION_ZIP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_ZIP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_PIZ">
<h3>IMWRITE_EXR_COMPRESSION_PIZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_PIZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PIZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_PXR24">
<h3>IMWRITE_EXR_COMPRESSION_PXR24</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_PXR24</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PXR24">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_B44">
<h3>IMWRITE_EXR_COMPRESSION_B44</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_B44</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_B44A">
<h3>IMWRITE_EXR_COMPRESSION_B44A</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_B44A</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44A">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_DWAA">
<h3>IMWRITE_EXR_COMPRESSION_DWAA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_DWAA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION_DWAB">
<h3>IMWRITE_EXR_COMPRESSION_DWAB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION_DWAB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_TYPE_HALF">
<h3>IMWRITE_EXR_TYPE_HALF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_TYPE_HALF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_HALF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_TYPE_FLOAT">
<h3>IMWRITE_EXR_TYPE_FLOAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_TYPE_FLOAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_FLOAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_QUALITY">
<h3>IMWRITE_JPEG_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_PROGRESSIVE">
<h3>IMWRITE_JPEG_PROGRESSIVE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_PROGRESSIVE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_PROGRESSIVE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_OPTIMIZE">
<h3>IMWRITE_JPEG_OPTIMIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_OPTIMIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_OPTIMIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_RST_INTERVAL">
<h3>IMWRITE_JPEG_RST_INTERVAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_RST_INTERVAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_RST_INTERVAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_LUMA_QUALITY">
<h3>IMWRITE_JPEG_LUMA_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_LUMA_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_LUMA_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_CHROMA_QUALITY">
<h3>IMWRITE_JPEG_CHROMA_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_CHROMA_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_CHROMA_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_COMPRESSION">
<h3>IMWRITE_PNG_COMPRESSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_COMPRESSION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY">
<h3>IMWRITE_PNG_STRATEGY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_BILEVEL">
<h3>IMWRITE_PNG_BILEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_BILEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_BILEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PXM_BINARY">
<h3>IMWRITE_PXM_BINARY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PXM_BINARY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PXM_BINARY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_TYPE">
<h3>IMWRITE_EXR_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_TYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_COMPRESSION">
<h3>IMWRITE_EXR_COMPRESSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_COMPRESSION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_EXR_DWA_COMPRESSION_LEVEL">
<h3>IMWRITE_EXR_DWA_COMPRESSION_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_EXR_DWA_COMPRESSION_LEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_DWA_COMPRESSION_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_WEBP_QUALITY">
<h3>IMWRITE_WEBP_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_WEBP_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_WEBP_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_HDR_COMPRESSION">
<h3>IMWRITE_HDR_COMPRESSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_HDR_COMPRESSION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_TUPLETYPE">
<h3>IMWRITE_PAM_TUPLETYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_TUPLETYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_TUPLETYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_RESUNIT">
<h3>IMWRITE_TIFF_RESUNIT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_RESUNIT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_RESUNIT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_XDPI">
<h3>IMWRITE_TIFF_XDPI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_XDPI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_XDPI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_YDPI">
<h3>IMWRITE_TIFF_YDPI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_YDPI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_YDPI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION">
<h3>IMWRITE_TIFF_COMPRESSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_ROWSPERSTRIP">
<h3>IMWRITE_TIFF_ROWSPERSTRIP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_ROWSPERSTRIP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_ROWSPERSTRIP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_PREDICTOR">
<h3>IMWRITE_TIFF_PREDICTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_PREDICTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_PREDICTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG2000_COMPRESSION_X1000">
<h3>IMWRITE_JPEG2000_COMPRESSION_X1000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG2000_COMPRESSION_X1000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG2000_COMPRESSION_X1000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_AVIF_QUALITY">
<h3>IMWRITE_AVIF_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_AVIF_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_AVIF_DEPTH">
<h3>IMWRITE_AVIF_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_AVIF_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_AVIF_SPEED">
<h3>IMWRITE_AVIF_SPEED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_AVIF_SPEED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_SPEED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEGXL_QUALITY">
<h3>IMWRITE_JPEGXL_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEGXL_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEGXL_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEGXL_EFFORT">
<h3>IMWRITE_JPEGXL_EFFORT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEGXL_EFFORT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEGXL_EFFORT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEGXL_DISTANCE">
<h3>IMWRITE_JPEGXL_DISTANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEGXL_DISTANCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEGXL_DISTANCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEGXL_DECODING_SPEED">
<h3>IMWRITE_JPEGXL_DECODING_SPEED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEGXL_DECODING_SPEED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEGXL_DECODING_SPEED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_LOOP">
<h3>IMWRITE_GIF_LOOP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_LOOP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_LOOP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_SPEED">
<h3>IMWRITE_GIF_SPEED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_SPEED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_SPEED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_QUALITY">
<h3>IMWRITE_GIF_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_DITHER">
<h3>IMWRITE_GIF_DITHER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_DITHER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_DITHER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_TRANSPARENCY">
<h3>IMWRITE_GIF_TRANSPARENCY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_TRANSPARENCY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_TRANSPARENCY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE">
<h3>IMWRITE_GIF_COLORTABLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_FAST_NO_DITHER">
<h3>IMWRITE_GIF_FAST_NO_DITHER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_FAST_NO_DITHER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_FAST_NO_DITHER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_FAST_FLOYD_DITHER">
<h3>IMWRITE_GIF_FAST_FLOYD_DITHER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_FAST_FLOYD_DITHER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_FAST_FLOYD_DITHER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_8">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_16">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_32">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_32</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_32</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_32">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_64">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_64</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_64</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_64">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_128">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_128</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_128</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_128">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_GIF_COLORTABLE_SIZE_256">
<h3>IMWRITE_GIF_COLORTABLE_SIZE_256</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_GIF_COLORTABLE_SIZE_256</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_GIF_COLORTABLE_SIZE_256">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_HDR_COMPRESSION_NONE">
<h3>IMWRITE_HDR_COMPRESSION_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_HDR_COMPRESSION_NONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_HDR_COMPRESSION_RLE">
<h3>IMWRITE_HDR_COMPRESSION_RLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_HDR_COMPRESSION_RLE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_RLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR_411">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR_411</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR_411</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_411">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR_420">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR_420</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR_420</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_420">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR_422">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR_422</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR_422</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_422">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR_440">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR_440</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR_440</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_440">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_JPEG_SAMPLING_FACTOR_444">
<h3>IMWRITE_JPEG_SAMPLING_FACTOR_444</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_JPEG_SAMPLING_FACTOR_444</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_444">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_NULL">
<h3>IMWRITE_PAM_FORMAT_NULL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_NULL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_NULL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_BLACKANDWHITE">
<h3>IMWRITE_PAM_FORMAT_BLACKANDWHITE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_BLACKANDWHITE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_BLACKANDWHITE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_GRAYSCALE">
<h3>IMWRITE_PAM_FORMAT_GRAYSCALE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_GRAYSCALE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">
<h3>IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_RGB">
<h3>IMWRITE_PAM_FORMAT_RGB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_RGB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PAM_FORMAT_RGB_ALPHA">
<h3>IMWRITE_PAM_FORMAT_RGB_ALPHA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PAM_FORMAT_RGB_ALPHA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB_ALPHA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY_DEFAULT">
<h3>IMWRITE_PNG_STRATEGY_DEFAULT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY_DEFAULT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_DEFAULT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY_FILTERED">
<h3>IMWRITE_PNG_STRATEGY_FILTERED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY_FILTERED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FILTERED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">
<h3>IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY_RLE">
<h3>IMWRITE_PNG_STRATEGY_RLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY_RLE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_RLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_PNG_STRATEGY_FIXED">
<h3>IMWRITE_PNG_STRATEGY_FIXED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_PNG_STRATEGY_FIXED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FIXED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_NONE">
<h3>IMWRITE_TIFF_COMPRESSION_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_NONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITTRLE">
<h3>IMWRITE_TIFF_COMPRESSION_CCITTRLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITTRLE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITTRLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITTFAX3">
<h3>IMWRITE_TIFF_COMPRESSION_CCITTFAX3</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITTFAX3</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITTFAX3">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITT_T4">
<h3>IMWRITE_TIFF_COMPRESSION_CCITT_T4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITT_T4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITT_T4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITTFAX4">
<h3>IMWRITE_TIFF_COMPRESSION_CCITTFAX4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITTFAX4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITTFAX4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITT_T6">
<h3>IMWRITE_TIFF_COMPRESSION_CCITT_T6</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITT_T6</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITT_T6">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_LZW">
<h3>IMWRITE_TIFF_COMPRESSION_LZW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_LZW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_LZW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_OJPEG">
<h3>IMWRITE_TIFF_COMPRESSION_OJPEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_OJPEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_OJPEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_JPEG">
<h3>IMWRITE_TIFF_COMPRESSION_JPEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_JPEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_JPEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_T85">
<h3>IMWRITE_TIFF_COMPRESSION_T85</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_T85</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_T85">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_T43">
<h3>IMWRITE_TIFF_COMPRESSION_T43</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_T43</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_T43">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_NEXT">
<h3>IMWRITE_TIFF_COMPRESSION_NEXT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_NEXT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_NEXT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_CCITTRLEW">
<h3>IMWRITE_TIFF_COMPRESSION_CCITTRLEW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_CCITTRLEW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_CCITTRLEW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_PACKBITS">
<h3>IMWRITE_TIFF_COMPRESSION_PACKBITS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_PACKBITS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_PACKBITS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_THUNDERSCAN">
<h3>IMWRITE_TIFF_COMPRESSION_THUNDERSCAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_THUNDERSCAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_THUNDERSCAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_IT8CTPAD">
<h3>IMWRITE_TIFF_COMPRESSION_IT8CTPAD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_IT8CTPAD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_IT8CTPAD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_IT8LW">
<h3>IMWRITE_TIFF_COMPRESSION_IT8LW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_IT8LW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_IT8LW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_IT8MP">
<h3>IMWRITE_TIFF_COMPRESSION_IT8MP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_IT8MP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_IT8MP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_IT8BL">
<h3>IMWRITE_TIFF_COMPRESSION_IT8BL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_IT8BL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_IT8BL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_PIXARFILM">
<h3>IMWRITE_TIFF_COMPRESSION_PIXARFILM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_PIXARFILM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_PIXARFILM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_PIXARLOG">
<h3>IMWRITE_TIFF_COMPRESSION_PIXARLOG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_PIXARLOG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_PIXARLOG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_DEFLATE">
<h3>IMWRITE_TIFF_COMPRESSION_DEFLATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_DEFLATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_DEFLATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE">
<h3>IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_ADOBE_DEFLATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_DCS">
<h3>IMWRITE_TIFF_COMPRESSION_DCS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_DCS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_DCS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_JBIG">
<h3>IMWRITE_TIFF_COMPRESSION_JBIG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_JBIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_JBIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_SGILOG">
<h3>IMWRITE_TIFF_COMPRESSION_SGILOG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_SGILOG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_SGILOG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_SGILOG24">
<h3>IMWRITE_TIFF_COMPRESSION_SGILOG24</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_SGILOG24</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_SGILOG24">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_JP2000">
<h3>IMWRITE_TIFF_COMPRESSION_JP2000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_JP2000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_JP2000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_LERC">
<h3>IMWRITE_TIFF_COMPRESSION_LERC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_LERC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_LERC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_LZMA">
<h3>IMWRITE_TIFF_COMPRESSION_LZMA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_LZMA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_LZMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_ZSTD">
<h3>IMWRITE_TIFF_COMPRESSION_ZSTD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_ZSTD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_ZSTD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_WEBP">
<h3>IMWRITE_TIFF_COMPRESSION_WEBP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_WEBP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_WEBP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_COMPRESSION_JXL">
<h3>IMWRITE_TIFF_COMPRESSION_JXL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_COMPRESSION_JXL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION_JXL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_PREDICTOR_NONE">
<h3>IMWRITE_TIFF_PREDICTOR_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_PREDICTOR_NONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_PREDICTOR_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_PREDICTOR_HORIZONTAL">
<h3>IMWRITE_TIFF_PREDICTOR_HORIZONTAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_PREDICTOR_HORIZONTAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_PREDICTOR_HORIZONTAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT">
<h3>IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_PREDICTOR_FLOATINGPOINT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Imgcodecs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Imgcodecs</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="imread(java.lang.String,int)">
<h3>imread</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">imread</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;flags)</span></div>
<div class="block">Loads an image from a file.

  imread

 The <code>imread</code> function loads an image from the specified file and returns OpenCV matrix. If the image cannot be
 read (because of a missing file, improper permissions, or unsupported/invalid format), the function
 returns an empty matrix.

 Currently, the following file formats are supported:

 <ul>
   <li>
    Windows bitmaps - \*.bmp, \*.dib (always supported)
   </li>
   <li>
    GIF files - \*.gif (always supported)
   </li>
   <li>
    JPEG files - \*.jpeg, \*.jpg, \*.jpe (see the *Note* section)
   </li>
   <li>
    JPEG 2000 files - \*.jp2 (see the *Note* section)
   </li>
   <li>
    Portable Network Graphics - \*.png (see the *Note* section)
   </li>
   <li>
    WebP - \*.webp (see the *Note* section)
   </li>
   <li>
    AVIF - \*.avif (see the *Note* section)
   </li>
   <li>
    Portable image format - \*.pbm, \*.pgm, \*.ppm, \*.pxm, \*.pnm (always supported)
   </li>
   <li>
    PFM files - \*.pfm (see the *Note* section)
   </li>
   <li>
    Sun rasters - \*.sr, \*.ras (always supported)
   </li>
   <li>
    TIFF files - \*.tiff, \*.tif (see the *Note* section)
   </li>
   <li>
    OpenEXR Image files - \*.exr (see the *Note* section)
   </li>
   <li>
    Radiance HDR - \*.hdr, \*.pic (always supported)
   </li>
   <li>
    Raster and Vector geospatial data supported by GDAL (see the *Note* section)
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
    The function determines the type of an image by its content, not by the file extension.
   </li>
   <li>
    In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.
   </li>
   <li>
    When using IMREAD_GRAYSCALE, the codec's internal grayscale conversion will be used, if available.
     Results may differ from the output of cvtColor().
   </li>
   <li>
    On Microsoft Windows\* and Mac OS\*, the codecs shipped with OpenCV (libjpeg, libpng, libtiff,
     and libjasper) are used by default. So, OpenCV can always read JPEGs, PNGs, and TIFFs. On Mac OS,
     there is also an option to use native Mac OS image readers. However, beware that currently these
     native image loaders give images with different pixel values because of the color management embedded
     into Mac OS.
   </li>
   <li>
    On Linux\*, BSD flavors, and other Unix-like open-source operating systems, OpenCV looks for
     codecs supplied with the OS. Ensure the relevant packages are installed (including development
     files, such as "libjpeg-dev" in Debian\* and Ubuntu\*) to get codec support, or turn
     on the OPENCV_BUILD_3RDPARTY_LIBS flag in CMake.
   </li>
   <li>
    If the *WITH_GDAL* flag is set to true in CMake and REF: IMREAD_LOAD_GDAL is used to load the image,
     the [GDAL](http://www.gdal.org) driver will be used to decode the image, supporting
     [Raster](http://www.gdal.org/formats_list.html) and [Vector](http://www.gdal.org/ogr_formats.html) formats.
   </li>
   <li>
    If EXIF information is embedded in the image file, the EXIF orientation will be taken into account,
     and thus the image will be rotated accordingly unless the flags REF: IMREAD_IGNORE_ORIENTATION
     or REF: IMREAD_UNCHANGED are passed.
   </li>
   <li>
    Use the IMREAD_UNCHANGED flag to preserve the floating-point values from PFM images.
   </li>
   <li>
    By default, the number of pixels must be less than 2^30. This limit can be changed by setting
     the environment variable <code>OPENCV_IO_MAX_IMAGE_PIXELS</code>. See REF: tutorial_env_reference.
   </li>
 </ul></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file to be loaded.</dd>
<dd><code>flags</code> - Flag that can take values of <code>cv::ImreadModes</code>.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imread(java.lang.String)">
<h3>imread</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">imread</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Loads an image from a file.

  imread

 The <code>imread</code> function loads an image from the specified file and returns OpenCV matrix. If the image cannot be
 read (because of a missing file, improper permissions, or unsupported/invalid format), the function
 returns an empty matrix.

 Currently, the following file formats are supported:

 <ul>
   <li>
    Windows bitmaps - \*.bmp, \*.dib (always supported)
   </li>
   <li>
    GIF files - \*.gif (always supported)
   </li>
   <li>
    JPEG files - \*.jpeg, \*.jpg, \*.jpe (see the *Note* section)
   </li>
   <li>
    JPEG 2000 files - \*.jp2 (see the *Note* section)
   </li>
   <li>
    Portable Network Graphics - \*.png (see the *Note* section)
   </li>
   <li>
    WebP - \*.webp (see the *Note* section)
   </li>
   <li>
    AVIF - \*.avif (see the *Note* section)
   </li>
   <li>
    Portable image format - \*.pbm, \*.pgm, \*.ppm, \*.pxm, \*.pnm (always supported)
   </li>
   <li>
    PFM files - \*.pfm (see the *Note* section)
   </li>
   <li>
    Sun rasters - \*.sr, \*.ras (always supported)
   </li>
   <li>
    TIFF files - \*.tiff, \*.tif (see the *Note* section)
   </li>
   <li>
    OpenEXR Image files - \*.exr (see the *Note* section)
   </li>
   <li>
    Radiance HDR - \*.hdr, \*.pic (always supported)
   </li>
   <li>
    Raster and Vector geospatial data supported by GDAL (see the *Note* section)
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
    The function determines the type of an image by its content, not by the file extension.
   </li>
   <li>
    In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.
   </li>
   <li>
    When using IMREAD_GRAYSCALE, the codec's internal grayscale conversion will be used, if available.
     Results may differ from the output of cvtColor().
   </li>
   <li>
    On Microsoft Windows\* and Mac OS\*, the codecs shipped with OpenCV (libjpeg, libpng, libtiff,
     and libjasper) are used by default. So, OpenCV can always read JPEGs, PNGs, and TIFFs. On Mac OS,
     there is also an option to use native Mac OS image readers. However, beware that currently these
     native image loaders give images with different pixel values because of the color management embedded
     into Mac OS.
   </li>
   <li>
    On Linux\*, BSD flavors, and other Unix-like open-source operating systems, OpenCV looks for
     codecs supplied with the OS. Ensure the relevant packages are installed (including development
     files, such as "libjpeg-dev" in Debian\* and Ubuntu\*) to get codec support, or turn
     on the OPENCV_BUILD_3RDPARTY_LIBS flag in CMake.
   </li>
   <li>
    If the *WITH_GDAL* flag is set to true in CMake and REF: IMREAD_LOAD_GDAL is used to load the image,
     the [GDAL](http://www.gdal.org) driver will be used to decode the image, supporting
     [Raster](http://www.gdal.org/formats_list.html) and [Vector](http://www.gdal.org/ogr_formats.html) formats.
   </li>
   <li>
    If EXIF information is embedded in the image file, the EXIF orientation will be taken into account,
     and thus the image will be rotated accordingly unless the flags REF: IMREAD_IGNORE_ORIENTATION
     or REF: IMREAD_UNCHANGED are passed.
   </li>
   <li>
    Use the IMREAD_UNCHANGED flag to preserve the floating-point values from PFM images.
   </li>
   <li>
    By default, the number of pixels must be less than 2^30. This limit can be changed by setting
     the environment variable <code>OPENCV_IO_MAX_IMAGE_PIXELS</code>. See REF: tutorial_env_reference.
   </li>
 </ul></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file to be loaded.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imread(java.lang.String,org.opencv.core.Mat,int)">
<h3>imread</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">imread</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags)</span></div>
<div class="block">Loads an image from a file.

 This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts and the return value.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>dst</code> - object in which the image will be loaded.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes
 <b>Note:</b>
 The image passing through the img parameter can be pre-allocated. The memory is reused if the shape and the type match with the load image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imread(java.lang.String,org.opencv.core.Mat)">
<h3>imread</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">imread</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Loads an image from a file.

 This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts and the return value.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>dst</code> - object in which the image will be loaded.
 <b>Note:</b>
 The image passing through the img parameter can be pre-allocated. The memory is reused if the shape and the type match with the load image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadmulti(java.lang.String,java.util.List,int)">
<h3>imreadmulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadmulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;flags)</span></div>
<div class="block">Loads a multi-page image from a file.

 The function imreadmulti loads a multi-page image from the specified file into a vector of Mat objects.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.
 SEE: cv::imread</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadmulti(java.lang.String,java.util.List)">
<h3>imreadmulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadmulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</span></div>
<div class="block">Loads a multi-page image from a file.

 The function imreadmulti loads a multi-page image from the specified file into a vector of Mat objects.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.
 SEE: cv::imread</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadmulti(java.lang.String,java.util.List,int,int,int)">
<h3>imreadmulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadmulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;start,
 int&nbsp;count,
 int&nbsp;flags)</span></div>
<div class="block">Loads images of a multi-page image from a file.

 The function imreadmulti loads a specified range from a multi-page image from the specified file into a vector of Mat objects.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>start</code> - Start index of the image to load</dd>
<dd><code>count</code> - Count number of images to load</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.
 SEE: cv::imread</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadmulti(java.lang.String,java.util.List,int,int)">
<h3>imreadmulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadmulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 int&nbsp;start,
 int&nbsp;count)</span></div>
<div class="block">Loads images of a multi-page image from a file.

 The function imreadmulti loads a specified range from a multi-page image from the specified file into a vector of Mat objects.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>start</code> - Start index of the image to load</dd>
<dd><code>count</code> - Count number of images to load
 SEE: cv::imread</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation,int,int)">
<h3>imreadanimation</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadanimation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 int&nbsp;start,
 int&nbsp;count)</span></div>
<div class="block">Loads frames from an animated image file into an Animation structure.

 The function imreadanimation loads frames from an animated image file (e.g., GIF, AVIF, APNG, WEBP) into the provided Animation struct.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - A string containing the path to the file.</dd>
<dd><code>animation</code> - A reference to an Animation structure where the loaded frames will be stored. It should be initialized before the function is called.</dd>
<dd><code>start</code> - The index of the first frame to load. This is optional and defaults to 0.</dd>
<dd><code>count</code> - The number of frames to load. This is optional and defaults to 32767.</dd>
<dt>Returns:</dt>
<dd>Returns true if the file was successfully loaded and frames were extracted; returns false otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation,int)">
<h3>imreadanimation</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadanimation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 int&nbsp;start)</span></div>
<div class="block">Loads frames from an animated image file into an Animation structure.

 The function imreadanimation loads frames from an animated image file (e.g., GIF, AVIF, APNG, WEBP) into the provided Animation struct.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - A string containing the path to the file.</dd>
<dd><code>animation</code> - A reference to an Animation structure where the loaded frames will be stored. It should be initialized before the function is called.</dd>
<dd><code>start</code> - The index of the first frame to load. This is optional and defaults to 0.</dd>
<dt>Returns:</dt>
<dd>Returns true if the file was successfully loaded and frames were extracted; returns false otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imreadanimation(java.lang.String,org.opencv.imgcodecs.Animation)">
<h3>imreadanimation</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imreadanimation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation)</span></div>
<div class="block">Loads frames from an animated image file into an Animation structure.

 The function imreadanimation loads frames from an animated image file (e.g., GIF, AVIF, APNG, WEBP) into the provided Animation struct.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - A string containing the path to the file.</dd>
<dd><code>animation</code> - A reference to an Animation structure where the loaded frames will be stored. It should be initialized before the function is called.</dd>
<dt>Returns:</dt>
<dd>Returns true if the file was successfully loaded and frames were extracted; returns false otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imwriteanimation(java.lang.String,org.opencv.imgcodecs.Animation,org.opencv.core.MatOfInt)">
<h3>imwriteanimation</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwriteanimation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Saves an Animation to a specified file.

 The function imwriteanimation saves the provided Animation data to the specified file in an animated format.
 Supported formats depend on the implementation and may include formats like GIF, AVIF, APNG, or WEBP.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The name of the file where the animation will be saved. The file extension determines the format.</dd>
<dd><code>animation</code> - A constant reference to an Animation struct containing the frames and metadata to be saved.</dd>
<dd><code>params</code> - Optional format-specific parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ...).
 These parameters are used to specify additional options for the encoding process. Refer to <code>cv::ImwriteFlags</code> for details on possible parameters.</dd>
<dt>Returns:</dt>
<dd>Returns true if the animation was successfully saved; returns false otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imwriteanimation(java.lang.String,org.opencv.imgcodecs.Animation)">
<h3>imwriteanimation</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwriteanimation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="Animation.html" title="class in org.opencv.imgcodecs">Animation</a>&nbsp;animation)</span></div>
<div class="block">Saves an Animation to a specified file.

 The function imwriteanimation saves the provided Animation data to the specified file in an animated format.
 Supported formats depend on the implementation and may include formats like GIF, AVIF, APNG, or WEBP.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The name of the file where the animation will be saved. The file extension determines the format.</dd>
<dd><code>animation</code> - A constant reference to an Animation struct containing the frames and metadata to be saved.
 These parameters are used to specify additional options for the encoding process. Refer to <code>cv::ImwriteFlags</code> for details on possible parameters.</dd>
<dt>Returns:</dt>
<dd>Returns true if the animation was successfully saved; returns false otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imcount(java.lang.String,int)">
<h3>imcount</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">imcount</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;flags)</span></div>
<div class="block">Returns the number of images inside the given file

 The function imcount returns the number of pages in a multi-page image (e.g. TIFF), the number of frames in an animation (e.g. AVIF), and 1 otherwise.
 If the image cannot be decoded, 0 is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.
 TODO: when cv::IMREAD_LOAD_GDAL flag used the return value will be 0 or 1 because OpenCV's GDAL decoder doesn't support multi-page reading yet.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imcount(java.lang.String)">
<h3>imcount</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">imcount</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Returns the number of images inside the given file

 The function imcount returns the number of pages in a multi-page image (e.g. TIFF), the number of frames in an animation (e.g. AVIF), and 1 otherwise.
 If the image cannot be decoded, 0 is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of file to be loaded.
 TODO: when cv::IMREAD_LOAD_GDAL flag used the return value will be 0 or 1 because OpenCV's GDAL decoder doesn't support multi-page reading yet.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imwrite(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfInt)">
<h3>imwrite</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwrite</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Saves an image to a specified file.

 The function imwrite saves the image to the specified file. The image format is chosen based on the
 filename extension (see cv::imread for the list of extensions). In general, only 8-bit unsigned (CV_8U)
 single-channel or 3-channel (with 'BGR' channel order) images
 can be saved using this function, with these exceptions:

 <ul>
   <li>
  With OpenEXR encoder, only 32-bit float (CV_32F) images can be saved.
   <ul>
     <li>
    8-bit unsigned (CV_8U) images are not supported.
     </li>
   </ul>
   <li>
  With Radiance HDR encoder, non 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    All images will be converted to 32-bit float (CV_32F).
     </li>
   </ul>
   <li>
  With JPEG 2000 encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With JPEG XL encoder, 8-bit unsigned (CV_8U), 16-bit unsigned (CV_16U) and 32-bit float(CV_32F) images can be saved.
   <ul>
     <li>
    JPEG XL images with an alpha channel can be saved using this function.
     To do this, create 8-bit (or 16-bit, 32-bit float) 4-channel image BGRA, where the alpha channel goes last.
     Fully transparent pixels should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535/1.0.
     </li>
   </ul>
   <li>
  With PAM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PNG encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   <ul>
     <li>
    PNG images with an alpha channel can be saved using this function. To do this, create
     8-bit (or 16-bit) 4-channel image BGRA, where the alpha channel goes last. Fully transparent pixels
     should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535 (see the code sample below).
     </li>
   </ul>
   <li>
  With PGM/PPM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With TIFF encoder, 8-bit unsigned (CV_8U), 8-bit signed (CV_8S),
                      16-bit unsigned (CV_16U), 16-bit signed (CV_16S),
                      32-bit signed (CV_32S),
                      32-bit float (CV_32F) and 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    Multiple images (vector of Mat) can be saved in TIFF format (see the code sample below).
     </li>
     <li>
    32-bit float 3-channel (CV_32FC3) TIFF images will be saved
     using the LogLuv high dynamic range encoding (4 bytes per pixel)
     </li>
   </ul>

 If the image format is not supported, the image will be converted to 8-bit unsigned (CV_8U) and saved that way.
   </li>
 </ul>

 If the format, depth or channel order is different, use
 Mat::convertTo and cv::cvtColor to convert it before saving. Or, use the universal FileStorage I/O
 functions to save the image to XML or YAML format.

 The sample below shows how to create a BGRA image, how to set custom compression parameters and save it to a PNG file.
 It also demonstrates how to save multiple images in a TIFF file:
 INCLUDE: snippets/imgcodecs_imwrite.cpp</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file.</dd>
<dd><code>img</code> - (Mat or vector of Mat) Image or Images to be saved.</dd>
<dd><code>params</code> - Format-specific parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ... .) see cv::ImwriteFlags</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imwrite(java.lang.String,org.opencv.core.Mat)">
<h3>imwrite</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwrite</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</span></div>
<div class="block">Saves an image to a specified file.

 The function imwrite saves the image to the specified file. The image format is chosen based on the
 filename extension (see cv::imread for the list of extensions). In general, only 8-bit unsigned (CV_8U)
 single-channel or 3-channel (with 'BGR' channel order) images
 can be saved using this function, with these exceptions:

 <ul>
   <li>
  With OpenEXR encoder, only 32-bit float (CV_32F) images can be saved.
   <ul>
     <li>
    8-bit unsigned (CV_8U) images are not supported.
     </li>
   </ul>
   <li>
  With Radiance HDR encoder, non 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    All images will be converted to 32-bit float (CV_32F).
     </li>
   </ul>
   <li>
  With JPEG 2000 encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With JPEG XL encoder, 8-bit unsigned (CV_8U), 16-bit unsigned (CV_16U) and 32-bit float(CV_32F) images can be saved.
   <ul>
     <li>
    JPEG XL images with an alpha channel can be saved using this function.
     To do this, create 8-bit (or 16-bit, 32-bit float) 4-channel image BGRA, where the alpha channel goes last.
     Fully transparent pixels should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535/1.0.
     </li>
   </ul>
   <li>
  With PAM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PNG encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   <ul>
     <li>
    PNG images with an alpha channel can be saved using this function. To do this, create
     8-bit (or 16-bit) 4-channel image BGRA, where the alpha channel goes last. Fully transparent pixels
     should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535 (see the code sample below).
     </li>
   </ul>
   <li>
  With PGM/PPM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With TIFF encoder, 8-bit unsigned (CV_8U), 8-bit signed (CV_8S),
                      16-bit unsigned (CV_16U), 16-bit signed (CV_16S),
                      32-bit signed (CV_32S),
                      32-bit float (CV_32F) and 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    Multiple images (vector of Mat) can be saved in TIFF format (see the code sample below).
     </li>
     <li>
    32-bit float 3-channel (CV_32FC3) TIFF images will be saved
     using the LogLuv high dynamic range encoding (4 bytes per pixel)
     </li>
   </ul>

 If the image format is not supported, the image will be converted to 8-bit unsigned (CV_8U) and saved that way.
   </li>
 </ul>

 If the format, depth or channel order is different, use
 Mat::convertTo and cv::cvtColor to convert it before saving. Or, use the universal FileStorage I/O
 functions to save the image to XML or YAML format.

 The sample below shows how to create a BGRA image, how to set custom compression parameters and save it to a PNG file.
 It also demonstrates how to save multiple images in a TIFF file:
 INCLUDE: snippets/imgcodecs_imwrite.cpp</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file.</dd>
<dd><code>img</code> - (Mat or vector of Mat) Image or Images to be saved.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imwritemulti(java.lang.String,java.util.List,org.opencv.core.MatOfInt)">
<h3>imwritemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwritemulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
</section>
</li>
<li>
<section class="detail" id="imwritemulti(java.lang.String,java.util.List)">
<h3>imwritemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imwritemulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img)</span></div>
</section>
</li>
<li>
<section class="detail" id="imdecode(org.opencv.core.Mat,int)">
<h3>imdecode</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">imdecode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags)</span></div>
<div class="block">Reads an image from a buffer in memory.

 The function imdecode reads an image from the specified buffer in the memory. If the buffer is too short or
 contains invalid data, the function returns an empty matrix ( Mat::data==NULL ).

 See cv::imread for the list of supported formats and flags description.

 <b>Note:</b> In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buf</code> - Input array or vector of bytes.</dd>
<dd><code>flags</code> - The same flags as in cv::imread, see cv::ImreadModes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imdecodemulti(org.opencv.core.Mat,int,java.util.List,org.opencv.core.Range)">
<h3>imdecodemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imdecodemulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
 <a href="../core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;range)</span></div>
<div class="block">Reads a multi-page image from a buffer in memory.

 The function imdecodemulti reads a multi-page image from the specified buffer in the memory. If the buffer is too short or
 contains invalid data, the function returns false.

 See cv::imreadmulti for the list of supported formats and flags description.

 <b>Note:</b> In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buf</code> - Input array or vector of bytes.</dd>
<dd><code>flags</code> - The same flags as in cv::imread, see cv::ImreadModes.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page, if more than one.</dd>
<dd><code>range</code> - A continuous selection of pages.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imdecodemulti(org.opencv.core.Mat,int,java.util.List)">
<h3>imdecodemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imdecodemulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
 int&nbsp;flags,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</span></div>
<div class="block">Reads a multi-page image from a buffer in memory.

 The function imdecodemulti reads a multi-page image from the specified buffer in the memory. If the buffer is too short or
 contains invalid data, the function returns false.

 See cv::imreadmulti for the list of supported formats and flags description.

 <b>Note:</b> In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buf</code> - Input array or vector of bytes.</dd>
<dd><code>flags</code> - The same flags as in cv::imread, see cv::ImreadModes.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page, if more than one.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imencode(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfByte,org.opencv.core.MatOfInt)">
<h3>imencode</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imencode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Encodes an image into a memory buffer.

 The function imencode compresses the image and stores it in the memory buffer that is resized to fit the
 result. See cv::imwrite for the list of supported formats and flags description.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>img</code> - Image to be compressed.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed image.</dd>
<dd><code>params</code> - Format-specific parameters. See cv::imwrite and cv::ImwriteFlags.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imencode(java.lang.String,org.opencv.core.Mat,org.opencv.core.MatOfByte)">
<h3>imencode</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imencode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</span></div>
<div class="block">Encodes an image into a memory buffer.

 The function imencode compresses the image and stores it in the memory buffer that is resized to fit the
 result. See cv::imwrite for the list of supported formats and flags description.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>img</code> - Image to be compressed.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed image.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imencodemulti(java.lang.String,java.util.List,org.opencv.core.MatOfByte,org.opencv.core.MatOfInt)">
<h3>imencodemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imencodemulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgs,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Encodes array of images into a memory buffer.

 The function is analog to cv::imencode for in-memory multi-page image compression.
 See cv::imwrite for the list of supported formats and flags description.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>imgs</code> - Vector of images to be written.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed data.</dd>
<dd><code>params</code> - Format-specific parameters. See cv::imwrite and cv::ImwriteFlags.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="imencodemulti(java.lang.String,java.util.List,org.opencv.core.MatOfByte)">
<h3>imencodemulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">imencodemulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgs,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</span></div>
<div class="block">Encodes array of images into a memory buffer.

 The function is analog to cv::imencode for in-memory multi-page image compression.
 See cv::imwrite for the list of supported formats and flags description.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>imgs</code> - Vector of images to be written.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed data.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="haveImageReader(java.lang.String)">
<h3>haveImageReader</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">haveImageReader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Checks if the specified image file can be decoded by OpenCV.

 The function haveImageReader checks if OpenCV is capable of reading the specified file.
 This can be useful for verifying support for a given image format before attempting to load an image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The name of the file to be checked.</dd>
<dt>Returns:</dt>
<dd>true if an image reader for the specified file is available and the file can be opened, false otherwise.

 <b>Note:</b> The function checks the availability of image codecs that are either built into OpenCV or dynamically loaded.
 It does not check for the actual existence of the file but rather the ability to read the specified file type.
 If the file cannot be opened or the format is unsupported, the function will return false.

 SEE: cv::haveImageWriter, cv::imread, cv::imdecode</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="haveImageWriter(java.lang.String)">
<h3>haveImageWriter</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">haveImageWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Checks if the specified image file or specified file extension can be encoded by OpenCV.

 The function haveImageWriter checks if OpenCV is capable of writing images with the specified file extension.
 This can be useful for verifying support for a given image format before attempting to save an image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The name of the file or the file extension (e.g., ".jpg", ".png").
 It is recommended to provide the file extension rather than the full file name.</dd>
<dt>Returns:</dt>
<dd>true if an image writer for the specified extension is available, false otherwise.

 <b>Note:</b> The function checks the availability of image codecs that are either built into OpenCV or dynamically loaded.
 It does not check for the actual existence of the file but rather the ability to write files of the given type.

 SEE: cv::haveImageReader, cv::imwrite, cv::imencode</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
