<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>IntelligentScissorsMB (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.imgproc, class: IntelligentScissorsMB">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.imgproc</a></div>
<h1 title="Class IntelligentScissorsMB" class="title">Class IntelligentScissorsMB</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.imgproc.IntelligentScissorsMB</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">IntelligentScissorsMB</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Intelligent Scissors image segmentation

 This class is used to find the path (contour) between two points
 which can be used for image segmentation.

 Usage example:
 SNIPPET: snippets/imgproc_segmentation.cpp usage_example_intelligent_scissors

 Reference: &lt;a href="http://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.3811&amp;rep=rep1&amp;type=pdf"&gt;"Intelligent Scissors for Image Composition"&lt;/a&gt;
 algorithm designed by Eric N. Mortensen and William A. Barrett, Brigham Young University
 CITE: Mortensen95intelligentscissors</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">IntelligentScissorsMB</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#applyImage(org.opencv.core.Mat)" class="member-name-link">applyImage</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify input image and extract image features</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#applyImageFeatures(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">applyImageFeatures</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify custom features of input image

 Customized advanced variant of applyImage() call.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#applyImageFeatures(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">applyImageFeatures</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify custom features of input image

 Customized advanced variant of applyImage() call.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildMap(org.opencv.core.Point)" class="member-name-link">buildMap</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;sourcePt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Prepares a map of optimal paths for the given source point on the image

 <b>Note:</b> applyImage() / applyImageFeatures() must be called before this call</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getContour(org.opencv.core.Point,org.opencv.core.Mat)" class="member-name-link">getContour</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getContour(org.opencv.core.Point,org.opencv.core.Mat,boolean)" class="member-name-link">getContour</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour,
 boolean&nbsp;backward)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeFeatureCannyParameters(double,double)" class="member-name-link">setEdgeFeatureCannyParameters</a><wbr>(double&nbsp;threshold1,
 double&nbsp;threshold2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeFeatureCannyParameters(double,double,int)" class="member-name-link">setEdgeFeatureCannyParameters</a><wbr>(double&nbsp;threshold1,
 double&nbsp;threshold2,
 int&nbsp;apertureSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeFeatureCannyParameters(double,double,int,boolean)" class="member-name-link">setEdgeFeatureCannyParameters</a><wbr>(double&nbsp;threshold1,
 double&nbsp;threshold2,
 int&nbsp;apertureSize,
 boolean&nbsp;L2gradient)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeFeatureZeroCrossingParameters()" class="member-name-link">setEdgeFeatureZeroCrossingParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeFeatureZeroCrossingParameters(float)" class="member-name-link">setEdgeFeatureZeroCrossingParameters</a><wbr>(float&nbsp;gradient_magnitude_min_value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGradientMagnitudeMaxLimit()" class="member-name-link">setGradientMagnitudeMaxLimit</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGradientMagnitudeMaxLimit(float)" class="member-name-link">setGradientMagnitudeMaxLimit</a><wbr>(float&nbsp;gradient_magnitude_threshold_max)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWeights(float,float,float)" class="member-name-link">setWeights</a><wbr>(float&nbsp;weight_non_edge,
 float&nbsp;weight_gradient_direction,
 float&nbsp;weight_gradient_magnitude)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify weights of feature functions

 Consider keeping weights normalized (sum of weights equals to 1.0)
 Discrete dynamic programming (DP) goal is minimization of costs between pixels.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>IntelligentScissorsMB</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">IntelligentScissorsMB</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setWeights(float,float,float)">
<h3>setWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setWeights</span><wbr><span class="parameters">(float&nbsp;weight_non_edge,
 float&nbsp;weight_gradient_direction,
 float&nbsp;weight_gradient_magnitude)</span></div>
<div class="block">Specify weights of feature functions

 Consider keeping weights normalized (sum of weights equals to 1.0)
 Discrete dynamic programming (DP) goal is minimization of costs between pixels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>weight_non_edge</code> - Specify cost of non-edge pixels (default: 0.43f)</dd>
<dd><code>weight_gradient_direction</code> - Specify cost of gradient direction function (default: 0.43f)</dd>
<dd><code>weight_gradient_magnitude</code> - Specify cost of gradient magnitude function (default: 0.14f)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGradientMagnitudeMaxLimit(float)">
<h3>setGradientMagnitudeMaxLimit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setGradientMagnitudeMaxLimit</span><wbr><span class="parameters">(float&nbsp;gradient_magnitude_threshold_max)</span></div>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).
 Otherwize pixels with <code>gradient magnitude &amp;gt;= threshold</code> have zero cost.

 <b>Note:</b> Thresholding should be used for images with irregular regions (to avoid stuck on parameters from high-contract areas, like embedded logos).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gradient_magnitude_threshold_max</code> - Specify gradient magnitude max value threshold (default: 0, disabled)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGradientMagnitudeMaxLimit()">
<h3>setGradientMagnitudeMaxLimit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setGradientMagnitudeMaxLimit</span>()</div>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).
 Otherwize pixels with <code>gradient magnitude &amp;gt;= threshold</code> have zero cost.

 <b>Note:</b> Thresholding should be used for images with irregular regions (to avoid stuck on parameters from high-contract areas, like embedded logos).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEdgeFeatureZeroCrossingParameters(float)">
<h3>setEdgeFeatureZeroCrossingParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setEdgeFeatureZeroCrossingParameters</span><wbr><span class="parameters">(float&nbsp;gradient_magnitude_min_value)</span></div>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.

 Implementation has additional filtering for regions with low-amplitude noise.
 This filtering is enabled through parameter of minimal gradient amplitude (use some small value 4, 8, 16).

 <b>Note:</b> Current implementation of this feature extractor is based on processing of grayscale images (color image is converted to grayscale image first).

 <b>Note:</b> Canny edge detector is a bit slower, but provides better results (especially on color images): use setEdgeFeatureCannyParameters().</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gradient_magnitude_min_value</code> - Minimal gradient magnitude value for edge pixels (default: 0, check is disabled)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEdgeFeatureZeroCrossingParameters()">
<h3>setEdgeFeatureZeroCrossingParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setEdgeFeatureZeroCrossingParameters</span>()</div>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.

 Implementation has additional filtering for regions with low-amplitude noise.
 This filtering is enabled through parameter of minimal gradient amplitude (use some small value 4, 8, 16).

 <b>Note:</b> Current implementation of this feature extractor is based on processing of grayscale images (color image is converted to grayscale image first).

 <b>Note:</b> Canny edge detector is a bit slower, but provides better results (especially on color images): use setEdgeFeatureCannyParameters().</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEdgeFeatureCannyParameters(double,double,int,boolean)">
<h3>setEdgeFeatureCannyParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setEdgeFeatureCannyParameters</span><wbr><span class="parameters">(double&nbsp;threshold1,
 double&nbsp;threshold2,
 int&nbsp;apertureSize,
 boolean&nbsp;L2gradient)</span></div>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dd><code>apertureSize</code> - automatically generated</dd>
<dd><code>L2gradient</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEdgeFeatureCannyParameters(double,double,int)">
<h3>setEdgeFeatureCannyParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setEdgeFeatureCannyParameters</span><wbr><span class="parameters">(double&nbsp;threshold1,
 double&nbsp;threshold2,
 int&nbsp;apertureSize)</span></div>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dd><code>apertureSize</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEdgeFeatureCannyParameters(double,double)">
<h3>setEdgeFeatureCannyParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">setEdgeFeatureCannyParameters</span><wbr><span class="parameters">(double&nbsp;threshold1,
 double&nbsp;threshold2)</span></div>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyImage(org.opencv.core.Mat)">
<h3>applyImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">applyImage</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Specify input image and extract image features</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image. Type is #CV_8UC1 / #CV_8UC3</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyImageFeatures(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>applyImageFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">applyImageFeatures</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Specify custom features of input image

 Customized advanced variant of applyImage() call.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>non_edge</code> - Specify cost of non-edge pixels. Type is CV_8UC1. Expected values are <code>{0, 1}</code>.</dd>
<dd><code>gradient_direction</code> - Specify gradient direction feature. Type is CV_32FC2. Values are expected to be normalized: <code>x^2 + y^2 == 1</code></dd>
<dd><code>gradient_magnitude</code> - Specify cost of gradient magnitude function: Type is CV_32FC1. Values should be in range <code>[0, 1]</code>.</dd>
<dd><code>image</code> - <b>Optional parameter</b>. Must be specified if subset of features is specified (non-specified features are calculated internally)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyImageFeatures(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>applyImageFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></span>&nbsp;<span class="element-name">applyImageFeatures</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude)</span></div>
<div class="block">Specify custom features of input image

 Customized advanced variant of applyImage() call.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>non_edge</code> - Specify cost of non-edge pixels. Type is CV_8UC1. Expected values are <code>{0, 1}</code>.</dd>
<dd><code>gradient_direction</code> - Specify gradient direction feature. Type is CV_32FC2. Values are expected to be normalized: <code>x^2 + y^2 == 1</code></dd>
<dd><code>gradient_magnitude</code> - Specify cost of gradient magnitude function: Type is CV_32FC1. Values should be in range <code>[0, 1]</code>.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildMap(org.opencv.core.Point)">
<h3>buildMap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildMap</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;sourcePt)</span></div>
<div class="block">Prepares a map of optimal paths for the given source point on the image

 <b>Note:</b> applyImage() / applyImageFeatures() must be called before this call</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourcePt</code> - The source point used to find the paths</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getContour(org.opencv.core.Point,org.opencv.core.Mat,boolean)">
<h3>getContour</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getContour</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour,
 boolean&nbsp;backward)</span></div>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetPt</code> - The target point</dd>
<dd><code>contour</code> - The list of pixels which contains optimal path between the source and the target points of the image. Type is CV_32SC2 (compatible with <code>std::vector&amp;lt;Point&amp;gt;</code>)</dd>
<dd><code>backward</code> - Flag to indicate reverse order of retrieved pixels (use "true" value to fetch points from the target to the source point)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getContour(org.opencv.core.Point,org.opencv.core.Mat)">
<h3>getContour</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getContour</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour)</span></div>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetPt</code> - The target point</dd>
<dd><code>contour</code> - The list of pixels which contains optimal path between the source and the target points of the image. Type is CV_32SC2 (compatible with <code>std::vector&amp;lt;Point&amp;gt;</code>)</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
