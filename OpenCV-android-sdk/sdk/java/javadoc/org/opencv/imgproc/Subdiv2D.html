<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Subdiv2D (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.imgproc, class: Subdiv2D">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.imgproc</a></div>
<h1 title="Class Subdiv2D" class="title">Class Subdiv2D</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.imgproc.Subdiv2D</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Subdiv2D</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NEXT_AROUND_DST" class="member-name-link">NEXT_AROUND_DST</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NEXT_AROUND_LEFT" class="member-name-link">NEXT_AROUND_LEFT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NEXT_AROUND_ORG" class="member-name-link">NEXT_AROUND_ORG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NEXT_AROUND_RIGHT" class="member-name-link">NEXT_AROUND_RIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PREV_AROUND_DST" class="member-name-link">PREV_AROUND_DST</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PREV_AROUND_LEFT" class="member-name-link">PREV_AROUND_LEFT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PREV_AROUND_ORG" class="member-name-link">PREV_AROUND_ORG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PREV_AROUND_RIGHT" class="member-name-link">PREV_AROUND_RIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PTLOC_ERROR" class="member-name-link">PTLOC_ERROR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PTLOC_INSIDE" class="member-name-link">PTLOC_INSIDE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PTLOC_ON_EDGE" class="member-name-link">PTLOC_ON_EDGE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PTLOC_OUTSIDE_RECT" class="member-name-link">PTLOC_OUTSIDE_RECT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PTLOC_VERTEX" class="member-name-link">PTLOC_VERTEX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Subdiv2D</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">creates an empty Subdiv2D object.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Rect)" class="member-name-link">Subdiv2D</a><wbr>(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Subdiv2D.html" title="class in org.opencv.imgproc">Subdiv2D</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#edgeDst(int)" class="member-name-link">edgeDst</a><wbr>(int&nbsp;edge)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the edge destination.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#edgeDst(int,org.opencv.core.Point)" class="member-name-link">edgeDst</a><wbr>(int&nbsp;edge,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;dstpt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the edge destination.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#edgeOrg(int)" class="member-name-link">edgeOrg</a><wbr>(int&nbsp;edge)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the edge origin.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#edgeOrg(int,org.opencv.core.Point)" class="member-name-link">edgeOrg</a><wbr>(int&nbsp;edge,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;orgpt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the edge origin.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findNearest(org.opencv.core.Point)" class="member-name-link">findNearest</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the subdivision vertex closest to the given point.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findNearest(org.opencv.core.Point,org.opencv.core.Point)" class="member-name-link">findNearest</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;nearestPt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the subdivision vertex closest to the given point.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEdge(int,int)" class="member-name-link">getEdge</a><wbr>(int&nbsp;edge,
 int&nbsp;nextEdgeType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns one of the edges related to the given edge.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEdgeList(org.opencv.core.MatOfFloat4)" class="member-name-link">getEdgeList</a><wbr>(<a href="../core/MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a>&nbsp;edgeList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of all edges.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLeadingEdgeList(org.opencv.core.MatOfInt)" class="member-name-link">getLeadingEdgeList</a><wbr>(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;leadingEdgeList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of the leading edge ID connected to each triangle.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTriangleList(org.opencv.core.MatOfFloat6)" class="member-name-link">getTriangleList</a><wbr>(<a href="../core/MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a>&nbsp;triangleList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of all triangles.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Point.html" title="class in org.opencv.core">Point</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVertex(int)" class="member-name-link">getVertex</a><wbr>(int&nbsp;vertex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns vertex location from vertex ID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Point.html" title="class in org.opencv.core">Point</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVertex(int,int%5B%5D)" class="member-name-link">getVertex</a><wbr>(int&nbsp;vertex,
 int[]&nbsp;firstEdge)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns vertex location from vertex ID.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVoronoiFacetList(org.opencv.core.MatOfInt,java.util.List,org.opencv.core.MatOfPoint2f)" class="member-name-link">getVoronoiFacetList</a><wbr>(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;idx,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;facetList,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;facetCenters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of all Voronoi facets.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initDelaunay(org.opencv.core.Rect)" class="member-name-link">initDelaunay</a><wbr>(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new empty Delaunay subdivision</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#insert(org.opencv.core.MatOfPoint2f)" class="member-name-link">insert</a><wbr>(<a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;ptvec)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Insert multiple points into a Delaunay triangulation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#insert(org.opencv.core.Point)" class="member-name-link">insert</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Insert a single point into a Delaunay triangulation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#locate(org.opencv.core.Point,int%5B%5D,int%5B%5D)" class="member-name-link">locate</a><wbr>(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
 int[]&nbsp;edge,
 int[]&nbsp;vertex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the location of a point within a Delaunay triangulation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#nextEdge(int)" class="member-name-link">nextEdge</a><wbr>(int&nbsp;edge)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns next edge around the edge origin.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rotateEdge(int,int)" class="member-name-link">rotateEdge</a><wbr>(int&nbsp;edge,
 int&nbsp;rotate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns another edge of the same quad-edge.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#symEdge(int)" class="member-name-link">symEdge</a><wbr>(int&nbsp;edge)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PTLOC_ERROR">
<h3>PTLOC_ERROR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PTLOC_ERROR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_ERROR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PTLOC_OUTSIDE_RECT">
<h3>PTLOC_OUTSIDE_RECT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PTLOC_OUTSIDE_RECT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_OUTSIDE_RECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PTLOC_INSIDE">
<h3>PTLOC_INSIDE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PTLOC_INSIDE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_INSIDE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PTLOC_VERTEX">
<h3>PTLOC_VERTEX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PTLOC_VERTEX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_VERTEX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PTLOC_ON_EDGE">
<h3>PTLOC_ON_EDGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PTLOC_ON_EDGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_ON_EDGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NEXT_AROUND_ORG">
<h3>NEXT_AROUND_ORG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NEXT_AROUND_ORG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_ORG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NEXT_AROUND_DST">
<h3>NEXT_AROUND_DST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NEXT_AROUND_DST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_DST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREV_AROUND_ORG">
<h3>PREV_AROUND_ORG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREV_AROUND_ORG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_ORG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREV_AROUND_DST">
<h3>PREV_AROUND_DST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREV_AROUND_DST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_DST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NEXT_AROUND_LEFT">
<h3>NEXT_AROUND_LEFT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NEXT_AROUND_LEFT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_LEFT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NEXT_AROUND_RIGHT">
<h3>NEXT_AROUND_RIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NEXT_AROUND_RIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_RIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREV_AROUND_LEFT">
<h3>PREV_AROUND_LEFT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREV_AROUND_LEFT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_LEFT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREV_AROUND_RIGHT">
<h3>PREV_AROUND_RIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREV_AROUND_RIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_RIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Subdiv2D</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Subdiv2D</span>()</div>
<div class="block">creates an empty Subdiv2D object.
     To create a new empty Delaunay subdivision you need to use the #initDelaunay function.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Rect)">
<h3>Subdiv2D</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Subdiv2D</span><wbr><span class="parameters">(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rect</code> - Rectangle that includes all of the 2D points that are to be added to the subdivision.

     The function creates an empty Delaunay subdivision where 2D points can be added using the function
     insert() . All of the points to be added must be within the specified rectangle, otherwise a runtime
     error is raised.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Subdiv2D.html" title="class in org.opencv.imgproc">Subdiv2D</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="initDelaunay(org.opencv.core.Rect)">
<h3>initDelaunay</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initDelaunay</span><wbr><span class="parameters">(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</span></div>
<div class="block">Creates a new empty Delaunay subdivision</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rect</code> - Rectangle that includes all of the 2D points that are to be added to the subdivision.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="insert(org.opencv.core.Point)">
<h3>insert</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">insert</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</span></div>
<div class="block">Insert a single point into a Delaunay triangulation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pt</code> - Point to insert.

     The function inserts a single point into a subdivision and modifies the subdivision topology
     appropriately. If a point with the same coordinates exists already, no new point is added.</dd>
<dt>Returns:</dt>
<dd>the ID of the point.

     <b>Note:</b> If the point is outside of the triangulation specified rect a runtime error is raised.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="insert(org.opencv.core.MatOfPoint2f)">
<h3>insert</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">insert</span><wbr><span class="parameters">(<a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;ptvec)</span></div>
<div class="block">Insert multiple points into a Delaunay triangulation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ptvec</code> - Points to insert.

     The function inserts a vector of points into a subdivision and modifies the subdivision topology
     appropriately.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="locate(org.opencv.core.Point,int[],int[])">
<h3>locate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">locate</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
 int[]&nbsp;edge,
 int[]&nbsp;vertex)</span></div>
<div class="block">Returns the location of a point within a Delaunay triangulation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pt</code> - Point to locate.</dd>
<dd><code>edge</code> - Output edge that the point belongs to or is located to the right of it.</dd>
<dd><code>vertex</code> - Optional output vertex the input point coincides with.

     The function locates the input point within the subdivision and gives one of the triangle edges
     or vertices.</dd>
<dt>Returns:</dt>
<dd>an integer which specify one of the following five cases for point location:
 <ul>
   <li>
       The point falls into some facet. The function returns #PTLOC_INSIDE and edge will contain one of
        edges of the facet.
   </li>
   <li>
       The point falls onto the edge. The function returns #PTLOC_ON_EDGE and edge will contain this edge.
   </li>
   <li>
       The point coincides with one of the subdivision vertices. The function returns #PTLOC_VERTEX and
        vertex will contain a pointer to the vertex.
   </li>
   <li>
       The point is outside the subdivision reference rectangle. The function returns #PTLOC_OUTSIDE_RECT
        and no pointers are filled.
   </li>
   <li>
       One of input arguments is invalid. A runtime error is raised or, if silent or "parent" error
        processing mode is selected, #PTLOC_ERROR is returned.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findNearest(org.opencv.core.Point,org.opencv.core.Point)">
<h3>findNearest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">findNearest</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;nearestPt)</span></div>
<div class="block">Finds the subdivision vertex closest to the given point.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pt</code> - Input point.</dd>
<dd><code>nearestPt</code> - Output subdivision vertex point.

     The function is another function that locates the input point within the subdivision. It finds the
     subdivision vertex that is the closest to the input point. It is not necessarily one of vertices
     of the facet containing the input point, though the facet (located using locate() ) is used as a
     starting point.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findNearest(org.opencv.core.Point)">
<h3>findNearest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">findNearest</span><wbr><span class="parameters">(<a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</span></div>
<div class="block">Finds the subdivision vertex closest to the given point.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pt</code> - Input point.

     The function is another function that locates the input point within the subdivision. It finds the
     subdivision vertex that is the closest to the input point. It is not necessarily one of vertices
     of the facet containing the input point, though the facet (located using locate() ) is used as a
     starting point.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEdgeList(org.opencv.core.MatOfFloat4)">
<h3>getEdgeList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getEdgeList</span><wbr><span class="parameters">(<a href="../core/MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a>&nbsp;edgeList)</span></div>
<div class="block">Returns a list of all edges.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edgeList</code> - Output vector.

     The function gives each edge as a 4 numbers vector, where each two are one of the edge
     vertices. i.e. org_x = v[0], org_y = v[1], dst_x = v[2], dst_y = v[3].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLeadingEdgeList(org.opencv.core.MatOfInt)">
<h3>getLeadingEdgeList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getLeadingEdgeList</span><wbr><span class="parameters">(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;leadingEdgeList)</span></div>
<div class="block">Returns a list of the leading edge ID connected to each triangle.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>leadingEdgeList</code> - Output vector.

     The function gives one edge ID for each triangle.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTriangleList(org.opencv.core.MatOfFloat6)">
<h3>getTriangleList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getTriangleList</span><wbr><span class="parameters">(<a href="../core/MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a>&nbsp;triangleList)</span></div>
<div class="block">Returns a list of all triangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>triangleList</code> - Output vector.

     The function gives each triangle as a 6 numbers vector, where each two are one of the triangle
     vertices. i.e. p1_x = v[0], p1_y = v[1], p2_x = v[2], p2_y = v[3], p3_x = v[4], p3_y = v[5].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVoronoiFacetList(org.opencv.core.MatOfInt,java.util.List,org.opencv.core.MatOfPoint2f)">
<h3>getVoronoiFacetList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getVoronoiFacetList</span><wbr><span class="parameters">(<a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;idx,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;facetList,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;facetCenters)</span></div>
<div class="block">Returns a list of all Voronoi facets.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>idx</code> - Vector of vertices IDs to consider. For all vertices you can pass empty vector.</dd>
<dd><code>facetList</code> - Output vector of the Voronoi facets.</dd>
<dd><code>facetCenters</code> - Output vector of the Voronoi facets center points.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVertex(int,int[])">
<h3>getVertex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Point.html" title="class in org.opencv.core">Point</a></span>&nbsp;<span class="element-name">getVertex</span><wbr><span class="parameters">(int&nbsp;vertex,
 int[]&nbsp;firstEdge)</span></div>
<div class="block">Returns vertex location from vertex ID.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vertex</code> - vertex ID.</dd>
<dd><code>firstEdge</code> - Optional. The first edge ID which is connected to the vertex.</dd>
<dt>Returns:</dt>
<dd>vertex (x,y)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVertex(int)">
<h3>getVertex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Point.html" title="class in org.opencv.core">Point</a></span>&nbsp;<span class="element-name">getVertex</span><wbr><span class="parameters">(int&nbsp;vertex)</span></div>
<div class="block">Returns vertex location from vertex ID.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vertex</code> - vertex ID.</dd>
<dt>Returns:</dt>
<dd>vertex (x,y)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEdge(int,int)">
<h3>getEdge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getEdge</span><wbr><span class="parameters">(int&nbsp;edge,
 int&nbsp;nextEdgeType)</span></div>
<div class="block">Returns one of the edges related to the given edge.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dd><code>nextEdgeType</code> - Parameter specifying which of the related edges to return.
     The following values are possible:
 <ul>
   <li>
        NEXT_AROUND_ORG next around the edge origin ( eOnext on the picture below if e is the input edge)
   </li>
   <li>
        NEXT_AROUND_DST next around the edge vertex ( eDnext )
   </li>
   <li>
        PREV_AROUND_ORG previous around the edge origin (reversed eRnext )
   </li>
   <li>
        PREV_AROUND_DST previous around the edge destination (reversed eLnext )
   </li>
   <li>
        NEXT_AROUND_LEFT next around the left facet ( eLnext )
   </li>
   <li>
        NEXT_AROUND_RIGHT next around the right facet ( eRnext )
   </li>
   <li>
        PREV_AROUND_LEFT previous around the left facet (reversed eOnext )
   </li>
   <li>
        PREV_AROUND_RIGHT previous around the right facet (reversed eDnext )
   </li>
 </ul>

     ![sample output](pics/quadedge.png)</dd>
<dt>Returns:</dt>
<dd>edge ID related to the input edge.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nextEdge(int)">
<h3>nextEdge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">nextEdge</span><wbr><span class="parameters">(int&nbsp;edge)</span></div>
<div class="block">Returns next edge around the edge origin.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dt>Returns:</dt>
<dd>an integer which is next edge ID around the edge origin: eOnext on the
     picture above if e is the input edge).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="rotateEdge(int,int)">
<h3>rotateEdge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">rotateEdge</span><wbr><span class="parameters">(int&nbsp;edge,
 int&nbsp;rotate)</span></div>
<div class="block">Returns another edge of the same quad-edge.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dd><code>rotate</code> - Parameter specifying which of the edges of the same quad-edge as the input
     one to return. The following values are possible:
 <ul>
   <li>
        0 - the input edge ( e on the picture below if e is the input edge)
   </li>
   <li>
        1 - the rotated edge ( eRot )
   </li>
   <li>
        2 - the reversed edge (reversed e (in green))
   </li>
   <li>
        3 - the reversed rotated edge (reversed eRot (in green))
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>one of the edges ID of the same quad-edge as the input edge.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="symEdge(int)">
<h3>symEdge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">symEdge</span><wbr><span class="parameters">(int&nbsp;edge)</span></div>
</section>
</li>
<li>
<section class="detail" id="edgeOrg(int,org.opencv.core.Point)">
<h3>edgeOrg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">edgeOrg</span><wbr><span class="parameters">(int&nbsp;edge,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;orgpt)</span></div>
<div class="block">Returns the edge origin.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dd><code>orgpt</code> - Output vertex location.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgeOrg(int)">
<h3>edgeOrg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">edgeOrg</span><wbr><span class="parameters">(int&nbsp;edge)</span></div>
<div class="block">Returns the edge origin.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgeDst(int,org.opencv.core.Point)">
<h3>edgeDst</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">edgeDst</span><wbr><span class="parameters">(int&nbsp;edge,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;dstpt)</span></div>
<div class="block">Returns the edge destination.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dd><code>dstpt</code> - Output vertex location.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgeDst(int)">
<h3>edgeDst</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">edgeDst</span><wbr><span class="parameters">(int&nbsp;edge)</span></div>
<div class="block">Returns the edge destination.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>edge</code> - Subdivision edge ID.</dd>
<dt>Returns:</dt>
<dd>vertex ID.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
