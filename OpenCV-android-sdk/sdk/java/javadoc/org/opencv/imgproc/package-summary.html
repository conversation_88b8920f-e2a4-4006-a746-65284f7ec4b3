<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.imgproc (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.imgproc">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.imgproc" class="title">Package org.opencv.imgproc</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.imgproc</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CLAHE.html" title="class in org.opencv.imgproc">CLAHE</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for Contrast Limited Adaptive Histogram Equalization.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GeneralizedHough.html" title="class in org.opencv.imgproc">GeneralizedHough</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">finds arbitrary template in the grayscale image using Generalized Hough Transform</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GeneralizedHoughBallard.html" title="class in org.opencv.imgproc">GeneralizedHoughBallard</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">finds arbitrary template in the grayscale image using Generalized Hough Transform

 Detects position only without translation and rotation CITE: Ballard1981 .</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GeneralizedHoughGuil.html" title="class in org.opencv.imgproc">GeneralizedHoughGuil</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">finds arbitrary template in the grayscale image using Generalized Hough Transform

 Detects position, translation and rotation CITE: Guil1999 .</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Imgproc.html" title="class in org.opencv.imgproc">Imgproc</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Intelligent Scissors image segmentation

 This class is used to find the path (contour) between two points
 which can be used for image segmentation.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LineSegmentDetector.html" title="class in org.opencv.imgproc">LineSegmentDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Line segment detector class

 following the algorithm described at CITE: Rafael12 .</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Moments.html" title="class in org.opencv.imgproc">Moments</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Subdiv2D.html" title="class in org.opencv.imgproc">Subdiv2D</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
