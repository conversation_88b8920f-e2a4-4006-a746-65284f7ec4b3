<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>LineSegmentDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.imgproc, class: LineSegmentDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.imgproc</a></div>
<h1 title="Class LineSegmentDetector" class="title">Class LineSegmentDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance">org.opencv.imgproc.LineSegmentDetector</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">LineSegmentDetector</span>
<span class="extends-implements">extends <a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></span></div>
<div class="block">Line segment detector class

 following the algorithm described at CITE: Rafael12 .

 <b>Note:</b> Implementation has been removed from OpenCV version 3.4.6 to 3.4.15 and version 4.1.0 to 4.5.3 due original code license conflict.
 restored again after [Computation of a NFA](https://github.com/rafael-grompone-von-gioi/binomial_nfa) code published under the MIT license.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="LineSegmentDetector.html" title="class in org.opencv.imgproc">LineSegmentDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compareSegments(org.opencv.core.Size,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">compareSegments</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines2)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draws two groups of lines in blue and red, counting the non overlapping (mismatching) pixels.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compareSegments(org.opencv.core.Size,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">compareSegments</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draws two groups of lines in blue and red, counting the non overlapping (mismatching) pixels.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds lines in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds lines in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prec)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds lines in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prec,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nfa)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds lines in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#drawSegments(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">drawSegments</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draws the line segments on a given image.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="LineSegmentDetector.html" title="class in org.opencv.imgproc">LineSegmentDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prec,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nfa)</span></div>
<div class="block">Finds lines in the input image.

     This is the output of the default parameters of the algorithm on the above shown image.

     ![image](pics/building_lsd.png)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - A grayscale (CV_8UC1) input image. If only a roi needs to be selected, use:
     <code>lsd_ptr-&amp;gt;detect(image(roi), lines, ...); lines += Scalar(roi.x, roi.y, roi.x, roi.y);</code></dd>
<dd><code>lines</code> - A vector of Vec4f elements specifying the beginning and ending point of a line. Where
     Vec4f is (x1, y1, x2, y2), point 1 is the start, point 2 - end. Returned lines are strictly
     oriented depending on the gradient.</dd>
<dd><code>width</code> - Vector of widths of the regions, where the lines are found. E.g. Width of line.</dd>
<dd><code>prec</code> - Vector of precisions with which the lines are found.</dd>
<dd><code>nfa</code> - Vector containing number of false alarms in the line region, with precision of 10%. The
     bigger the value, logarithmically better the detection.
 <ul>
   <li>
      -1 corresponds to 10 mean false alarms
   </li>
   <li>
      0 corresponds to 1 mean false alarm
   </li>
   <li>
      1 corresponds to 0.1 mean false alarms
     This vector will be calculated only when the objects type is #LSD_REFINE_ADV.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prec)</span></div>
<div class="block">Finds lines in the input image.

     This is the output of the default parameters of the algorithm on the above shown image.

     ![image](pics/building_lsd.png)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - A grayscale (CV_8UC1) input image. If only a roi needs to be selected, use:
     <code>lsd_ptr-&amp;gt;detect(image(roi), lines, ...); lines += Scalar(roi.x, roi.y, roi.x, roi.y);</code></dd>
<dd><code>lines</code> - A vector of Vec4f elements specifying the beginning and ending point of a line. Where
     Vec4f is (x1, y1, x2, y2), point 1 is the start, point 2 - end. Returned lines are strictly
     oriented depending on the gradient.</dd>
<dd><code>width</code> - Vector of widths of the regions, where the lines are found. E.g. Width of line.</dd>
<dd><code>prec</code> - Vector of precisions with which the lines are found.
     bigger the value, logarithmically better the detection.
 <ul>
   <li>
      -1 corresponds to 10 mean false alarms
   </li>
   <li>
      0 corresponds to 1 mean false alarm
   </li>
   <li>
      1 corresponds to 0.1 mean false alarms
     This vector will be calculated only when the objects type is #LSD_REFINE_ADV.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;width)</span></div>
<div class="block">Finds lines in the input image.

     This is the output of the default parameters of the algorithm on the above shown image.

     ![image](pics/building_lsd.png)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - A grayscale (CV_8UC1) input image. If only a roi needs to be selected, use:
     <code>lsd_ptr-&amp;gt;detect(image(roi), lines, ...); lines += Scalar(roi.x, roi.y, roi.x, roi.y);</code></dd>
<dd><code>lines</code> - A vector of Vec4f elements specifying the beginning and ending point of a line. Where
     Vec4f is (x1, y1, x2, y2), point 1 is the start, point 2 - end. Returned lines are strictly
     oriented depending on the gradient.</dd>
<dd><code>width</code> - Vector of widths of the regions, where the lines are found. E.g. Width of line.
     bigger the value, logarithmically better the detection.
 <ul>
   <li>
      -1 corresponds to 10 mean false alarms
   </li>
   <li>
      0 corresponds to 1 mean false alarm
   </li>
   <li>
      1 corresponds to 0.1 mean false alarms
     This vector will be calculated only when the objects type is #LSD_REFINE_ADV.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</span></div>
<div class="block">Finds lines in the input image.

     This is the output of the default parameters of the algorithm on the above shown image.

     ![image](pics/building_lsd.png)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - A grayscale (CV_8UC1) input image. If only a roi needs to be selected, use:
     <code>lsd_ptr-&amp;gt;detect(image(roi), lines, ...); lines += Scalar(roi.x, roi.y, roi.x, roi.y);</code></dd>
<dd><code>lines</code> - A vector of Vec4f elements specifying the beginning and ending point of a line. Where
     Vec4f is (x1, y1, x2, y2), point 1 is the start, point 2 - end. Returned lines are strictly
     oriented depending on the gradient.
     bigger the value, logarithmically better the detection.
 <ul>
   <li>
      -1 corresponds to 10 mean false alarms
   </li>
   <li>
      0 corresponds to 1 mean false alarm
   </li>
   <li>
      1 corresponds to 0.1 mean false alarms
     This vector will be calculated only when the objects type is #LSD_REFINE_ADV.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawSegments(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>drawSegments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawSegments</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</span></div>
<div class="block">Draws the line segments on a given image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - The image, where the lines will be drawn. Should be bigger or equal to the image,
     where the lines were found.</dd>
<dd><code>lines</code> - A vector of the lines that needed to be drawn.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compareSegments(org.opencv.core.Size,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>compareSegments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compareSegments</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Draws two groups of lines in blue and red, counting the non overlapping (mismatching) pixels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - The size of the image, where lines1 and lines2 were found.</dd>
<dd><code>lines1</code> - The first group of lines that needs to be drawn. It is visualized in blue color.</dd>
<dd><code>lines2</code> - The second group of lines. They visualized in red color.</dd>
<dd><code>image</code> - Optional image, where the lines will be drawn. The image should be color(3-channel)
     in order for lines1 and lines2 to be drawn in the above mentioned colors.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compareSegments(org.opencv.core.Size,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>compareSegments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compareSegments</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines2)</span></div>
<div class="block">Draws two groups of lines in blue and red, counting the non overlapping (mismatching) pixels.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - The size of the image, where lines1 and lines2 were found.</dd>
<dd><code>lines1</code> - The first group of lines that needs to be drawn. It is visualized in blue color.</dd>
<dd><code>lines2</code> - The second group of lines. They visualized in red color.
     in order for lines1 and lines2 to be drawn in the above mentioned colors.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
