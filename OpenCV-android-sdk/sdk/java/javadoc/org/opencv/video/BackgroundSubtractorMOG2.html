<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>BackgroundSubtractorMOG2 (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.video, class: BackgroundSubtractorMOG2">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.video</a></div>
<h1 title="Class BackgroundSubtractorMOG2" class="title">Class BackgroundSubtractorMOG2</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="BackgroundSubtractor.html" title="class in org.opencv.video">org.opencv.video.BackgroundSubtractor</a>
<div class="inheritance">org.opencv.video.BackgroundSubtractorMOG2</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">BackgroundSubtractorMOG2</span>
<span class="extends-implements">extends <a href="BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></span></div>
<div class="block">Gaussian Mixture-based Background/Foreground Segmentation Algorithm.

 The class implements the Gaussian mixture model background subtraction described in CITE: Zivkovic2004
 and CITE: Zivkovic2006 .</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#apply(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">apply</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes a foreground mask.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#apply(org.opencv.core.Mat,org.opencv.core.Mat,double)" class="member-name-link">apply</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask,
 double&nbsp;learningRate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes a foreground mask.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBackgroundRatio()" class="member-name-link">getBackgroundRatio</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the "background ratio" parameter of the algorithm

     If a foreground pixel keeps semi-constant value for about backgroundRatio\*history frames, it's
     considered background and added to the model as a center of a new component.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComplexityReductionThreshold()" class="member-name-link">getComplexityReductionThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the complexity reduction threshold

     This parameter defines the number of samples needed to accept to prove the component exists.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDetectShadows()" class="member-name-link">getDetectShadows</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the shadow detection flag

     If true, the algorithm detects shadows and marks them.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHistory()" class="member-name-link">getHistory</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the number of last frames that affect the background model</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNMixtures()" class="member-name-link">getNMixtures</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the number of gaussian components in the background model</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShadowThreshold()" class="member-name-link">getShadowThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the shadow threshold

     A shadow is detected if pixel is a darker version of the background.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShadowValue()" class="member-name-link">getShadowValue</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the shadow value

     Shadow value is the value used to mark shadows in the foreground mask.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarInit()" class="member-name-link">getVarInit</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the initial variance of each gaussian component</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarMax()" class="member-name-link">getVarMax</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarMin()" class="member-name-link">getVarMin</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarThreshold()" class="member-name-link">getVarThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the variance threshold for the pixel-model match

     The main threshold on the squared Mahalanobis distance to decide if the sample is well described by
     the background model or not.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarThresholdGen()" class="member-name-link">getVarThresholdGen</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the variance threshold for the pixel-model match used for new mixture component generation

     Threshold for the squared Mahalanobis distance that helps decide when a sample is close to the
     existing components (corresponds to Tg in the paper).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBackgroundRatio(double)" class="member-name-link">setBackgroundRatio</a><wbr>(double&nbsp;ratio)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the "background ratio" parameter of the algorithm</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComplexityReductionThreshold(double)" class="member-name-link">setComplexityReductionThreshold</a><wbr>(double&nbsp;ct)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the complexity reduction threshold</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDetectShadows(boolean)" class="member-name-link">setDetectShadows</a><wbr>(boolean&nbsp;detectShadows)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables or disables shadow detection</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHistory(int)" class="member-name-link">setHistory</a><wbr>(int&nbsp;history)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the number of last frames that affect the background model</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNMixtures(int)" class="member-name-link">setNMixtures</a><wbr>(int&nbsp;nmixtures)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the number of gaussian components in the background model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShadowThreshold(double)" class="member-name-link">setShadowThreshold</a><wbr>(double&nbsp;threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the shadow threshold</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShadowValue(int)" class="member-name-link">setShadowValue</a><wbr>(int&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the shadow value</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVarInit(double)" class="member-name-link">setVarInit</a><wbr>(double&nbsp;varInit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the initial variance of each gaussian component</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVarMax(double)" class="member-name-link">setVarMax</a><wbr>(double&nbsp;varMax)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVarMin(double)" class="member-name-link">setVarMin</a><wbr>(double&nbsp;varMin)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVarThreshold(double)" class="member-name-link">setVarThreshold</a><wbr>(double&nbsp;varThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the variance threshold for the pixel-model match</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVarThresholdGen(double)" class="member-name-link">setVarThresholdGen</a><wbr>(double&nbsp;varThresholdGen)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the variance threshold for the pixel-model match used for new mixture component generation</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.video.BackgroundSubtractor">Methods inherited from class&nbsp;org.opencv.video.<a href="BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></h3>
<code><a href="BackgroundSubtractor.html#getBackgroundImage(org.opencv.core.Mat)">getBackgroundImage</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getHistory()">
<h3>getHistory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getHistory</span>()</div>
<div class="block">Returns the number of last frames that affect the background model</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHistory(int)">
<h3>setHistory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHistory</span><wbr><span class="parameters">(int&nbsp;history)</span></div>
<div class="block">Sets the number of last frames that affect the background model</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNMixtures()">
<h3>getNMixtures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNMixtures</span>()</div>
<div class="block">Returns the number of gaussian components in the background model</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNMixtures(int)">
<h3>setNMixtures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNMixtures</span><wbr><span class="parameters">(int&nbsp;nmixtures)</span></div>
<div class="block">Sets the number of gaussian components in the background model.

     The model needs to be reinitalized to reserve memory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nmixtures</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBackgroundRatio()">
<h3>getBackgroundRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getBackgroundRatio</span>()</div>
<div class="block">Returns the "background ratio" parameter of the algorithm

     If a foreground pixel keeps semi-constant value for about backgroundRatio\*history frames, it's
     considered background and added to the model as a center of a new component. It corresponds to TB
     parameter in the paper.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBackgroundRatio(double)">
<h3>setBackgroundRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBackgroundRatio</span><wbr><span class="parameters">(double&nbsp;ratio)</span></div>
<div class="block">Sets the "background ratio" parameter of the algorithm</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ratio</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVarThreshold()">
<h3>getVarThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getVarThreshold</span>()</div>
<div class="block">Returns the variance threshold for the pixel-model match

     The main threshold on the squared Mahalanobis distance to decide if the sample is well described by
     the background model or not. Related to Cthr from the paper.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVarThreshold(double)">
<h3>setVarThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVarThreshold</span><wbr><span class="parameters">(double&nbsp;varThreshold)</span></div>
<div class="block">Sets the variance threshold for the pixel-model match</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>varThreshold</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVarThresholdGen()">
<h3>getVarThresholdGen</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getVarThresholdGen</span>()</div>
<div class="block">Returns the variance threshold for the pixel-model match used for new mixture component generation

     Threshold for the squared Mahalanobis distance that helps decide when a sample is close to the
     existing components (corresponds to Tg in the paper). If a pixel is not close to any component, it
     is considered foreground or added as a new component. 3 sigma =&gt; Tg=3\*3=9 is default. A smaller Tg
     value generates more components. A higher Tg value may result in a small number of components but
     they can grow too large.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVarThresholdGen(double)">
<h3>setVarThresholdGen</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVarThresholdGen</span><wbr><span class="parameters">(double&nbsp;varThresholdGen)</span></div>
<div class="block">Sets the variance threshold for the pixel-model match used for new mixture component generation</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>varThresholdGen</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVarInit()">
<h3>getVarInit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getVarInit</span>()</div>
<div class="block">Returns the initial variance of each gaussian component</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVarInit(double)">
<h3>setVarInit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVarInit</span><wbr><span class="parameters">(double&nbsp;varInit)</span></div>
<div class="block">Sets the initial variance of each gaussian component</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>varInit</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVarMin()">
<h3>getVarMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getVarMin</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setVarMin(double)">
<h3>setVarMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVarMin</span><wbr><span class="parameters">(double&nbsp;varMin)</span></div>
</section>
</li>
<li>
<section class="detail" id="getVarMax()">
<h3>getVarMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getVarMax</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setVarMax(double)">
<h3>setVarMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVarMax</span><wbr><span class="parameters">(double&nbsp;varMax)</span></div>
</section>
</li>
<li>
<section class="detail" id="getComplexityReductionThreshold()">
<h3>getComplexityReductionThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getComplexityReductionThreshold</span>()</div>
<div class="block">Returns the complexity reduction threshold

     This parameter defines the number of samples needed to accept to prove the component exists. CT=0.05
     is a default value for all the samples. By setting CT=0 you get an algorithm very similar to the
     standard Stauffer&amp;Grimson algorithm.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setComplexityReductionThreshold(double)">
<h3>setComplexityReductionThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComplexityReductionThreshold</span><wbr><span class="parameters">(double&nbsp;ct)</span></div>
<div class="block">Sets the complexity reduction threshold</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ct</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDetectShadows()">
<h3>getDetectShadows</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDetectShadows</span>()</div>
<div class="block">Returns the shadow detection flag

     If true, the algorithm detects shadows and marks them. See createBackgroundSubtractorMOG2 for
     details.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDetectShadows(boolean)">
<h3>setDetectShadows</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDetectShadows</span><wbr><span class="parameters">(boolean&nbsp;detectShadows)</span></div>
<div class="block">Enables or disables shadow detection</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>detectShadows</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getShadowValue()">
<h3>getShadowValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getShadowValue</span>()</div>
<div class="block">Returns the shadow value

     Shadow value is the value used to mark shadows in the foreground mask. Default value is 127. Value 0
     in the mask always means background, 255 means foreground.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShadowValue(int)">
<h3>setShadowValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShadowValue</span><wbr><span class="parameters">(int&nbsp;value)</span></div>
<div class="block">Sets the shadow value</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getShadowThreshold()">
<h3>getShadowThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getShadowThreshold</span>()</div>
<div class="block">Returns the shadow threshold

     A shadow is detected if pixel is a darker version of the background. The shadow threshold (Tau in
     the paper) is a threshold defining how much darker the shadow can be. Tau= 0.5 means that if a pixel
     is more than twice darker then it is not shadow. See Prati, Mikic, Trivedi and Cucchiara,
 Detecting Moving Shadows...*, IEEE PAMI,2003.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShadowThreshold(double)">
<h3>setShadowThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShadowThreshold</span><wbr><span class="parameters">(double&nbsp;threshold)</span></div>
<div class="block">Sets the shadow threshold</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>threshold</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="apply(org.opencv.core.Mat,org.opencv.core.Mat,double)">
<h3>apply</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">apply</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask,
 double&nbsp;learningRate)</span></div>
<div class="block">Computes a foreground mask.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="BackgroundSubtractor.html#apply(org.opencv.core.Mat,org.opencv.core.Mat,double)">apply</a></code>&nbsp;in class&nbsp;<code><a href="BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></code></dd>
<dt>Parameters:</dt>
<dd><code>image</code> - Next video frame. Floating point frame will be used without scaling and should be in range \([0,255]\).</dd>
<dd><code>fgmask</code> - The output foreground mask as an 8-bit binary image.</dd>
<dd><code>learningRate</code> - The value between 0 and 1 that indicates how fast the background model is
     learnt. Negative parameter value makes the algorithm to use some automatically chosen learning
     rate. 0 means that the background model is not updated at all, 1 means that the background model
     is completely reinitialized from the last frame.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="apply(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>apply</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">apply</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask)</span></div>
<div class="block">Computes a foreground mask.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="BackgroundSubtractor.html#apply(org.opencv.core.Mat,org.opencv.core.Mat)">apply</a></code>&nbsp;in class&nbsp;<code><a href="BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></code></dd>
<dt>Parameters:</dt>
<dd><code>image</code> - Next video frame. Floating point frame will be used without scaling and should be in range \([0,255]\).</dd>
<dd><code>fgmask</code> - The output foreground mask as an 8-bit binary image.
     learnt. Negative parameter value makes the algorithm to use some automatically chosen learning
     rate. 0 means that the background model is not updated at all, 1 means that the background model
     is completely reinitialized from the last frame.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
