<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Video (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.video, class: Video">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.video</a></div>
<h1 title="Class Video" class="title">Class Video</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.video.Video</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Video</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MOTION_AFFINE" class="member-name-link">MOTION_AFFINE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MOTION_EUCLIDEAN" class="member-name-link">MOTION_EUCLIDEAN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MOTION_HOMOGRAPHY" class="member-name-link">MOTION_HOMOGRAPHY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MOTION_TRANSLATION" class="member-name-link">MOTION_TRANSLATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#OPTFLOW_FARNEBACK_GAUSSIAN" class="member-name-link">OPTFLOW_FARNEBACK_GAUSSIAN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#OPTFLOW_LK_GET_MIN_EIGENVALS" class="member-name-link">OPTFLOW_LK_GET_MIN_EIGENVALS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#OPTFLOW_USE_INITIAL_FLOW" class="member-name-link">OPTFLOW_USE_INITIAL_FLOW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#TrackerSamplerCSC_MODE_DETECT" class="member-name-link">TrackerSamplerCSC_MODE_DETECT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#TrackerSamplerCSC_MODE_INIT_NEG" class="member-name-link">TrackerSamplerCSC_MODE_INIT_NEG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#TrackerSamplerCSC_MODE_INIT_POS" class="member-name-link">TrackerSamplerCSC_MODE_INIT_POS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#TrackerSamplerCSC_MODE_TRACK_NEG" class="member-name-link">TrackerSamplerCSC_MODE_TRACK_NEG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#TrackerSamplerCSC_MODE_TRACK_POS" class="member-name-link">TrackerSamplerCSC_MODE_TRACK_POS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Video</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int)" class="member-name-link">buildOpticalFlowPyramid</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean)" class="member-name-link">buildOpticalFlowPyramid</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int)" class="member-name-link">buildOpticalFlowPyramid</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int,int)" class="member-name-link">buildOpticalFlowPyramid</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder,
 int&nbsp;derivBorder)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int,int,boolean)" class="member-name-link">buildOpticalFlowPyramid</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder,
 int&nbsp;derivBorder,
 boolean&nbsp;tryReuseInputImage)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowFarneback(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,double,int,int,int,int,double,int)" class="member-name-link">calcOpticalFlowFarneback</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
 double&nbsp;pyr_scale,
 int&nbsp;levels,
 int&nbsp;winsize,
 int&nbsp;iterations,
 int&nbsp;poly_n,
 double&nbsp;poly_sigma,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Computes a dense optical flow using the Gunnar Farneback's algorithm.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria,int)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria,int,double)" class="member-name-link">calcOpticalFlowPyrLK</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 int&nbsp;flags,
 double&nbsp;minEigThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#CamShift(org.opencv.core.Mat,org.opencv.core.Rect,org.opencv.core.TermCriteria)" class="member-name-link">CamShift</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
 <a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Finds an object center, size, and orientation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#computeECC(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">computeECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#computeECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">computeECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorKNN()" class="member-name-link">createBackgroundSubtractorKNN</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates KNN Background Subtractor

 whether a pixel is close to that sample.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorKNN(int)" class="member-name-link">createBackgroundSubtractorKNN</a><wbr>(int&nbsp;history)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates KNN Background Subtractor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorKNN(int,double)" class="member-name-link">createBackgroundSubtractorKNN</a><wbr>(int&nbsp;history,
 double&nbsp;dist2Threshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates KNN Background Subtractor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorKNN(int,double,boolean)" class="member-name-link">createBackgroundSubtractorKNN</a><wbr>(int&nbsp;history,
 double&nbsp;dist2Threshold,
 boolean&nbsp;detectShadows)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates KNN Background Subtractor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorMOG2()" class="member-name-link">createBackgroundSubtractorMOG2</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MOG2 Background Subtractor

 to decide whether a pixel is well described by the background model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorMOG2(int)" class="member-name-link">createBackgroundSubtractorMOG2</a><wbr>(int&nbsp;history)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MOG2 Background Subtractor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorMOG2(int,double)" class="member-name-link">createBackgroundSubtractorMOG2</a><wbr>(int&nbsp;history,
 double&nbsp;varThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MOG2 Background Subtractor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createBackgroundSubtractorMOG2(int,double,boolean)" class="member-name-link">createBackgroundSubtractorMOG2</a><wbr>(int&nbsp;history,
 double&nbsp;varThreshold,
 boolean&nbsp;detectShadows)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MOG2 Background Subtractor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">findTransformECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">findTransformECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria)" class="member-name-link">findTransformECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria,org.opencv.core.Mat)" class="member-name-link">findTransformECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria,org.opencv.core.Mat,int)" class="member-name-link">findTransformECC</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask,
 int&nbsp;gaussFiltSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Finds the geometric transform (warp) between two images in terms of the ECC criterion CITE: EP08 .</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#meanShift(org.opencv.core.Mat,org.opencv.core.Rect,org.opencv.core.TermCriteria)" class="member-name-link">meanShift</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
 <a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Finds an object on a back projection image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readOpticalFlow(java.lang.String)" class="member-name-link">readOpticalFlow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read a .flo file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#writeOpticalFlow(java.lang.String,org.opencv.core.Mat)" class="member-name-link">writeOpticalFlow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Write a .flo to disk</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="OPTFLOW_USE_INITIAL_FLOW">
<h3>OPTFLOW_USE_INITIAL_FLOW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">OPTFLOW_USE_INITIAL_FLOW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_USE_INITIAL_FLOW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="OPTFLOW_LK_GET_MIN_EIGENVALS">
<h3>OPTFLOW_LK_GET_MIN_EIGENVALS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">OPTFLOW_LK_GET_MIN_EIGENVALS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_LK_GET_MIN_EIGENVALS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="OPTFLOW_FARNEBACK_GAUSSIAN">
<h3>OPTFLOW_FARNEBACK_GAUSSIAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">OPTFLOW_FARNEBACK_GAUSSIAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_FARNEBACK_GAUSSIAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MOTION_TRANSLATION">
<h3>MOTION_TRANSLATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MOTION_TRANSLATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_TRANSLATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MOTION_EUCLIDEAN">
<h3>MOTION_EUCLIDEAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MOTION_EUCLIDEAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_EUCLIDEAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MOTION_AFFINE">
<h3>MOTION_AFFINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MOTION_AFFINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_AFFINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MOTION_HOMOGRAPHY">
<h3>MOTION_HOMOGRAPHY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MOTION_HOMOGRAPHY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_HOMOGRAPHY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TrackerSamplerCSC_MODE_INIT_POS">
<h3>TrackerSamplerCSC_MODE_INIT_POS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TrackerSamplerCSC_MODE_INIT_POS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_POS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TrackerSamplerCSC_MODE_INIT_NEG">
<h3>TrackerSamplerCSC_MODE_INIT_NEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TrackerSamplerCSC_MODE_INIT_NEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_NEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TrackerSamplerCSC_MODE_TRACK_POS">
<h3>TrackerSamplerCSC_MODE_TRACK_POS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TrackerSamplerCSC_MODE_TRACK_POS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_POS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TrackerSamplerCSC_MODE_TRACK_NEG">
<h3>TrackerSamplerCSC_MODE_TRACK_NEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TrackerSamplerCSC_MODE_TRACK_NEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_NEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TrackerSamplerCSC_MODE_DETECT">
<h3>TrackerSamplerCSC_MODE_DETECT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TrackerSamplerCSC_MODE_DETECT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_DETECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Video</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Video</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createBackgroundSubtractorMOG2(int,double,boolean)">
<h3>createBackgroundSubtractorMOG2</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorMOG2</span><wbr><span class="parameters">(int&nbsp;history,
 double&nbsp;varThreshold,
 boolean&nbsp;detectShadows)</span></div>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>varThreshold</code> - Threshold on the squared Mahalanobis distance between the pixel and the model
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.</dd>
<dd><code>detectShadows</code> - If true, the algorithm will detect shadows and mark them. It decreases the
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorMOG2(int,double)">
<h3>createBackgroundSubtractorMOG2</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorMOG2</span><wbr><span class="parameters">(int&nbsp;history,
 double&nbsp;varThreshold)</span></div>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>varThreshold</code> - Threshold on the squared Mahalanobis distance between the pixel and the model
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorMOG2(int)">
<h3>createBackgroundSubtractorMOG2</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorMOG2</span><wbr><span class="parameters">(int&nbsp;history)</span></div>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorMOG2()">
<h3>createBackgroundSubtractorMOG2</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorMOG2</span>()</div>
<div class="block">Creates MOG2 Background Subtractor

 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorKNN(int,double,boolean)">
<h3>createBackgroundSubtractorKNN</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorKNN</span><wbr><span class="parameters">(int&nbsp;history,
 double&nbsp;dist2Threshold,
 boolean&nbsp;detectShadows)</span></div>
<div class="block">Creates KNN Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>dist2Threshold</code> - Threshold on the squared distance between the pixel and the sample to decide
 whether a pixel is close to that sample. This parameter does not affect the background update.</dd>
<dd><code>detectShadows</code> - If true, the algorithm will detect shadows and mark them. It decreases the
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorKNN(int,double)">
<h3>createBackgroundSubtractorKNN</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorKNN</span><wbr><span class="parameters">(int&nbsp;history,
 double&nbsp;dist2Threshold)</span></div>
<div class="block">Creates KNN Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>dist2Threshold</code> - Threshold on the squared distance between the pixel and the sample to decide
 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorKNN(int)">
<h3>createBackgroundSubtractorKNN</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorKNN</span><wbr><span class="parameters">(int&nbsp;history)</span></div>
<div class="block">Creates KNN Background Subtractor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>history</code> - Length of the history.
 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createBackgroundSubtractorKNN()">
<h3>createBackgroundSubtractorKNN</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></span>&nbsp;<span class="element-name">createBackgroundSubtractorKNN</span>()</div>
<div class="block">Creates KNN Background Subtractor

 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CamShift(org.opencv.core.Mat,org.opencv.core.Rect,org.opencv.core.TermCriteria)">
<h3>CamShift</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a></span>&nbsp;<span class="element-name">CamShift</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
 <a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</span></div>
<div class="block">Finds an object center, size, and orientation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>probImage</code> - Back projection of the object histogram. See calcBackProject.</dd>
<dd><code>window</code> - Initial search window.</dd>
<dd><code>criteria</code> - Stop criteria for the underlying meanShift.
 returns
 (in old interfaces) Number of iterations CAMSHIFT took to converge
 The function implements the CAMSHIFT object tracking algorithm CITE: Bradski98 . First, it finds an
 object center using meanShift and then adjusts the window size and finds the optimal rotation. The
 function returns the rotated rectangle structure that includes the object position, size, and
 orientation. The next position of the search window can be obtained with RotatedRect::boundingRect()

 See the OpenCV sample camshiftdemo.c that tracks colored objects.

 <b>Note:</b>
 <ul>
   <li>
    (Python) A sample explaining the camshift tracking algorithm can be found at
     opencv_source_code/samples/python/camshift.py
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="meanShift(org.opencv.core.Mat,org.opencv.core.Rect,org.opencv.core.TermCriteria)">
<h3>meanShift</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">meanShift</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
 <a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</span></div>
<div class="block">Finds an object on a back projection image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>probImage</code> - Back projection of the object histogram. See calcBackProject for details.</dd>
<dd><code>window</code> - Initial search window.</dd>
<dd><code>criteria</code> - Stop criteria for the iterative search algorithm.
 returns
 :   Number of iterations CAMSHIFT took to converge.
 The function implements the iterative object search algorithm. It takes the input back projection of
 an object and the initial position. The mass center in window of the back projection image is
 computed and the search window center shifts to the mass center. The procedure is repeated until the
 specified number of iterations criteria.maxCount is done or until the window center shifts by less
 than criteria.epsilon. The algorithm is used inside CamShift and, unlike CamShift , the search
 window size or orientation do not change during the search. You can simply pass the output of
 calcBackProject to this function. But better results can be obtained if you pre-filter the back
 projection and remove the noise. For example, you can do this by retrieving connected components
 with findContours , throwing away contours with small area ( contourArea ), and rendering the
 remaining contours with drawContours.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int,int,boolean)">
<h3>buildOpticalFlowPyramid</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">buildOpticalFlowPyramid</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder,
 int&nbsp;derivBorder,
 boolean&nbsp;tryReuseInputImage)</span></div>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.</dd>
<dd><code>derivBorder</code> - the border mode for gradients.</dd>
<dd><code>tryReuseInputImage</code> - put ROI of input image into the pyramid if possible. You can pass false
 to force data copying.</dd>
<dt>Returns:</dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int,int)">
<h3>buildOpticalFlowPyramid</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">buildOpticalFlowPyramid</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder,
 int&nbsp;derivBorder)</span></div>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.</dd>
<dd><code>derivBorder</code> - the border mode for gradients.
 to force data copying.</dd>
<dt>Returns:</dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean,int)">
<h3>buildOpticalFlowPyramid</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">buildOpticalFlowPyramid</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives,
 int&nbsp;pyrBorder)</span></div>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.
 to force data copying.</dd>
<dt>Returns:</dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int,boolean)">
<h3>buildOpticalFlowPyramid</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">buildOpticalFlowPyramid</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 boolean&nbsp;withDerivatives)</span></div>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.
 to force data copying.</dd>
<dt>Returns:</dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildOpticalFlowPyramid(org.opencv.core.Mat,java.util.List,org.opencv.core.Size,int)">
<h3>buildOpticalFlowPyramid</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">buildOpticalFlowPyramid</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel)</span></div>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.
 to force data copying.</dd>
<dt>Returns:</dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria,int,double)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 int&nbsp;flags,
 double&nbsp;minEigThreshold)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.</dd>
<dd><code>flags</code> - operation flags:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul></dd>
<dd><code>minEigThreshold</code> - the algorithm calculates the minimum eigen value of a 2x2 normal matrix of
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria,int)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 int&nbsp;flags)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.</dd>
<dd><code>flags</code> - operation flags:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul>
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int,org.opencv.core.TermCriteria)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul>
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size,int)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
 int&nbsp;maxLevel)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul>
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat,org.opencv.core.Size)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul>
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowPyrLK(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfPoint2f,org.opencv.core.MatOfByte,org.opencv.core.MatOfFloat)">
<h3>calcOpticalFlowPyrLK</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowPyrLK</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
 <a href="../core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</span></div>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
   </li>
 </ul>
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calcOpticalFlowFarneback(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,double,int,int,int,int,double,int)">
<h3>calcOpticalFlowFarneback</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">calcOpticalFlowFarneback</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
 double&nbsp;pyr_scale,
 int&nbsp;levels,
 int&nbsp;winsize,
 int&nbsp;iterations,
 int&nbsp;poly_n,
 double&nbsp;poly_sigma,
 int&nbsp;flags)</span></div>
<div class="block">Computes a dense optical flow using the Gunnar Farneback's algorithm.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prev</code> - first 8-bit single-channel input image.</dd>
<dd><code>next</code> - second input image of the same size and the same type as prev.</dd>
<dd><code>flow</code> - computed flow image that has the same size as prev and type CV_32FC2.</dd>
<dd><code>pyr_scale</code> - parameter, specifying the image scale (&lt;1) to build pyramids for each image;
 pyr_scale=0.5 means a classical pyramid, where each next layer is twice smaller than the previous
 one.</dd>
<dd><code>levels</code> - number of pyramid layers including the initial image; levels=1 means that no extra
 layers are created and only the original images are used.</dd>
<dd><code>winsize</code> - averaging window size; larger values increase the algorithm robustness to image
 noise and give more chances for fast motion detection, but yield more blurred motion field.</dd>
<dd><code>iterations</code> - number of iterations the algorithm does at each pyramid level.</dd>
<dd><code>poly_n</code> - size of the pixel neighborhood used to find polynomial expansion in each pixel;
 larger values mean that the image will be approximated with smoother surfaces, yielding more
 robust algorithm and more blurred motion field, typically poly_n =5 or 7.</dd>
<dd><code>poly_sigma</code> - standard deviation of the Gaussian that is used to smooth derivatives used as a
 basis for the polynomial expansion; for poly_n=5, you can set poly_sigma=1.1, for poly_n=7, a
 good value would be poly_sigma=1.5.</dd>
<dd><code>flags</code> - operation flags that can be a combination of the following:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses the input flow as an initial flow approximation.
   </li>
   <li>
     <b>OPTFLOW_FARNEBACK_GAUSSIAN</b> uses the Gaussian \(\texttt{winsize}\times\texttt{winsize}\)
      filter instead of a box filter of the same size for optical flow estimation; usually, this
      option gives z more accurate flow than with a box filter, at the cost of lower speed;
      normally, winsize for a Gaussian window should be set to a larger value to achieve the same
      level of robustness.
   </li>
 </ul>

 The function finds an optical flow for each prev pixel using the CITE: Farneback2003 algorithm so that

 \(\texttt{prev} (y,x)  \sim \texttt{next} ( y + \texttt{flow} (y,x)[1],  x + \texttt{flow} (y,x)[0])\)

 <b>Note:</b> Some examples:

 <ul>
   <li>
    An example using the optical flow algorithm described by Gunnar Farneback can be found at
     opencv_source_code/samples/cpp/fback.cpp
   </li>
   <li>
    (Python) An example using the optical flow algorithm described by Gunnar Farneback can be
     found at opencv_source_code/samples/python/opt_flow.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>computeECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">computeECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</span></div>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image to be warped to provide an image similar to
  templateImage, same type as templateImage.</dd>
<dd><code>inputMask</code> - An optional mask to indicate valid values of inputImage.

 SEE:
 findTransformECC</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeECC(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>computeECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">computeECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage)</span></div>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image to be warped to provide an image similar to
  templateImage, same type as templateImage.

 SEE:
 findTransformECC</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria,org.opencv.core.Mat,int)">
<h3>findTransformECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">findTransformECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask,
 int&nbsp;gaussFiltSize)</span></div>
<div class="block">Finds the geometric transform (warp) between two images in terms of the ECC criterion CITE: EP08 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image which should be warped with the final warpMatrix in
 order to provide an image similar to templateImage, same type as templateImage.</dd>
<dd><code>warpMatrix</code> - floating-point \(2\times 3\) or \(3\times 3\) mapping matrix (warp).</dd>
<dd><code>motionType</code> - parameter, specifying the type of motion:
 <ul>
   <li>
     <b>MOTION_TRANSLATION</b> sets a translational motion model; warpMatrix is \(2\times 3\) with
      the first \(2\times 2\) part being the unity matrix and the rest two parameters being
      estimated.
   </li>
   <li>
     <b>MOTION_EUCLIDEAN</b> sets a Euclidean (rigid) transformation as motion model; three
      parameters are estimated; warpMatrix is \(2\times 3\).
   </li>
   <li>
     <b>MOTION_AFFINE</b> sets an affine motion model (DEFAULT); six parameters are estimated;
      warpMatrix is \(2\times 3\).
   </li>
   <li>
     <b>MOTION_HOMOGRAPHY</b> sets a homography as a motion model; eight parameters are
      estimated;\<code>warpMatrix\</code> is \(3\times 3\).
   </li>
 </ul></dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the ECC algorithm;
 criteria.epsilon defines the threshold of the increment in the correlation coefficient between two
 iterations (a negative criteria.epsilon makes criteria.maxcount the only termination criterion).
 Default values are shown in the declaration above.</dd>
<dd><code>inputMask</code> - An optional mask to indicate valid values of inputImage.</dd>
<dd><code>gaussFiltSize</code> - An optional value indicating size of gaussian blur filter; (DEFAULT: 5)

 The function estimates the optimum transformation (warpMatrix) with respect to ECC criterion
 (CITE: EP08), that is

 \(\texttt{warpMatrix} = \arg\max_{W} \texttt{ECC}(\texttt{templateImage}(x,y),\texttt{inputImage}(x',y'))\)

 where

 \(\begin{bmatrix} x' \\ y' \end{bmatrix} = W \cdot \begin{bmatrix} x \\ y \\ 1 \end{bmatrix}\)

 (the equation holds with homogeneous coordinates for homography). It returns the final enhanced
 correlation coefficient, that is the correlation coefficient between the template image and the
 final warped input image. When a \(3\times 3\) matrix is given with motionType =0, 1 or 2, the third
 row is ignored.

 Unlike findHomography and estimateRigidTransform, the function findTransformECC implements an
 area-based alignment that builds on intensity similarities. In essence, the function updates the
 initial transformation that roughly aligns the images. If this information is missing, the identity
 warp (unity matrix) is used as an initialization. Note that if images undergo strong
 displacements/rotations, an initial transformation that roughly aligns the images is necessary
 (e.g., a simple euclidean/similarity transform that allows for the images showing the same image
 content approximately). Use inverse warping in the second image to take an image close to the first
 one, i.e. use the flag WARP_INVERSE_MAP with warpAffine or warpPerspective. See also the OpenCV
 sample image_alignment.cpp that demonstrates the use of the function. Note that the function throws
 an exception if algorithm does not converges.

 SEE:
 computeECC, estimateAffine2D, estimateAffinePartial2D, findHomography</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria,org.opencv.core.Mat)">
<h3>findTransformECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">findTransformECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</span></div>
</section>
</li>
<li>
<section class="detail" id="findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int,org.opencv.core.TermCriteria)">
<h3>findTransformECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">findTransformECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType,
 <a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</span></div>
</section>
</li>
<li>
<section class="detail" id="findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>findTransformECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">findTransformECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
 int&nbsp;motionType)</span></div>
</section>
</li>
<li>
<section class="detail" id="findTransformECC(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>findTransformECC</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">findTransformECC</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</span></div>
</section>
</li>
<li>
<section class="detail" id="readOpticalFlow(java.lang.String)">
<h3>readOpticalFlow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">readOpticalFlow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Read a .flo file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - Path to the file to be loaded

  The function readOpticalFlow loads a flow field from a file and returns it as a single matrix.
  Resulting Mat has a type CV_32FC2 - floating-point, 2-channel. First channel corresponds to the
  flow in the horizontal direction (u), second - vertical (v).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeOpticalFlow(java.lang.String,org.opencv.core.Mat)">
<h3>writeOpticalFlow</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">writeOpticalFlow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow)</span></div>
<div class="block">Write a .flo to disk</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - Path to the file to be written</dd>
<dd><code>flow</code> - Flow field to be stored

  The function stores a flow field in a file, returns true on success, false otherwise.
  The flow field must be a 2-channel, floating-point matrix (CV_32FC2). First channel corresponds
  to the flow in the horizontal direction (u), second - vertical (v).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
