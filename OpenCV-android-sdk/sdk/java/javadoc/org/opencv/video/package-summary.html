<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.video (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.video">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.video" class="title">Package org.opencv.video</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.video</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for background/foreground segmentation.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">K-nearest neighbours - based Background/Foreground Segmentation Algorithm.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Gaussian Mixture-based Background/Foreground Segmentation Algorithm.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base class for dense optical flow algorithms</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">DIS optical flow algorithm.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class computing a dense optical flow using the Gunnar Farneback's algorithm.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="KalmanFilter.html" title="class in org.opencv.video">KalmanFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Kalman filter class.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SparseOpticalFlow.html" title="class in org.opencv.video">SparseOpticalFlow</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base interface for sparse optical flow algorithms.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SparsePyrLKOpticalFlow.html" title="class in org.opencv.video">SparsePyrLKOpticalFlow</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class used for calculating a sparse optical flow.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Tracker.html" title="class in org.opencv.video">Tracker</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base abstract class for the long-term tracker</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TrackerDaSiamRPN.html" title="class in org.opencv.video">TrackerDaSiamRPN</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TrackerDaSiamRPN_Params.html" title="class in org.opencv.video">TrackerDaSiamRPN_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TrackerGOTURN.html" title="class in org.opencv.video">TrackerGOTURN</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">the GOTURN (Generic Object Tracking Using Regression Networks) tracker

 GOTURN (CITE: GOTURN) is kind of trackers based on Convolutional Neural Networks (CNN).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TrackerGOTURN_Params.html" title="class in org.opencv.video">TrackerGOTURN_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TrackerMIL.html" title="class in org.opencv.video">TrackerMIL</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The MIL algorithm trains a classifier in an online manner to separate the object from the
 background.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TrackerMIL_Params.html" title="class in org.opencv.video">TrackerMIL_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TrackerNano.html" title="class in org.opencv.video">TrackerNano</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">the Nano tracker is a super lightweight dnn-based general object tracking.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TrackerNano_Params.html" title="class in org.opencv.video">TrackerNano_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TrackerVit.html" title="class in org.opencv.video">TrackerVit</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">the VIT tracker is a super lightweight dnn-based general object tracking.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TrackerVit_Params.html" title="class in org.opencv.video">TrackerVit_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="VariationalRefinement.html" title="class in org.opencv.video">VariationalRefinement</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Variational optical flow refinement

 This class implements variational refinement of the input flow field, i.e.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Video.html" title="class in org.opencv.video">Video</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
