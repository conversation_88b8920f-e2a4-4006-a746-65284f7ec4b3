<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>FarnebackOpticalFlow (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.video, class: FarnebackOpticalFlow">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.video</a></div>
<h1 title="Class FarnebackOpticalFlow" class="title">Class FarnebackOpticalFlow</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="DenseOpticalFlow.html" title="class in org.opencv.video">org.opencv.video.DenseOpticalFlow</a>
<div class="inheritance">org.opencv.video.FarnebackOpticalFlow</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FarnebackOpticalFlow</span>
<span class="extends-implements">extends <a href="DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></span></div>
<div class="block">Class computing a dense optical flow using the Gunnar Farneback's algorithm.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean,int)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean,int,int,int,double)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN,
 double&nbsp;polySigma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,boolean,int,int,int,double,int)" class="member-name-link">create</a><wbr>(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN,
 double&nbsp;polySigma,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFastPyramids()" class="member-name-link">getFastPyramids</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFlags()" class="member-name-link">getFlags</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNumIters()" class="member-name-link">getNumIters</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNumLevels()" class="member-name-link">getNumLevels</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPolyN()" class="member-name-link">getPolyN</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPolySigma()" class="member-name-link">getPolySigma</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPyrScale()" class="member-name-link">getPyrScale</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWinSize()" class="member-name-link">getWinSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFastPyramids(boolean)" class="member-name-link">setFastPyramids</a><wbr>(boolean&nbsp;fastPyramids)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFlags(int)" class="member-name-link">setFlags</a><wbr>(int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNumIters(int)" class="member-name-link">setNumIters</a><wbr>(int&nbsp;numIters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNumLevels(int)" class="member-name-link">setNumLevels</a><wbr>(int&nbsp;numLevels)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPolyN(int)" class="member-name-link">setPolyN</a><wbr>(int&nbsp;polyN)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPolySigma(double)" class="member-name-link">setPolySigma</a><wbr>(double&nbsp;polySigma)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPyrScale(double)" class="member-name-link">setPyrScale</a><wbr>(double&nbsp;pyrScale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWinSize(int)" class="member-name-link">setWinSize</a><wbr>(int&nbsp;winSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.video.DenseOpticalFlow">Methods inherited from class&nbsp;org.opencv.video.<a href="DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></h3>
<code><a href="DenseOpticalFlow.html#calc(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">calc</a>, <a href="DenseOpticalFlow.html#collectGarbage()">collectGarbage</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNumLevels()">
<h3>getNumLevels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNumLevels</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setNumLevels(int)">
<h3>setNumLevels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNumLevels</span><wbr><span class="parameters">(int&nbsp;numLevels)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPyrScale()">
<h3>getPyrScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getPyrScale</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPyrScale(double)">
<h3>setPyrScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPyrScale</span><wbr><span class="parameters">(double&nbsp;pyrScale)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFastPyramids()">
<h3>getFastPyramids</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getFastPyramids</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setFastPyramids(boolean)">
<h3>setFastPyramids</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFastPyramids</span><wbr><span class="parameters">(boolean&nbsp;fastPyramids)</span></div>
</section>
</li>
<li>
<section class="detail" id="getWinSize()">
<h3>getWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getWinSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setWinSize(int)">
<h3>setWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWinSize</span><wbr><span class="parameters">(int&nbsp;winSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNumIters()">
<h3>getNumIters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNumIters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setNumIters(int)">
<h3>setNumIters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNumIters</span><wbr><span class="parameters">(int&nbsp;numIters)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPolyN()">
<h3>getPolyN</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPolyN</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPolyN(int)">
<h3>setPolyN</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPolyN</span><wbr><span class="parameters">(int&nbsp;polyN)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPolySigma()">
<h3>getPolySigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getPolySigma</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPolySigma(double)">
<h3>setPolySigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPolySigma</span><wbr><span class="parameters">(double&nbsp;polySigma)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFlags()">
<h3>getFlags</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFlags</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setFlags(int)">
<h3>setFlags</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFlags</span><wbr><span class="parameters">(int&nbsp;flags)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean,int,int,int,double,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN,
 double&nbsp;polySigma,
 int&nbsp;flags)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean,int,int,int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN,
 double&nbsp;polySigma)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters,
 int&nbsp;polyN)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize,
 int&nbsp;numIters)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids,
 int&nbsp;winSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,boolean)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale,
 boolean&nbsp;fastPyramids)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels,
 double&nbsp;pyrScale)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numLevels)</span></div>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></span>&nbsp;<span class="element-name">create</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
