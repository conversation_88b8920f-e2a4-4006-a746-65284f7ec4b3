<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.android Class Hierarchy (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="tree: package: org.opencv.android">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.android</h1>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.RotatedCameraFrame.html" class="type-name-link" title="class in org.opencv.android">CameraBridgeViewBase.RotatedCameraFrame</a> (implements org.opencv.android.<a href="CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewFrame</a>)</li>
<li class="circle">org.opencv.android.<a href="CameraGLRendererBase.html" class="type-name-link" title="class in org.opencv.android">CameraGLRendererBase</a> (implements android.opengl.GLSurfaceView.Renderer, android.graphics.SurfaceTexture.OnFrameAvailableListener)
<ul>
<li class="circle">org.opencv.android.<a href="Camera2Renderer.html" class="type-name-link" title="class in org.opencv.android">Camera2Renderer</a></li>
<li class="circle">org.opencv.android.<a href="CameraRenderer.html" class="type-name-link" title="class in org.opencv.android">CameraRenderer</a></li>
</ul>
</li>
<li class="circle">android.content.Context
<ul>
<li class="circle">android.content.ContextWrapper
<ul>
<li class="circle">android.view.ContextThemeWrapper
<ul>
<li class="circle">android.app.Activity (implements android.content.ComponentCallbacks2, android.view.KeyEvent.Callback, android.view.LayoutInflater.Factory2, android.view.View.OnCreateContextMenuListener, android.view.Window.Callback)
<ul>
<li class="circle">org.opencv.android.<a href="CameraActivity.html" class="type-name-link" title="class in org.opencv.android">CameraActivity</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.opencv.android.<a href="FpsMeter.html" class="type-name-link" title="class in org.opencv.android">FpsMeter</a></li>
<li class="circle">org.opencv.android.<a href="JavaCamera2View.JavaCameraSizeAccessor.html" class="type-name-link" title="class in org.opencv.android">JavaCamera2View.JavaCameraSizeAccessor</a> (implements org.opencv.android.<a href="CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li class="circle">org.opencv.android.<a href="JavaCameraView.JavaCameraSizeAccessor.html" class="type-name-link" title="class in org.opencv.android">JavaCameraView.JavaCameraSizeAccessor</a> (implements org.opencv.android.<a href="CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li class="circle">org.opencv.android.<a href="NativeCameraView.OpenCvSizeAccessor.html" class="type-name-link" title="class in org.opencv.android">NativeCameraView.OpenCvSizeAccessor</a> (implements org.opencv.android.<a href="CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li class="circle">org.opencv.android.<a href="OpenCVLoader.html" class="type-name-link" title="class in org.opencv.android">OpenCVLoader</a></li>
<li class="circle">org.opencv.android.<a href="Utils.html" class="type-name-link" title="class in org.opencv.android">Utils</a></li>
<li class="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li class="circle">android.view.SurfaceView
<ul>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.html" class="type-name-link" title="class in org.opencv.android">CameraBridgeViewBase</a> (implements android.view.SurfaceHolder.Callback)
<ul>
<li class="circle">org.opencv.android.<a href="JavaCamera2View.html" class="type-name-link" title="class in org.opencv.android">JavaCamera2View</a></li>
<li class="circle">org.opencv.android.<a href="JavaCameraView.html" class="type-name-link" title="class in org.opencv.android">JavaCameraView</a> (implements android.hardware.Camera.PreviewCallback)</li>
<li class="circle">org.opencv.android.<a href="NativeCameraView.html" class="type-name-link" title="class in org.opencv.android">NativeCameraView</a></li>
</ul>
</li>
<li class="circle">android.opengl.GLSurfaceView (implements android.view.SurfaceHolder.Callback2)
<ul>
<li class="circle">org.opencv.android.<a href="CameraGLSurfaceView.html" class="type-name-link" title="class in org.opencv.android">CameraGLSurfaceView</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.CvCameraViewFrame.html" class="type-name-link" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewFrame</a></li>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.CvCameraViewListener.html" class="type-name-link" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener</a></li>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.CvCameraViewListener2.html" class="type-name-link" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener2</a></li>
<li class="circle">org.opencv.android.<a href="CameraBridgeViewBase.ListItemAccessor.html" class="type-name-link" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a></li>
<li class="circle">org.opencv.android.<a href="CameraGLSurfaceView.CameraTextureListener.html" class="type-name-link" title="interface in org.opencv.android">CameraGLSurfaceView.CameraTextureListener</a></li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
