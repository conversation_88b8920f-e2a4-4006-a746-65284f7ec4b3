<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Utils (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.android, class: Utils">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.android</a></div>
<h1 title="Class Utils" class="title">Class Utils</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.android.Utils</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Utils</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Utils</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#bitmapToMat(android.graphics.Bitmap,org.opencv.core.Mat)" class="member-name-link">bitmapToMat</a><wbr>(android.graphics.Bitmap&nbsp;bmp,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Short form of the bitmapToMat(bmp, mat, unPremultiplyAlpha=false).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#bitmapToMat(android.graphics.Bitmap,org.opencv.core.Mat,boolean)" class="member-name-link">bitmapToMat</a><wbr>(android.graphics.Bitmap&nbsp;bmp,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 boolean&nbsp;unPremultiplyAlpha)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Converts Android Bitmap to OpenCV Mat.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#exportResource(android.content.Context,int)" class="member-name-link">exportResource</a><wbr>(android.content.Context&nbsp;context,
 int&nbsp;resourceId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#exportResource(android.content.Context,int,java.lang.String)" class="member-name-link">exportResource</a><wbr>(android.content.Context&nbsp;context,
 int&nbsp;resourceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#loadResource(android.content.Context,int)" class="member-name-link">loadResource</a><wbr>(android.content.Context&nbsp;context,
 int&nbsp;resourceId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#loadResource(android.content.Context,int,int)" class="member-name-link">loadResource</a><wbr>(android.content.Context&nbsp;context,
 int&nbsp;resourceId,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matToBitmap(org.opencv.core.Mat,android.graphics.Bitmap)" class="member-name-link">matToBitmap</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 android.graphics.Bitmap&nbsp;bmp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Short form of the <b>matToBitmap(mat, bmp, premultiplyAlpha=false)</b></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matToBitmap(org.opencv.core.Mat,android.graphics.Bitmap,boolean)" class="member-name-link">matToBitmap</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 android.graphics.Bitmap&nbsp;bmp,
 boolean&nbsp;premultiplyAlpha)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Converts OpenCV Mat to Android Bitmap.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Utils</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Utils</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="exportResource(android.content.Context,int)">
<h3>exportResource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">exportResource</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 int&nbsp;resourceId)</span></div>
</section>
</li>
<li>
<section class="detail" id="exportResource(android.content.Context,int,java.lang.String)">
<h3>exportResource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">exportResource</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 int&nbsp;resourceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</span></div>
</section>
</li>
<li>
<section class="detail" id="loadResource(android.content.Context,int)">
<h3>loadResource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">loadResource</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 int&nbsp;resourceId)</span>
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadResource(android.content.Context,int,int)">
<h3>loadResource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">loadResource</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 int&nbsp;resourceId,
 int&nbsp;flags)</span>
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bitmapToMat(android.graphics.Bitmap,org.opencv.core.Mat,boolean)">
<h3>bitmapToMat</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">bitmapToMat</span><wbr><span class="parameters">(android.graphics.Bitmap&nbsp;bmp,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 boolean&nbsp;unPremultiplyAlpha)</span></div>
<div class="block">Converts Android Bitmap to OpenCV Mat.
 <p>
 This function converts an Android Bitmap image to the OpenCV Mat.
 <br>'ARGB_8888' and 'RGB_565' input Bitmap formats are supported.
 <br>The output Mat is always created of the same size as the input Bitmap and of the 'CV_8UC4' type,
 it keeps the image in RGBA format.
 <br>This function throws an exception if the conversion fails.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bmp</code> - is a valid input Bitmap object of the type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>mat</code> - is a valid output Mat object, it will be reallocated if needed, so it may be empty.</dd>
<dd><code>unPremultiplyAlpha</code> - is a flag, that determines, whether the bitmap needs to be converted from alpha premultiplied format (like Android keeps 'ARGB_8888' ones) to regular one; this flag is ignored for 'RGB_565' bitmaps.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bitmapToMat(android.graphics.Bitmap,org.opencv.core.Mat)">
<h3>bitmapToMat</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">bitmapToMat</span><wbr><span class="parameters">(android.graphics.Bitmap&nbsp;bmp,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat)</span></div>
<div class="block">Short form of the bitmapToMat(bmp, mat, unPremultiplyAlpha=false).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bmp</code> - is a valid input Bitmap object of the type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>mat</code> - is a valid output Mat object, it will be reallocated if needed, so Mat may be empty.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matToBitmap(org.opencv.core.Mat,android.graphics.Bitmap,boolean)">
<h3>matToBitmap</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">matToBitmap</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 android.graphics.Bitmap&nbsp;bmp,
 boolean&nbsp;premultiplyAlpha)</span></div>
<div class="block">Converts OpenCV Mat to Android Bitmap.
 <p>
 <br>This function converts an image in the OpenCV Mat representation to the Android Bitmap.
 <br>The input Mat object has to be of the types 'CV_8UC1' (gray-scale), 'CV_8UC3' (RGB) or 'CV_8UC4' (RGBA).
 <br>The output Bitmap object has to be of the same size as the input Mat and of the types 'ARGB_8888' or 'RGB_565'.
 <br>This function throws an exception if the conversion fails.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mat</code> - is a valid input Mat object of types 'CV_8UC1', 'CV_8UC3' or 'CV_8UC4'.</dd>
<dd><code>bmp</code> - is a valid Bitmap object of the same size as the Mat and of type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>premultiplyAlpha</code> - is a flag, that determines, whether the Mat needs to be converted to alpha premultiplied format (like Android keeps 'ARGB_8888' bitmaps); the flag is ignored for 'RGB_565' bitmaps.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matToBitmap(org.opencv.core.Mat,android.graphics.Bitmap)">
<h3>matToBitmap</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">matToBitmap</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
 android.graphics.Bitmap&nbsp;bmp)</span></div>
<div class="block">Short form of the <b>matToBitmap(mat, bmp, premultiplyAlpha=false)</b></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mat</code> - is a valid input Mat object of the types 'CV_8UC1', 'CV_8UC3' or 'CV_8UC4'.</dd>
<dd><code>bmp</code> - is a valid Bitmap object of the same size as the Mat and of type 'ARGB_8888' or 'RGB_565'.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
