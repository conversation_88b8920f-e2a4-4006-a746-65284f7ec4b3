<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.android (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.android">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.android" class="title">Package org.opencv.android</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.android</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Camera2Renderer.html" title="class in org.opencv.android">Camera2Renderer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CameraActivity.html" title="class in org.opencv.android">CameraActivity</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a basic class, implementing the interaction with Camera and OpenCV library.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewFrame</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">This class interface is abstract representation of single frame from camera for onCameraFrame callback
 Attention: Do not use objects, that represents this interface out of onCameraFrame callback!</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CameraBridgeViewBase.CvCameraViewListener.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="CameraBridgeViewBase.CvCameraViewListener2.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener2</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CameraGLRendererBase.html" title="class in org.opencv.android">CameraGLRendererBase</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CameraGLSurfaceView.html" title="class in org.opencv.android">CameraGLSurfaceView</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="CameraGLSurfaceView.CameraTextureListener.html" title="interface in org.opencv.android">CameraGLSurfaceView.CameraTextureListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CameraRenderer.html" title="class in org.opencv.android">CameraRenderer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FpsMeter.html" title="class in org.opencv.android">FpsMeter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JavaCamera2View.html" title="class in org.opencv.android">JavaCamera2View</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is an implementation of the Bridge View between OpenCV and Java Camera.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JavaCamera2View.JavaCameraSizeAccessor.html" title="class in org.opencv.android">JavaCamera2View.JavaCameraSizeAccessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JavaCameraView.html" title="class in org.opencv.android">JavaCameraView</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is an implementation of the Bridge View between OpenCV and Java Camera.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android">JavaCameraView.JavaCameraSizeAccessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="NativeCameraView.html" title="class in org.opencv.android">NativeCameraView</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is an implementation of a bridge between SurfaceView and OpenCV VideoCapture.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NativeCameraView.OpenCvSizeAccessor.html" title="class in org.opencv.android">NativeCameraView.OpenCvSizeAccessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="OpenCVLoader.html" title="class in org.opencv.android">OpenCVLoader</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Helper class provides common initialization methods for OpenCV library.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Utils.html" title="class in org.opencv.android">Utils</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
