<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Feature2D (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: Feature2D">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class Feature2D" class="title">Class Feature2D</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance">org.opencv.features2d.Feature2D</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code>, <code><a href="AgastFeatureDetector.html" title="class in org.opencv.features2d">AgastFeatureDetector</a></code>, <code><a href="AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code>, <code><a href="BRISK.html" title="class in org.opencv.features2d">BRISK</a></code>, <code><a href="FastFeatureDetector.html" title="class in org.opencv.features2d">FastFeatureDetector</a></code>, <code><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code>, <code><a href="KAZE.html" title="class in org.opencv.features2d">KAZE</a></code>, <code><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code>, <code><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code>, <code><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code>, <code><a href="SimpleBlobDetector.html" title="class in org.opencv.features2d">SimpleBlobDetector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Feature2D</span>
<span class="extends-implements">extends <a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></span></div>
<div class="block">Abstract base class for 2D image feature detectors and descriptor extractors</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(java.util.List,java.util.List,java.util.List)" class="member-name-link">compute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)" class="member-name-link">compute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes the descriptors for a set of keypoints detected in an image (first variant) or image set
     (second variant).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#defaultNorm()" class="member-name-link">defaultNorm</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#descriptorSize()" class="member-name-link">descriptorSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#descriptorType()" class="member-name-link">descriptorType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(java.util.List,java.util.List)" class="member-name-link">detect</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(java.util.List,java.util.List,java.util.List)" class="member-name-link">detect</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects keypoints in an image (first variant) or image set (second variant).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects keypoints in an image (first variant) or image set (second variant).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)" class="member-name-link">detectAndCompute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects keypoints and computes the descriptors</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)" class="member-name-link">detectAndCompute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors,
 boolean&nbsp;useProvidedKeypoints)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects keypoints and computes the descriptors</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the Algorithm is empty (e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultName()" class="member-name-link">getDefaultName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the algorithm string identifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read(java.lang.String)" class="member-name-link">read</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(java.lang.String)" class="member-name-link">write</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
<div class="block">Detects keypoints in an image (first variant) or image set (second variant).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Image.</dd>
<dd><code>keypoints</code> - The detected keypoints. In the second variant of the method keypoints[i] is a set
     of keypoints detected in images[i] .</dd>
<dd><code>mask</code> - Mask specifying where to look for keypoints (optional). It must be a 8-bit integer
     matrix with non-zero values in the region of interest.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints)</span></div>
<div class="block">Detects keypoints in an image (first variant) or image set (second variant).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Image.</dd>
<dd><code>keypoints</code> - The detected keypoints. In the second variant of the method keypoints[i] is a set
     of keypoints detected in images[i] .
     matrix with non-zero values in the region of interest.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(java.util.List,java.util.List,java.util.List)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - Image set.</dd>
<dd><code>keypoints</code> - The detected keypoints. In the second variant of the method keypoints[i] is a set
     of keypoints detected in images[i] .</dd>
<dd><code>masks</code> - Masks for each input image specifying where to look for keypoints (optional).
     masks[i] is a mask for images[i].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(java.util.List,java.util.List)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - Image set.</dd>
<dd><code>keypoints</code> - The detected keypoints. In the second variant of the method keypoints[i] is a set
     of keypoints detected in images[i] .
     masks[i] is a mask for images[i].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</span></div>
<div class="block">Computes the descriptors for a set of keypoints detected in an image (first variant) or image set
     (second variant).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Image.</dd>
<dd><code>keypoints</code> - Input collection of keypoints. Keypoints for which a descriptor cannot be
     computed are removed. Sometimes new keypoints can be added, for example: SIFT duplicates keypoint
     with several dominant orientations (for each orientation).</dd>
<dd><code>descriptors</code> - Computed descriptors. In the second variant of the method descriptors[i] are
     descriptors computed for a keypoints[i]. Row j is the keypoints (or keypoints[i]) is the
     descriptor for keypoint j-th keypoint.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(java.util.List,java.util.List,java.util.List)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>images</code> - Image set.</dd>
<dd><code>keypoints</code> - Input collection of keypoints. Keypoints for which a descriptor cannot be
     computed are removed. Sometimes new keypoints can be added, for example: SIFT duplicates keypoint
     with several dominant orientations (for each orientation).</dd>
<dd><code>descriptors</code> - Computed descriptors. In the second variant of the method descriptors[i] are
     descriptors computed for a keypoints[i]. Row j is the keypoints (or keypoints[i]) is the
     descriptor for keypoint j-th keypoint.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)">
<h3>detectAndCompute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectAndCompute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors,
 boolean&nbsp;useProvidedKeypoints)</span></div>
<div class="block">Detects keypoints and computes the descriptors</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>mask</code> - automatically generated</dd>
<dd><code>keypoints</code> - automatically generated</dd>
<dd><code>descriptors</code> - automatically generated</dd>
<dd><code>useProvidedKeypoints</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">
<h3>detectAndCompute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectAndCompute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</span></div>
<div class="block">Detects keypoints and computes the descriptors</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>mask</code> - automatically generated</dd>
<dd><code>keypoints</code> - automatically generated</dd>
<dd><code>descriptors</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="descriptorSize()">
<h3>descriptorSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">descriptorSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="descriptorType()">
<h3>descriptorType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">descriptorType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="defaultNorm()">
<h3>defaultNorm</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">defaultNorm</span>()</div>
</section>
</li>
<li>
<section class="detail" id="write(java.lang.String)">
<h3>write</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
</section>
</li>
<li>
<section class="detail" id="read(java.lang.String)">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">empty</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#empty()">Algorithm</a></code></span></div>
<div class="block">Returns true if the Algorithm is empty (e.g. in the very beginning or after unsuccessful read</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../core/Algorithm.html#empty()">empty</a></code>&nbsp;in class&nbsp;<code><a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultName()">
<h3>getDefaultName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultName</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#getDefaultName()">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
