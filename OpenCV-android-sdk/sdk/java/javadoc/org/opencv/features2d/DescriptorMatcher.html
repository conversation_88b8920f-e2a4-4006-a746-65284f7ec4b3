<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>DescriptorMatcher (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: DescriptorMatcher">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class DescriptorMatcher" class="title">Class DescriptorMatcher</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance">org.opencv.features2d.DescriptorMatcher</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></code>, <code><a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DescriptorMatcher</span>
<span class="extends-implements">extends <a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></span></div>
<div class="block">Abstract base class for matching keypoint descriptors.

 It has two groups of match methods: for matching descriptors of an image with another image or with
 an image set.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#BRUTEFORCE" class="member-name-link">BRUTEFORCE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#BRUTEFORCE_HAMMING" class="member-name-link">BRUTEFORCE_HAMMING</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#BRUTEFORCE_HAMMINGLUT" class="member-name-link">BRUTEFORCE_HAMMINGLUT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#BRUTEFORCE_L1" class="member-name-link">BRUTEFORCE_L1</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#BRUTEFORCE_SL2" class="member-name-link">BRUTEFORCE_SL2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#FLANNBASED" class="member-name-link">FLANNBASED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(java.util.List)" class="member-name-link">add</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds descriptors to train a CPU(trainDescCollectionis) or GPU(utrainDescCollectionis) descriptor
     collection.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clear()" class="member-name-link">clear</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clears the train descriptor collections.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clones the matcher.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone(boolean)" class="member-name-link">clone</a><wbr>(boolean&nbsp;emptyTrainData)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clones the matcher.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;matcherType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorMatcherType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates a descriptor matcher of a given type with the default parameters (using default
     constructor).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if there are no train descriptors in the both collections.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainDescriptors()" class="member-name-link">getTrainDescriptors</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a constant link to the train descriptor collection trainDescCollection .</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isMaskSupported()" class="member-name-link">isMaskSupported</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the descriptor matcher supports masking permissible matches.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,java.util.List,int)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List,boolean)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
 boolean&nbsp;compactResult)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat,boolean)" class="member-name-link">knnMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 boolean&nbsp;compactResult)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch,java.util.List)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the best match for each descriptor from a query set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch,org.opencv.core.Mat)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the best match for each descriptor from a query set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,java.util.List,float)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List,boolean)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
 boolean&nbsp;compactResult)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat,boolean)" class="member-name-link">radiusMatch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 boolean&nbsp;compactResult)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read(java.lang.String)" class="member-name-link">read</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#train()" class="member-name-link">train</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains a descriptor matcher

     Trains a descriptor matcher (for example, the flann index).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(java.lang.String)" class="member-name-link">write</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="FLANNBASED">
<h3>FLANNBASED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FLANNBASED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.FLANNBASED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BRUTEFORCE">
<h3>BRUTEFORCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTEFORCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BRUTEFORCE_L1">
<h3>BRUTEFORCE_L1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTEFORCE_L1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_L1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BRUTEFORCE_HAMMING">
<h3>BRUTEFORCE_HAMMING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTEFORCE_HAMMING</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BRUTEFORCE_HAMMINGLUT">
<h3>BRUTEFORCE_HAMMINGLUT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTEFORCE_HAMMINGLUT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMINGLUT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BRUTEFORCE_SL2">
<h3>BRUTEFORCE_SL2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTEFORCE_SL2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_SL2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="add(java.util.List)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</span></div>
<div class="block">Adds descriptors to train a CPU(trainDescCollectionis) or GPU(utrainDescCollectionis) descriptor
     collection.

     If the collection is not empty, the new descriptors are added to existing train descriptors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descriptors</code> - Descriptors to add. Each descriptors[i] is a set of descriptors from the same
     train image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainDescriptors()">
<h3>getTrainDescriptors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;</span>&nbsp;<span class="element-name">getTrainDescriptors</span>()</div>
<div class="block">Returns a constant link to the train descriptor collection trainDescCollection .</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clear()">
<h3>clear</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clear</span>()</div>
<div class="block">Clears the train descriptor collections.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../core/Algorithm.html#clear()">clear</a></code>&nbsp;in class&nbsp;<code><a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">empty</span>()</div>
<div class="block">Returns true if there are no train descriptors in the both collections.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../core/Algorithm.html#empty()">empty</a></code>&nbsp;in class&nbsp;<code><a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isMaskSupported()">
<h3>isMaskSupported</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isMaskSupported</span>()</div>
<div class="block">Returns true if the descriptor matcher supports masking permissible matches.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="train()">
<h3>train</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">train</span>()</div>
<div class="block">Trains a descriptor matcher

     Trains a descriptor matcher (for example, the flann index). In all methods to match, the method
     train() is run every time before matching. Some descriptor matchers (for example, BruteForceMatcher)
     have an empty implementation of this method. Other matchers really train their inner structures (for
     example, FlannBasedMatcher trains flann::Index ).</div>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch,org.opencv.core.Mat)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
<div class="block">Finds the best match for each descriptor from a query set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     In the first variant of this method, the train descriptors are passed as an input argument. In the
     second variant of the method, train descriptors collection that was set by DescriptorMatcher::add is
     used. Optional mask (or masks) can be passed to specify which query and training descriptors can be
     matched. Namely, queryDescriptors[i] can be matched with trainDescriptors[j] only if
     mask.at&lt;uchar&gt;(i,j) is non-zero.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</span></div>
<div class="block">Finds the best match for each descriptor from a query set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.
     descriptors.

     In the first variant of this method, the train descriptors are passed as an input argument. In the
     second variant of the method, train descriptors collection that was set by DescriptorMatcher::add is
     used. Optional mask (or masks) can be passed to specify which query and training descriptors can be
     matched. Namely, queryDescriptors[i] can be matched with trainDescriptors[j] only if
     mask.at&lt;uchar&gt;(i,j) is non-zero.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat,boolean)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 boolean&nbsp;compactResult)</span></div>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k)</span></div>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat,boolean)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 boolean&nbsp;compactResult)</span></div>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</span></div>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance)</span></div>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch,java.util.List)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List,boolean)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
 boolean&nbsp;compactResult)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="knnMatch(org.opencv.core.Mat,java.util.List,int)">
<h3>knnMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">knnMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 int&nbsp;k)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List,boolean)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
 boolean&nbsp;compactResult)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="radiusMatch(org.opencv.core.Mat,java.util.List,float)">
<h3>radiusMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">radiusMatch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
 float&nbsp;maxDistance)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="write(java.lang.String)">
<h3>write</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
</section>
</li>
<li>
<section class="detail" id="read(java.lang.String)">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
</section>
</li>
<li>
<section class="detail" id="clone(boolean)">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span>&nbsp;<span class="element-name">clone</span><wbr><span class="parameters">(boolean&nbsp;emptyTrainData)</span></div>
<div class="block">Clones the matcher.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>emptyTrainData</code> - If emptyTrainData is false, the method creates a deep copy of the object,
     that is, copies both parameters and train data. If emptyTrainData is true, the method creates an
     object copy with the current parameters but with empty train data.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Clones the matcher.

     that is, copies both parameters and train data. If emptyTrainData is true, the method creates an
     object copy with the current parameters but with empty train data.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorMatcherType)</span></div>
<div class="block">Creates a descriptor matcher of a given type with the default parameters (using default
     constructor).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descriptorMatcherType</code> - Descriptor matcher type. Now the following matcher types are
     supported:
 <ul>
   <li>
        <code>BruteForce</code> (it uses L2 )
   </li>
   <li>
        <code>BruteForce-L1</code>
   </li>
   <li>
        <code>BruteForce-Hamming</code>
   </li>
   <li>
        <code>BruteForce-Hamming(2)</code>
   </li>
   <li>
        <code>FlannBased</code>
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;matcherType)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
