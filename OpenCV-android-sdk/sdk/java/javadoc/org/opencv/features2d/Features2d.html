<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Features2d (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: Features2d">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class Features2d" class="title">Class Features2d</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.features2d.Features2d</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Features2d</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DrawMatchesFlags_DEFAULT" class="member-name-link">DrawMatchesFlags_DEFAULT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DrawMatchesFlags_DRAW_OVER_OUTIMG" class="member-name-link">DrawMatchesFlags_DRAW_OVER_OUTIMG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DrawMatchesFlags_DRAW_RICH_KEYPOINTS" class="member-name-link">DrawMatchesFlags_DRAW_RICH_KEYPOINTS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS" class="member-name-link">DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Features2d</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)" class="member-name-link">drawKeypoints</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws keypoints.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawKeypoints</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws keypoints.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.Scalar,int)" class="member-name-link">drawKeypoints</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws keypoints.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws the found matches of keypoints from two images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte,int)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws the found matches of keypoints from two images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws the found matches of keypoints from two images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws the found matches of keypoints from two images.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte,int)" class="member-name-link">drawMatches</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws the found matches of keypoints from two images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat)" class="member-name-link">drawMatchesKnn</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawMatchesKnn</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar)" class="member-name-link">drawMatchesKnn</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,java.util.List)" class="member-name-link">drawMatchesKnn</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,java.util.List,int)" class="member-name-link">drawMatchesKnn</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DrawMatchesFlags_DEFAULT">
<h3>DrawMatchesFlags_DEFAULT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DrawMatchesFlags_DEFAULT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DEFAULT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DrawMatchesFlags_DRAW_OVER_OUTIMG">
<h3>DrawMatchesFlags_DRAW_OVER_OUTIMG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DrawMatchesFlags_DRAW_OVER_OUTIMG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_OVER_OUTIMG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">
<h3>DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DrawMatchesFlags_DRAW_RICH_KEYPOINTS">
<h3>DrawMatchesFlags_DRAW_RICH_KEYPOINTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DrawMatchesFlags_DRAW_RICH_KEYPOINTS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_RICH_KEYPOINTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Features2d</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Features2d</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.Scalar,int)">
<h3>drawKeypoints</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawKeypoints</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color,
 int&nbsp;flags)</span></div>
<div class="block">Draws keypoints.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>color</code> - Color of keypoints.</dd>
<dd><code>flags</code> - Flags setting drawing features. Possible flags bit values are defined by
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawKeypoints</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawKeypoints</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color)</span></div>
<div class="block">Draws keypoints.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>color</code> - Color of keypoints.
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawKeypoints(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">
<h3>drawKeypoints</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawKeypoints</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage)</span></div>
<div class="block">Draws keypoints.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte,int)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
 int&nbsp;flags)</span></div>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.</dd>
<dd><code>matchesMask</code> - Mask determining which matches are drawn. If the mask is empty, all matches are
 drawn.</dd>
<dd><code>flags</code> - Flags setting drawing features. Possible flags bit values are defined by
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</span></div>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.</dd>
<dd><code>matchesMask</code> - Mask determining which matches are drawn. If the mask is empty, all matches are
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</span></div>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</span></div>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</span></div>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.
 , the color is generated randomly.
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte,int)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
 int&nbsp;flags)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar,org.opencv.core.MatOfByte)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar,org.opencv.core.Scalar)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int,org.opencv.core.Scalar)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatches(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.MatOfDMatch,org.opencv.core.Mat,int)">
<h3>drawMatches</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatches</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 int&nbsp;matchesThickness)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,java.util.List,int)">
<h3>drawMatchesKnn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatchesKnn</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask,
 int&nbsp;flags)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar,java.util.List)">
<h3>drawMatchesKnn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatchesKnn</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar,org.opencv.core.Scalar)">
<h3>drawMatchesKnn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatchesKnn</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawMatchesKnn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatchesKnn</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawMatchesKnn(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,java.util.List,org.opencv.core.Mat)">
<h3>drawMatchesKnn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawMatchesKnn</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
 <a href="../core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
