<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>ORB (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: ORB">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class ORB" class="title">Class ORB</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a>
<div class="inheritance">org.opencv.features2d.ORB</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ORB</span>
<span class="extends-implements">extends <a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></span></div>
<div class="block">Class implementing the ORB (*oriented BRIEF*) keypoint detector and descriptor extractor

 described in CITE: RRKB11 . The algorithm uses FAST in pyramids to detect stable keypoints, selects
 the strongest features using FAST or Harris response, finds their orientation using first-order
 moments and computes the descriptors using BRIEF (where the coordinates of random point pairs (or
 k-tuples) are rotated according to the measured orientation).</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#FAST_SCORE" class="member-name-link">FAST_SCORE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#HARRIS_SCORE" class="member-name-link">HARRIS_SCORE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor

     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType,
 int&nbsp;patchSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ORB.html" title="class in org.opencv.features2d">ORB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,float,int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType,
 int&nbsp;patchSize,
 int&nbsp;fastThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The ORB constructor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultName()" class="member-name-link">getDefaultName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the algorithm string identifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEdgeThreshold()" class="member-name-link">getEdgeThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFastThreshold()" class="member-name-link">getFastThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFirstLevel()" class="member-name-link">getFirstLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxFeatures()" class="member-name-link">getMaxFeatures</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNLevels()" class="member-name-link">getNLevels</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPatchSize()" class="member-name-link">getPatchSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getScaleFactor()" class="member-name-link">getScaleFactor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getScoreType()" class="member-name-link">getScoreType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWTA_K()" class="member-name-link">getWTA_K</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeThreshold(int)" class="member-name-link">setEdgeThreshold</a><wbr>(int&nbsp;edgeThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFastThreshold(int)" class="member-name-link">setFastThreshold</a><wbr>(int&nbsp;fastThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFirstLevel(int)" class="member-name-link">setFirstLevel</a><wbr>(int&nbsp;firstLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxFeatures(int)" class="member-name-link">setMaxFeatures</a><wbr>(int&nbsp;maxFeatures)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNLevels(int)" class="member-name-link">setNLevels</a><wbr>(int&nbsp;nlevels)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPatchSize(int)" class="member-name-link">setPatchSize</a><wbr>(int&nbsp;patchSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setScaleFactor(double)" class="member-name-link">setScaleFactor</a><wbr>(double&nbsp;scaleFactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setScoreType(int)" class="member-name-link">setScoreType</a><wbr>(int&nbsp;scoreType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWTA_K(int)" class="member-name-link">setWTA_K</a><wbr>(int&nbsp;wta_k)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.features2d.Feature2D">Methods inherited from class&nbsp;org.opencv.features2d.<a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="Feature2D.html#compute(java.util.List,java.util.List,java.util.List)">compute</a>, <a href="Feature2D.html#compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">compute</a>, <a href="Feature2D.html#defaultNorm()">defaultNorm</a>, <a href="Feature2D.html#descriptorSize()">descriptorSize</a>, <a href="Feature2D.html#descriptorType()">descriptorType</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detect</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detectAndCompute</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)">detectAndCompute</a>, <a href="Feature2D.html#empty()">empty</a>, <a href="Feature2D.html#read(java.lang.String)">read</a>, <a href="Feature2D.html#write(java.lang.String)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="HARRIS_SCORE">
<h3>HARRIS_SCORE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">HARRIS_SCORE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.ORB.HARRIS_SCORE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="FAST_SCORE">
<h3>FAST_SCORE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FAST_SCORE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.features2d.ORB.FAST_SCORE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType,
 int&nbsp;patchSize,
 int&nbsp;fastThreshold)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.</dd>
<dd><code>patchSize</code> - size of the patch used by the oriented BRIEF descriptor. Of course, on smaller
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dd><code>fastThreshold</code> - the fast threshold</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType,
 int&nbsp;patchSize)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.</dd>
<dd><code>patchSize</code> - size of the patch used by the oriented BRIEF descriptor. Of course, on smaller
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K,
 int&nbsp;scoreType)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel,
 int&nbsp;WTA_K)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold,
 int&nbsp;firstLevel)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels,
 int&nbsp;edgeThreshold)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor,
 int&nbsp;nlevels)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,float)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 float&nbsp;scaleFactor)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures)</span></div>
<div class="block">The ORB constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">The ORB constructor

     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMaxFeatures(int)">
<h3>setMaxFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxFeatures</span><wbr><span class="parameters">(int&nbsp;maxFeatures)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxFeatures()">
<h3>getMaxFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxFeatures</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setScaleFactor(double)">
<h3>setScaleFactor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setScaleFactor</span><wbr><span class="parameters">(double&nbsp;scaleFactor)</span></div>
</section>
</li>
<li>
<section class="detail" id="getScaleFactor()">
<h3>getScaleFactor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getScaleFactor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setNLevels(int)">
<h3>setNLevels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNLevels</span><wbr><span class="parameters">(int&nbsp;nlevels)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNLevels()">
<h3>getNLevels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNLevels</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setEdgeThreshold(int)">
<h3>setEdgeThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEdgeThreshold</span><wbr><span class="parameters">(int&nbsp;edgeThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getEdgeThreshold()">
<h3>getEdgeThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getEdgeThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setFirstLevel(int)">
<h3>setFirstLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFirstLevel</span><wbr><span class="parameters">(int&nbsp;firstLevel)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFirstLevel()">
<h3>getFirstLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFirstLevel</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setWTA_K(int)">
<h3>setWTA_K</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWTA_K</span><wbr><span class="parameters">(int&nbsp;wta_k)</span></div>
</section>
</li>
<li>
<section class="detail" id="getWTA_K()">
<h3>getWTA_K</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getWTA_K</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setScoreType(int)">
<h3>setScoreType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setScoreType</span><wbr><span class="parameters">(int&nbsp;scoreType)</span></div>
</section>
</li>
<li>
<section class="detail" id="getScoreType()">
<h3>getScoreType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getScoreType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPatchSize(int)">
<h3>setPatchSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPatchSize</span><wbr><span class="parameters">(int&nbsp;patchSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPatchSize()">
<h3>getPatchSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPatchSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setFastThreshold(int)">
<h3>setFastThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFastThreshold</span><wbr><span class="parameters">(int&nbsp;fastThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getFastThreshold()">
<h3>getFastThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFastThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultName()">
<h3>getDefaultName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultName</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#getDefaultName()">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Feature2D.html#getDefaultName()">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
