<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.features2d (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.features2d" class="title">Package org.opencv.features2d</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.features2d</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class for implementing the wrapper which makes detectors and extractors to be affine invariant,
 described as ASIFT in CITE: YM11 .</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AgastFeatureDetector.html" title="class in org.opencv.features2d">AgastFeatureDetector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wrapping class for feature detection using the AGAST method.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class implementing the AKAZE keypoint detector and descriptor extractor, described in CITE: ANB13.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Brute-force descriptor matcher.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BOWImgDescriptorExtractor.html" title="class in org.opencv.features2d">BOWImgDescriptorExtractor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class to compute an image descriptor using the *bag of visual words*.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BOWKMeansTrainer.html" title="class in org.opencv.features2d">BOWKMeansTrainer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">kmeans -based class to train visual vocabulary using the *bag of visual words* approach.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BOWTrainer.html" title="class in org.opencv.features2d">BOWTrainer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract base class for training the *bag of visual words* vocabulary from a set of descriptors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BRISK.html" title="class in org.opencv.features2d">BRISK</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class implementing the BRISK keypoint detector and descriptor extractor, described in CITE: LCS11 .</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract base class for matching keypoint descriptors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FastFeatureDetector.html" title="class in org.opencv.features2d">FastFeatureDetector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wrapping class for feature detection using the FAST method.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract base class for 2D image feature detectors and descriptor extractors</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Features2d.html" title="class in org.opencv.features2d">Features2d</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Flann-based descriptor matcher.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wrapping class for feature detection using the goodFeaturesToTrack function.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="KAZE.html" title="class in org.opencv.features2d">KAZE</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class implementing the KAZE keypoint detector and descriptor extractor, described in CITE: ABD12 .</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Maximally stable extremal region extractor

 The class encapsulates all the parameters of the %MSER extraction algorithm (see [wiki
 article](http://en.wikipedia.org/wiki/Maximally_stable_extremal_regions)).</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ORB.html" title="class in org.opencv.features2d">ORB</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class implementing the ORB (*oriented BRIEF*) keypoint detector and descriptor extractor

 described in CITE: RRKB11 .</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class for extracting keypoints and computing descriptors using the Scale Invariant Feature Transform
 (SIFT) algorithm by D.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SimpleBlobDetector.html" title="class in org.opencv.features2d">SimpleBlobDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class for extracting blobs from an image.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SimpleBlobDetector_Params.html" title="class in org.opencv.features2d">SimpleBlobDetector_Params</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
