<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>MSER (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: MSER">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class MSER" class="title">Class MSER</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a>
<div class="inheritance">org.opencv.features2d.MSER</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">MSER</span>
<span class="extends-implements">extends <a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></span></div>
<div class="block">Maximally stable extremal region extractor

 The class encapsulates all the parameters of the %MSER extraction algorithm (see [wiki
 article](http://en.wikipedia.org/wiki/Maximally_stable_extremal_regions)).

 <ul>
   <li>
  there are two different implementation of %MSER: one for grey image, one for color image
   </li>
 </ul>

 <ul>
   <li>
  the grey image algorithm is taken from: CITE: nister2008linear ;  the paper claims to be faster
 than union-find method; it actually get 1.5~2m/s on my centrino L7200 1.2GHz laptop.
   </li>
 </ul>

 <ul>
   <li>
  the color image algorithm is taken from: CITE: forssen2007maximally ; it should be much slower
 than grey image method ( 3~4 times )
   </li>
 </ul>

 <ul>
   <li>
  (Python) A complete example showing the use of the %MSER detector can be found at samples/python/mser.py
   </li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;delta)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double,double)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double,double,int)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double,double,int,double)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double,double,int,double,double)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold,
 double&nbsp;min_margin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MSER.html" title="class in org.opencv.features2d">MSER</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,double,double,int,double,double,int)" class="member-name-link">create</a><wbr>(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold,
 double&nbsp;min_margin,
 int&nbsp;edge_blur_size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Full constructor for %MSER detector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectRegions(org.opencv.core.Mat,java.util.List,org.opencv.core.MatOfRect)" class="member-name-link">detectRegions</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;msers,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detect %MSER regions</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAreaThreshold()" class="member-name-link">getAreaThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultName()" class="member-name-link">getDefaultName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the algorithm string identifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDelta()" class="member-name-link">getDelta</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEdgeBlurSize()" class="member-name-link">getEdgeBlurSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxArea()" class="member-name-link">getMaxArea</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxEvolution()" class="member-name-link">getMaxEvolution</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxVariation()" class="member-name-link">getMaxVariation</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMinArea()" class="member-name-link">getMinArea</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMinDiversity()" class="member-name-link">getMinDiversity</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMinMargin()" class="member-name-link">getMinMargin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPass2Only()" class="member-name-link">getPass2Only</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAreaThreshold(double)" class="member-name-link">setAreaThreshold</a><wbr>(double&nbsp;areaThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDelta(int)" class="member-name-link">setDelta</a><wbr>(int&nbsp;delta)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeBlurSize(int)" class="member-name-link">setEdgeBlurSize</a><wbr>(int&nbsp;edge_blur_size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxArea(int)" class="member-name-link">setMaxArea</a><wbr>(int&nbsp;maxArea)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxEvolution(int)" class="member-name-link">setMaxEvolution</a><wbr>(int&nbsp;maxEvolution)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxVariation(double)" class="member-name-link">setMaxVariation</a><wbr>(double&nbsp;maxVariation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinArea(int)" class="member-name-link">setMinArea</a><wbr>(int&nbsp;minArea)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinDiversity(double)" class="member-name-link">setMinDiversity</a><wbr>(double&nbsp;minDiversity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinMargin(double)" class="member-name-link">setMinMargin</a><wbr>(double&nbsp;min_margin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPass2Only(boolean)" class="member-name-link">setPass2Only</a><wbr>(boolean&nbsp;f)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.features2d.Feature2D">Methods inherited from class&nbsp;org.opencv.features2d.<a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="Feature2D.html#compute(java.util.List,java.util.List,java.util.List)">compute</a>, <a href="Feature2D.html#compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">compute</a>, <a href="Feature2D.html#defaultNorm()">defaultNorm</a>, <a href="Feature2D.html#descriptorSize()">descriptorSize</a>, <a href="Feature2D.html#descriptorType()">descriptorType</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detect</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detectAndCompute</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)">detectAndCompute</a>, <a href="Feature2D.html#empty()">empty</a>, <a href="Feature2D.html#read(java.lang.String)">read</a>, <a href="Feature2D.html#write(java.lang.String)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double,double,int,double,double,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold,
 double&nbsp;min_margin,
 int&nbsp;edge_blur_size)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dd><code>min_margin</code> - for color image, ignore too small margin</dd>
<dd><code>edge_blur_size</code> - for color image, the aperture size for edge blur</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double,double,int,double,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold,
 double&nbsp;min_margin)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dd><code>min_margin</code> - for color image, ignore too small margin</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double,double,int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution,
 double&nbsp;area_threshold)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double,double,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity,
 int&nbsp;max_evolution)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation,
 double&nbsp;min_diversity)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area,
 double&nbsp;max_variation)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area,
 int&nbsp;max_area)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta,
 int&nbsp;min_area)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;delta)</span></div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MSER.html" title="class in org.opencv.features2d">MSER</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Full constructor for %MSER detector</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectRegions(org.opencv.core.Mat,java.util.List,org.opencv.core.MatOfRect)">
<h3>detectRegions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectRegions</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;msers,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes)</span></div>
<div class="block">Detect %MSER regions</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image (8UC1, 8UC3 or 8UC4, must be greater or equal than 3x3)</dd>
<dd><code>msers</code> - resulting list of point sets</dd>
<dd><code>bboxes</code> - resulting bounding boxes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDelta(int)">
<h3>setDelta</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDelta</span><wbr><span class="parameters">(int&nbsp;delta)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDelta()">
<h3>getDelta</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDelta</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMinArea(int)">
<h3>setMinArea</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinArea</span><wbr><span class="parameters">(int&nbsp;minArea)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMinArea()">
<h3>getMinArea</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMinArea</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMaxArea(int)">
<h3>setMaxArea</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxArea</span><wbr><span class="parameters">(int&nbsp;maxArea)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxArea()">
<h3>getMaxArea</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxArea</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMaxVariation(double)">
<h3>setMaxVariation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxVariation</span><wbr><span class="parameters">(double&nbsp;maxVariation)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxVariation()">
<h3>getMaxVariation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getMaxVariation</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMinDiversity(double)">
<h3>setMinDiversity</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinDiversity</span><wbr><span class="parameters">(double&nbsp;minDiversity)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMinDiversity()">
<h3>getMinDiversity</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getMinDiversity</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMaxEvolution(int)">
<h3>setMaxEvolution</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxEvolution</span><wbr><span class="parameters">(int&nbsp;maxEvolution)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxEvolution()">
<h3>getMaxEvolution</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxEvolution</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setAreaThreshold(double)">
<h3>setAreaThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAreaThreshold</span><wbr><span class="parameters">(double&nbsp;areaThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getAreaThreshold()">
<h3>getAreaThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getAreaThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMinMargin(double)">
<h3>setMinMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinMargin</span><wbr><span class="parameters">(double&nbsp;min_margin)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMinMargin()">
<h3>getMinMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getMinMargin</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setEdgeBlurSize(int)">
<h3>setEdgeBlurSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEdgeBlurSize</span><wbr><span class="parameters">(int&nbsp;edge_blur_size)</span></div>
</section>
</li>
<li>
<section class="detail" id="getEdgeBlurSize()">
<h3>getEdgeBlurSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getEdgeBlurSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPass2Only(boolean)">
<h3>setPass2Only</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPass2Only</span><wbr><span class="parameters">(boolean&nbsp;f)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPass2Only()">
<h3>getPass2Only</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getPass2Only</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultName()">
<h3>getDefaultName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultName</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#getDefaultName()">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Feature2D.html#getDefaultName()">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
