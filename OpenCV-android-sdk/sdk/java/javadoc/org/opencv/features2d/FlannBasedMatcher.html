<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>FlannBasedMatcher (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: FlannBasedMatcher">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class FlannBasedMatcher" class="title">Class FlannBasedMatcher</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="DescriptorMatcher.html" title="class in org.opencv.features2d">org.opencv.features2d.DescriptorMatcher</a>
<div class="inheritance">org.opencv.features2d.FlannBasedMatcher</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FlannBasedMatcher</span>
<span class="extends-implements">extends <a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span></div>
<div class="block">Flann-based descriptor matcher.

 This matcher trains cv::flann::Index on a train descriptor collection and calls its nearest search
 methods to find the best matches. So, this matcher may be faster when matching a large train
 collection than the brute force matcher. FlannBasedMatcher does not support masking permissible
 matches of descriptor sets because flann::Index does not support this. :</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.features2d.DescriptorMatcher">Fields inherited from class&nbsp;org.opencv.features2d.<a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></h3>
<code><a href="DescriptorMatcher.html#BRUTEFORCE">BRUTEFORCE</a>, <a href="DescriptorMatcher.html#BRUTEFORCE_HAMMING">BRUTEFORCE_HAMMING</a>, <a href="DescriptorMatcher.html#BRUTEFORCE_HAMMINGLUT">BRUTEFORCE_HAMMINGLUT</a>, <a href="DescriptorMatcher.html#BRUTEFORCE_L1">BRUTEFORCE_L1</a>, <a href="DescriptorMatcher.html#BRUTEFORCE_SL2">BRUTEFORCE_SL2</a>, <a href="DescriptorMatcher.html#FLANNBASED">FLANNBASED</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">FlannBasedMatcher</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.features2d.DescriptorMatcher">Methods inherited from class&nbsp;org.opencv.features2d.<a href="DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></h3>
<code><a href="DescriptorMatcher.html#add(java.util.List)">add</a>, <a href="DescriptorMatcher.html#clear()">clear</a>, <a href="DescriptorMatcher.html#clone()">clone</a>, <a href="DescriptorMatcher.html#clone(boolean)">clone</a>, <a href="DescriptorMatcher.html#create(int)">create</a>, <a href="DescriptorMatcher.html#create(java.lang.String)">create</a>, <a href="DescriptorMatcher.html#empty()">empty</a>, <a href="DescriptorMatcher.html#getTrainDescriptors()">getTrainDescriptors</a>, <a href="DescriptorMatcher.html#isMaskSupported()">isMaskSupported</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,java.util.List,int)">knnMatch</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List)">knnMatch</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,java.util.List,int,java.util.List,boolean)">knnMatch</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int)">knnMatch</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat)">knnMatch</a>, <a href="DescriptorMatcher.html#knnMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,int,org.opencv.core.Mat,boolean)">knnMatch</a>, <a href="DescriptorMatcher.html#match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch)">match</a>, <a href="DescriptorMatcher.html#match(org.opencv.core.Mat,org.opencv.core.MatOfDMatch,java.util.List)">match</a>, <a href="DescriptorMatcher.html#match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch)">match</a>, <a href="DescriptorMatcher.html#match(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfDMatch,org.opencv.core.Mat)">match</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,java.util.List,float)">radiusMatch</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List)">radiusMatch</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,java.util.List,float,java.util.List,boolean)">radiusMatch</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float)">radiusMatch</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat)">radiusMatch</a>, <a href="DescriptorMatcher.html#radiusMatch(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,float,org.opencv.core.Mat,boolean)">radiusMatch</a>, <a href="DescriptorMatcher.html#read(java.lang.String)">read</a>, <a href="DescriptorMatcher.html#train()">train</a>, <a href="DescriptorMatcher.html#write(java.lang.String)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>FlannBasedMatcher</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FlannBasedMatcher</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></span>&nbsp;<span class="element-name">create</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
