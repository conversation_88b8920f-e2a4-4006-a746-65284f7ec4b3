<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>SIFT (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: SIFT">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class SIFT" class="title">Class SIFT</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a>
<div class="inheritance">org.opencv.features2d.SIFT</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SIFT</span>
<span class="extends-implements">extends <a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></span></div>
<div class="block">Class for extracting keypoints and computing descriptors using the Scale Invariant Feature Transform
 (SIFT) algorithm by D. Lowe CITE: Lowe04 .</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">(measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double,double)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double,double,double)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double,double,double,boolean)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 boolean&nbsp;enable_precise_upscale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double,double,double,int)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 int&nbsp;descriptorType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create SIFT with specified descriptorType.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,double,double,double,int,boolean)" class="member-name-link">create</a><wbr>(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 int&nbsp;descriptorType,
 boolean&nbsp;enable_precise_upscale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create SIFT with specified descriptorType.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getContrastThreshold()" class="member-name-link">getContrastThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultName()" class="member-name-link">getDefaultName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the algorithm string identifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEdgeThreshold()" class="member-name-link">getEdgeThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNFeatures()" class="member-name-link">getNFeatures</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNOctaveLayers()" class="member-name-link">getNOctaveLayers</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSigma()" class="member-name-link">getSigma</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setContrastThreshold(double)" class="member-name-link">setContrastThreshold</a><wbr>(double&nbsp;contrastThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEdgeThreshold(double)" class="member-name-link">setEdgeThreshold</a><wbr>(double&nbsp;edgeThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNFeatures(int)" class="member-name-link">setNFeatures</a><wbr>(int&nbsp;maxFeatures)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNOctaveLayers(int)" class="member-name-link">setNOctaveLayers</a><wbr>(int&nbsp;nOctaveLayers)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSigma(double)" class="member-name-link">setSigma</a><wbr>(double&nbsp;sigma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.features2d.Feature2D">Methods inherited from class&nbsp;org.opencv.features2d.<a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="Feature2D.html#compute(java.util.List,java.util.List,java.util.List)">compute</a>, <a href="Feature2D.html#compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">compute</a>, <a href="Feature2D.html#defaultNorm()">defaultNorm</a>, <a href="Feature2D.html#descriptorSize()">descriptorSize</a>, <a href="Feature2D.html#descriptorType()">descriptorType</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detect</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detectAndCompute</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)">detectAndCompute</a>, <a href="Feature2D.html#empty()">empty</a>, <a href="Feature2D.html#read(java.lang.String)">read</a>, <a href="Feature2D.html#write(java.lang.String)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double,double,double,boolean)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 boolean&nbsp;enable_precise_upscale)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>enable_precise_upscale</code> - Whether to enable precise upscaling in the scale pyramid, which maps
     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double,double,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">(measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double,double,double,int,boolean)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 int&nbsp;descriptorType,
 boolean&nbsp;enable_precise_upscale)</span></div>
<div class="block">Create SIFT with specified descriptorType.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>descriptorType</code> - The type of descriptors. Only CV_32F and CV_8U are supported.</dd>
<dd><code>enable_precise_upscale</code> - Whether to enable precise upscaling in the scale pyramid, which maps
     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,double,double,double,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SIFT.html" title="class in org.opencv.features2d">SIFT</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;nfeatures,
 int&nbsp;nOctaveLayers,
 double&nbsp;contrastThreshold,
 double&nbsp;edgeThreshold,
 double&nbsp;sigma,
 int&nbsp;descriptorType)</span></div>
<div class="block">Create SIFT with specified descriptorType.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>descriptorType</code> - The type of descriptors. Only CV_32F and CV_8U are supported.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultName()">
<h3>getDefaultName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultName</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#getDefaultName()">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Feature2D.html#getDefaultName()">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNFeatures(int)">
<h3>setNFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNFeatures</span><wbr><span class="parameters">(int&nbsp;maxFeatures)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNFeatures()">
<h3>getNFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNFeatures</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setNOctaveLayers(int)">
<h3>setNOctaveLayers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNOctaveLayers</span><wbr><span class="parameters">(int&nbsp;nOctaveLayers)</span></div>
</section>
</li>
<li>
<section class="detail" id="getNOctaveLayers()">
<h3>getNOctaveLayers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNOctaveLayers</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setContrastThreshold(double)">
<h3>setContrastThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setContrastThreshold</span><wbr><span class="parameters">(double&nbsp;contrastThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getContrastThreshold()">
<h3>getContrastThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getContrastThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setEdgeThreshold(double)">
<h3>setEdgeThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEdgeThreshold</span><wbr><span class="parameters">(double&nbsp;edgeThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getEdgeThreshold()">
<h3>getEdgeThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getEdgeThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setSigma(double)">
<h3>setSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSigma</span><wbr><span class="parameters">(double&nbsp;sigma)</span></div>
</section>
</li>
<li>
<section class="detail" id="getSigma()">
<h3>getSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getSigma</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
