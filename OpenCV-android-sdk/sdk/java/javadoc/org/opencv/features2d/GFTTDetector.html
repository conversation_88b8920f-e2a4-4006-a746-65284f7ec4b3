<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>GFTTDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.features2d, class: GFTTDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.features2d</a></div>
<h1 title="Class GFTTDetector" class="title">Class GFTTDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a>
<div class="inheritance">org.opencv.features2d.GFTTDetector</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">GFTTDetector</span>
<span class="extends-implements">extends <a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></span></div>
<div class="block">Wrapping class for feature detection using the goodFeaturesToTrack function. :</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int,boolean)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 boolean&nbsp;useHarrisDetector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int,boolean,double)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 boolean&nbsp;useHarrisDetector,
 double&nbsp;k)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int,int,boolean)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize,
 boolean&nbsp;useHarrisDetector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,double,double,int,int,boolean,double)" class="member-name-link">create</a><wbr>(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize,
 boolean&nbsp;useHarrisDetector,
 double&nbsp;k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBlockSize()" class="member-name-link">getBlockSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultName()" class="member-name-link">getDefaultName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the algorithm string identifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGradientSize()" class="member-name-link">getGradientSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHarrisDetector()" class="member-name-link">getHarrisDetector</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getK()" class="member-name-link">getK</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxFeatures()" class="member-name-link">getMaxFeatures</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMinDistance()" class="member-name-link">getMinDistance</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getQualityLevel()" class="member-name-link">getQualityLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBlockSize(int)" class="member-name-link">setBlockSize</a><wbr>(int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGradientSize(int)" class="member-name-link">setGradientSize</a><wbr>(int&nbsp;gradientSize_)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHarrisDetector(boolean)" class="member-name-link">setHarrisDetector</a><wbr>(boolean&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setK(double)" class="member-name-link">setK</a><wbr>(double&nbsp;k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxFeatures(int)" class="member-name-link">setMaxFeatures</a><wbr>(int&nbsp;maxFeatures)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinDistance(double)" class="member-name-link">setMinDistance</a><wbr>(double&nbsp;minDistance)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setQualityLevel(double)" class="member-name-link">setQualityLevel</a><wbr>(double&nbsp;qlevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.features2d.Feature2D">Methods inherited from class&nbsp;org.opencv.features2d.<a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="Feature2D.html#compute(java.util.List,java.util.List,java.util.List)">compute</a>, <a href="Feature2D.html#compute(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">compute</a>, <a href="Feature2D.html#defaultNorm()">defaultNorm</a>, <a href="Feature2D.html#descriptorSize()">descriptorSize</a>, <a href="Feature2D.html#descriptorType()">descriptorType</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(java.util.List,java.util.List,java.util.List)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint)">detect</a>, <a href="Feature2D.html#detect(org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detect</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat)">detectAndCompute</a>, <a href="Feature2D.html#detectAndCompute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfKeyPoint,org.opencv.core.Mat,boolean)">detectAndCompute</a>, <a href="Feature2D.html#empty()">empty</a>, <a href="Feature2D.html#read(java.lang.String)">read</a>, <a href="Feature2D.html#write(java.lang.String)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int,boolean,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 boolean&nbsp;useHarrisDetector,
 double&nbsp;k)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int,boolean)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 boolean&nbsp;useHarrisDetector)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners)</span></div>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span>()</div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int,int,boolean,double)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize,
 boolean&nbsp;useHarrisDetector,
 double&nbsp;k)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int,int,boolean)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize,
 boolean&nbsp;useHarrisDetector)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,double,double,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;maxCorners,
 double&nbsp;qualityLevel,
 double&nbsp;minDistance,
 int&nbsp;blockSize,
 int&nbsp;gradiantSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="setMaxFeatures(int)">
<h3>setMaxFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxFeatures</span><wbr><span class="parameters">(int&nbsp;maxFeatures)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMaxFeatures()">
<h3>getMaxFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxFeatures</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setQualityLevel(double)">
<h3>setQualityLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setQualityLevel</span><wbr><span class="parameters">(double&nbsp;qlevel)</span></div>
</section>
</li>
<li>
<section class="detail" id="getQualityLevel()">
<h3>getQualityLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getQualityLevel</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMinDistance(double)">
<h3>setMinDistance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinDistance</span><wbr><span class="parameters">(double&nbsp;minDistance)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMinDistance()">
<h3>getMinDistance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getMinDistance</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setBlockSize(int)">
<h3>setBlockSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBlockSize</span><wbr><span class="parameters">(int&nbsp;blockSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="getBlockSize()">
<h3>getBlockSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getBlockSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setGradientSize(int)">
<h3>setGradientSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGradientSize</span><wbr><span class="parameters">(int&nbsp;gradientSize_)</span></div>
</section>
</li>
<li>
<section class="detail" id="getGradientSize()">
<h3>getGradientSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getGradientSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setHarrisDetector(boolean)">
<h3>setHarrisDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHarrisDetector</span><wbr><span class="parameters">(boolean&nbsp;val)</span></div>
</section>
</li>
<li>
<section class="detail" id="getHarrisDetector()">
<h3>getHarrisDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getHarrisDetector</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setK(double)">
<h3>setK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setK</span><wbr><span class="parameters">(double&nbsp;k)</span></div>
</section>
</li>
<li>
<section class="detail" id="getK()">
<h3>getK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getK</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultName()">
<h3>getDefaultName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultName</span>()</div>
<div class="block"><span class="descfrm-type-label">Description copied from class:&nbsp;<code><a href="../core/Algorithm.html#getDefaultName()">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Feature2D.html#getDefaultName()">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
