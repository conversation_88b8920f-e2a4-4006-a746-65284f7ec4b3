<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>StereoSGBM (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.calib3d, class: StereoSGBM">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.calib3d</a></div>
<h1 title="Class StereoSGBM" class="title">Class StereoSGBM</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StereoMatcher.html" title="class in org.opencv.calib3d">org.opencv.calib3d.StereoMatcher</a>
<div class="inheritance">org.opencv.calib3d.StereoSGBM</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">StereoSGBM</span>
<span class="extends-implements">extends <a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></span></div>
<div class="block">The class implements the modified H. Hirschmuller algorithm CITE: HH08 that differs from the original
 one as follows:

 <ul>
   <li>
    By default, the algorithm is single-pass, which means that you consider only 5 directions
 instead of 8. Set mode=StereoSGBM::MODE_HH in createStereoSGBM to run the full variant of the
 algorithm but beware that it may consume a lot of memory.
   </li>
   <li>
    The algorithm matches blocks, not individual pixels. Though, setting blockSize=1 reduces the
 blocks to single pixels.
   </li>
   <li>
    Mutual information cost function is not implemented. Instead, a simpler Birchfield-Tomasi
 sub-pixel metric from CITE: BT98 is used. Though, the color images are supported as well.
   </li>
   <li>
    Some pre- and post- processing steps from K. Konolige algorithm StereoBM are included, for
 example: pre-filtering (StereoBM::PREFILTER_XSOBEL type) and post-filtering (uniqueness
 check, quadratic interpolation and speckle filtering).
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
       (Python) An example illustrating the use of the StereoSGBM matching algorithm can be found
         at opencv_source_code/samples/python/stereo_match.py
   </li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MODE_HH" class="member-name-link">MODE_HH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MODE_HH4" class="member-name-link">MODE_HH4</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MODE_SGBM" class="member-name-link">MODE_SGBM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MODE_SGBM_3WAY" class="member-name-link">MODE_SGBM_3WAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.calib3d.StereoMatcher">Fields inherited from class&nbsp;org.opencv.calib3d.<a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="StereoMatcher.html#DISP_SCALE">DISP_SCALE</a>, <a href="StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object

     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize,
 int&nbsp;speckleRange)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int,int,int,int,int,int,int,int,int,int)" class="member-name-link">create</a><wbr>(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize,
 int&nbsp;speckleRange,
 int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoSGBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode()" class="member-name-link">getMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getP1()" class="member-name-link">getP1</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getP2()" class="member-name-link">getP2</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreFilterCap()" class="member-name-link">getPreFilterCap</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUniquenessRatio()" class="member-name-link">getUniquenessRatio</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMode(int)" class="member-name-link">setMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setP1(int)" class="member-name-link">setP1</a><wbr>(int&nbsp;P1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setP2(int)" class="member-name-link">setP2</a><wbr>(int&nbsp;P2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreFilterCap(int)" class="member-name-link">setPreFilterCap</a><wbr>(int&nbsp;preFilterCap)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUniquenessRatio(int)" class="member-name-link">setUniquenessRatio</a><wbr>(int&nbsp;uniquenessRatio)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.calib3d.StereoMatcher">Methods inherited from class&nbsp;org.opencv.calib3d.<a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="StereoMatcher.html#compute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">compute</a>, <a href="StereoMatcher.html#getBlockSize()">getBlockSize</a>, <a href="StereoMatcher.html#getDisp12MaxDiff()">getDisp12MaxDiff</a>, <a href="StereoMatcher.html#getMinDisparity()">getMinDisparity</a>, <a href="StereoMatcher.html#getNumDisparities()">getNumDisparities</a>, <a href="StereoMatcher.html#getSpeckleRange()">getSpeckleRange</a>, <a href="StereoMatcher.html#getSpeckleWindowSize()">getSpeckleWindowSize</a>, <a href="StereoMatcher.html#setBlockSize(int)">setBlockSize</a>, <a href="StereoMatcher.html#setDisp12MaxDiff(int)">setDisp12MaxDiff</a>, <a href="StereoMatcher.html#setMinDisparity(int)">setMinDisparity</a>, <a href="StereoMatcher.html#setNumDisparities(int)">setNumDisparities</a>, <a href="StereoMatcher.html#setSpeckleRange(int)">setSpeckleRange</a>, <a href="StereoMatcher.html#setSpeckleWindowSize(int)">setSpeckleWindowSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="MODE_SGBM">
<h3>MODE_SGBM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODE_SGBM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MODE_HH">
<h3>MODE_HH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODE_HH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_HH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MODE_SGBM_3WAY">
<h3>MODE_SGBM_3WAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODE_SGBM_3WAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM_3WAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MODE_HH4">
<h3>MODE_HH4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODE_HH4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_HH4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPreFilterCap()">
<h3>getPreFilterCap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPreFilterCap</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPreFilterCap(int)">
<h3>setPreFilterCap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreFilterCap</span><wbr><span class="parameters">(int&nbsp;preFilterCap)</span></div>
</section>
</li>
<li>
<section class="detail" id="getUniquenessRatio()">
<h3>getUniquenessRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUniquenessRatio</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setUniquenessRatio(int)">
<h3>setUniquenessRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUniquenessRatio</span><wbr><span class="parameters">(int&nbsp;uniquenessRatio)</span></div>
</section>
</li>
<li>
<section class="detail" id="getP1()">
<h3>getP1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getP1</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setP1(int)">
<h3>setP1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setP1</span><wbr><span class="parameters">(int&nbsp;P1)</span></div>
</section>
</li>
<li>
<section class="detail" id="getP2()">
<h3>getP2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getP2</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setP2(int)">
<h3>setP2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setP2</span><wbr><span class="parameters">(int&nbsp;P2)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMode()">
<h3>getMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMode</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setMode(int)">
<h3>setMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize,
 int&nbsp;speckleRange,
 int&nbsp;mode)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.</dd>
<dd><code>speckleRange</code> - Maximum disparity variation within each connected component. If you do speckle
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.</dd>
<dd><code>mode</code> - Set it to StereoSGBM::MODE_HH to run the full-scale two-pass dynamic programming
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize,
 int&nbsp;speckleRange)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.</dd>
<dd><code>speckleRange</code> - Maximum disparity variation within each connected component. If you do speckle
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio,
 int&nbsp;speckleWindowSize)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap,
 int&nbsp;uniquenessRatio)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff,
 int&nbsp;preFilterCap)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2,
 int&nbsp;disp12MaxDiff)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1,
 int&nbsp;P2)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize,
 int&nbsp;P1)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities,
 int&nbsp;blockSize)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity,
 int&nbsp;numDisparities)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;minDisparity)</span></div>
<div class="block">Creates StereoSGBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates StereoSGBM object

     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
