<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>StereoBM (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.calib3d, class: StereoBM">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.calib3d</a></div>
<h1 title="Class StereoBM" class="title">Class StereoBM</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StereoMatcher.html" title="class in org.opencv.calib3d">org.opencv.calib3d.StereoMatcher</a>
<div class="inheritance">org.opencv.calib3d.StereoBM</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">StereoBM</span>
<span class="extends-implements">extends <a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></span></div>
<div class="block">Class for computing stereo correspondence using the block matching algorithm, introduced and
 contributed to OpenCV by K. Konolige.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PREFILTER_NORMALIZED_RESPONSE" class="member-name-link">PREFILTER_NORMALIZED_RESPONSE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PREFILTER_XSOBEL" class="member-name-link">PREFILTER_XSOBEL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.calib3d.StereoMatcher">Fields inherited from class&nbsp;org.opencv.calib3d.<a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="StereoMatcher.html#DISP_SCALE">DISP_SCALE</a>, <a href="StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoBM object

     disparity from 0 (default minimum disparity) to numDisparities.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int)" class="member-name-link">create</a><wbr>(int&nbsp;numDisparities)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoBM object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(int,int)" class="member-name-link">create</a><wbr>(int&nbsp;numDisparities,
 int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates StereoBM object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreFilterCap()" class="member-name-link">getPreFilterCap</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreFilterSize()" class="member-name-link">getPreFilterSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreFilterType()" class="member-name-link">getPreFilterType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getROI1()" class="member-name-link">getROI1</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getROI2()" class="member-name-link">getROI2</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSmallerBlockSize()" class="member-name-link">getSmallerBlockSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTextureThreshold()" class="member-name-link">getTextureThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUniquenessRatio()" class="member-name-link">getUniquenessRatio</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreFilterCap(int)" class="member-name-link">setPreFilterCap</a><wbr>(int&nbsp;preFilterCap)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreFilterSize(int)" class="member-name-link">setPreFilterSize</a><wbr>(int&nbsp;preFilterSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreFilterType(int)" class="member-name-link">setPreFilterType</a><wbr>(int&nbsp;preFilterType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setROI1(org.opencv.core.Rect)" class="member-name-link">setROI1</a><wbr>(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setROI2(org.opencv.core.Rect)" class="member-name-link">setROI2</a><wbr>(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSmallerBlockSize(int)" class="member-name-link">setSmallerBlockSize</a><wbr>(int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTextureThreshold(int)" class="member-name-link">setTextureThreshold</a><wbr>(int&nbsp;textureThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUniquenessRatio(int)" class="member-name-link">setUniquenessRatio</a><wbr>(int&nbsp;uniquenessRatio)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.calib3d.StereoMatcher">Methods inherited from class&nbsp;org.opencv.calib3d.<a href="StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="StereoMatcher.html#compute(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">compute</a>, <a href="StereoMatcher.html#getBlockSize()">getBlockSize</a>, <a href="StereoMatcher.html#getDisp12MaxDiff()">getDisp12MaxDiff</a>, <a href="StereoMatcher.html#getMinDisparity()">getMinDisparity</a>, <a href="StereoMatcher.html#getNumDisparities()">getNumDisparities</a>, <a href="StereoMatcher.html#getSpeckleRange()">getSpeckleRange</a>, <a href="StereoMatcher.html#getSpeckleWindowSize()">getSpeckleWindowSize</a>, <a href="StereoMatcher.html#setBlockSize(int)">setBlockSize</a>, <a href="StereoMatcher.html#setDisp12MaxDiff(int)">setDisp12MaxDiff</a>, <a href="StereoMatcher.html#setMinDisparity(int)">setMinDisparity</a>, <a href="StereoMatcher.html#setNumDisparities(int)">setNumDisparities</a>, <a href="StereoMatcher.html#setSpeckleRange(int)">setSpeckleRange</a>, <a href="StereoMatcher.html#setSpeckleWindowSize(int)">setSpeckleWindowSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PREFILTER_NORMALIZED_RESPONSE">
<h3>PREFILTER_NORMALIZED_RESPONSE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREFILTER_NORMALIZED_RESPONSE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoBM.PREFILTER_NORMALIZED_RESPONSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREFILTER_XSOBEL">
<h3>PREFILTER_XSOBEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREFILTER_XSOBEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.calib3d.StereoBM.PREFILTER_XSOBEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPreFilterType()">
<h3>getPreFilterType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPreFilterType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPreFilterType(int)">
<h3>setPreFilterType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreFilterType</span><wbr><span class="parameters">(int&nbsp;preFilterType)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPreFilterSize()">
<h3>getPreFilterSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPreFilterSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPreFilterSize(int)">
<h3>setPreFilterSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreFilterSize</span><wbr><span class="parameters">(int&nbsp;preFilterSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPreFilterCap()">
<h3>getPreFilterCap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPreFilterCap</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setPreFilterCap(int)">
<h3>setPreFilterCap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreFilterCap</span><wbr><span class="parameters">(int&nbsp;preFilterCap)</span></div>
</section>
</li>
<li>
<section class="detail" id="getTextureThreshold()">
<h3>getTextureThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getTextureThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setTextureThreshold(int)">
<h3>setTextureThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTextureThreshold</span><wbr><span class="parameters">(int&nbsp;textureThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="getUniquenessRatio()">
<h3>getUniquenessRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUniquenessRatio</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setUniquenessRatio(int)">
<h3>setUniquenessRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUniquenessRatio</span><wbr><span class="parameters">(int&nbsp;uniquenessRatio)</span></div>
</section>
</li>
<li>
<section class="detail" id="getSmallerBlockSize()">
<h3>getSmallerBlockSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getSmallerBlockSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setSmallerBlockSize(int)">
<h3>setSmallerBlockSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSmallerBlockSize</span><wbr><span class="parameters">(int&nbsp;blockSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="getROI1()">
<h3>getROI1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></span>&nbsp;<span class="element-name">getROI1</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setROI1(org.opencv.core.Rect)">
<h3>setROI1</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setROI1</span><wbr><span class="parameters">(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1)</span></div>
</section>
</li>
<li>
<section class="detail" id="getROI2()">
<h3>getROI2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Rect.html" title="class in org.opencv.core">Rect</a></span>&nbsp;<span class="element-name">getROI2</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setROI2(org.opencv.core.Rect)">
<h3>setROI2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setROI2</span><wbr><span class="parameters">(<a href="../core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2)</span></div>
</section>
</li>
<li>
<section class="detail" id="create(int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numDisparities,
 int&nbsp;blockSize)</span></div>
<div class="block">Creates StereoBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>numDisparities</code> - the disparity search range. For each pixel algorithm will find the best
     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.</dd>
<dd><code>blockSize</code> - the linear size of the blocks compared by the algorithm. The size should be odd
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(int&nbsp;numDisparities)</span></div>
<div class="block">Creates StereoBM object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>numDisparities</code> - the disparity search range. For each pixel algorithm will find the best
     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates StereoBM object

     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
