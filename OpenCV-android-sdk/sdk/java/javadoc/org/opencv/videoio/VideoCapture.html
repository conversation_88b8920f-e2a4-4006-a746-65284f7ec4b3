<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>VideoCapture (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.videoio, class: VideoCapture">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.videoio</a></div>
<h1 title="Class VideoCapture" class="title">Class VideoCapture</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.videoio.VideoCapture</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">VideoCapture</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Class for video capturing from video files, image sequences or cameras.

 The class provides C++ API for capturing video from cameras or for reading video files and image sequences.

 Here is how the class can be used:
 INCLUDE: samples/cpp/videocapture_basic.cpp

 <b>Note:</b> In REF: videoio_c "C API" the black-box structure <code>CvCapture</code> is used instead of %VideoCapture.
 <b>Note:</b>
 <ul>
   <li>
    (C++) A basic sample on using the %VideoCapture interface can be found at
     <code>OPENCV_SOURCE_CODE/samples/cpp/videocapture_starter.cpp</code>
   </li>
   <li>
    (Python) A basic sample on using the %VideoCapture interface can be found at
     <code>OPENCV_SOURCE_CODE/samples/python/video.py</code>
   </li>
   <li>
    (Python) A multi threaded video processing sample can be found at
     <code>OPENCV_SOURCE_CODE/samples/python/video_threaded.py</code>
   </li>
   <li>
    (Python) %VideoCapture sample showcasing some features of the Video4Linux2 backend
     <code>OPENCV_SOURCE_CODE/samples/python/video_v4l2.py</code>
   </li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">VideoCapture</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor
     <b>Note:</b> In REF: videoio_c "C API", when you finished working with video, release CvCapture structure with
     cvReleaseCapture(), or use Ptr&lt;CvCapture&gt; that calls cvReleaseCapture() automatically in the
     destructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int)" class="member-name-link">VideoCapture</a><wbr>(int&nbsp;index)</code></div>
<div class="col-last odd-row-color">
<div class="block">Opens a camera for video capturing</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int)" class="member-name-link">VideoCapture</a><wbr>(int&nbsp;index,
 int&nbsp;apiPreference)</code></div>
<div class="col-last even-row-color">
<div class="block">Opens a camera for video capturing</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int,org.opencv.core.MatOfInt)" class="member-name-link">VideoCapture</a><wbr>(int&nbsp;index,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color">
<div class="block">Opens a camera for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">VideoCapture</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int)" class="member-name-link">VideoCapture</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference)</code></div>
<div class="col-last odd-row-color">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,org.opencv.core.MatOfInt)" class="member-name-link">VideoCapture</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.videoio.IStreamReader,int,org.opencv.core.MatOfInt)" class="member-name-link">VideoCapture</a><wbr>(<a href="IStreamReader.html" title="class in org.opencv.videoio">IStreamReader</a>&nbsp;source,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color">
<div class="block">Opens a video using data stream.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="VideoCapture.html" title="class in org.opencv.videoio">VideoCapture</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int)" class="member-name-link">get</a><wbr>(int&nbsp;propId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the specified VideoCapture property</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBackendName()" class="member-name-link">getBackendName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExceptionMode()" class="member-name-link">getExceptionMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#grab()" class="member-name-link">grab</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Grabs the next frame from video file or capturing device.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isOpened()" class="member-name-link">isOpened</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if video capturing has been initialized already.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(int)" class="member-name-link">open</a><wbr>(int&nbsp;index)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(int,int)" class="member-name-link">open</a><wbr>(int&nbsp;index,
 int&nbsp;apiPreference)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(int,int,org.opencv.core.MatOfInt)" class="member-name-link">open</a><wbr>(int&nbsp;index,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a camera for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,org.opencv.core.MatOfInt)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(org.opencv.videoio.IStreamReader,int,org.opencv.core.MatOfInt)" class="member-name-link">open</a><wbr>(<a href="IStreamReader.html" title="class in org.opencv.videoio">IStreamReader</a>&nbsp;source,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Opens a video using data stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read(org.opencv.core.Mat)" class="member-name-link">read</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Grabs, decodes and returns the next video frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes video file or capturing device.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#retrieve(org.opencv.core.Mat)" class="member-name-link">retrieve</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes and returns the grabbed video frame.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#retrieve(org.opencv.core.Mat,int)" class="member-name-link">retrieve</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 int&nbsp;flag)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes and returns the grabbed video frame.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set(int,double)" class="member-name-link">set</a><wbr>(int&nbsp;propId,
 double&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a property in the VideoCapture.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExceptionMode(boolean)" class="member-name-link">setExceptionMode</a><wbr>(boolean&nbsp;enable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Switches exceptions mode

 methods raise exceptions if not successful instead of returning an error code</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span>()</div>
<div class="block">Default constructor
     <b>Note:</b> In REF: videoio_c "C API", when you finished working with video, release CvCapture structure with
     cvReleaseCapture(), or use Ptr&lt;CvCapture&gt; that calls cvReleaseCapture() automatically in the
     destructor.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - it can be:
 <ul>
   <li>
      name of video file (eg. <code>video.avi</code>)
   </li>
   <li>
      or image sequence (eg. <code>img_%02d.jpg</code>, which will read samples like <code>img_00.jpg, img_01.jpg, img_02.jpg, ...</code>)
   </li>
   <li>
      or URL of video stream (eg. <code>protocol://host:port/script_name?script_params|auth</code>)
   </li>
   <li>
      or GStreamer pipeline string in gst-launch tool format in case if GStreamer is used as backend
       Note that each video stream or IP camera feed has its own URL scheme. Please refer to the
       documentation of source stream to know the right URL.
   </li>
 </ul></dd>
<dd><code>apiPreference</code> - preferred Capture API backends to use. Can be used to enforce a specific reader
     implementation if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_IMAGES or cv::CAP_DSHOW.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - it can be:
 <ul>
   <li>
      name of video file (eg. <code>video.avi</code>)
   </li>
   <li>
      or image sequence (eg. <code>img_%02d.jpg</code>, which will read samples like <code>img_00.jpg, img_01.jpg, img_02.jpg, ...</code>)
   </li>
   <li>
      or URL of video stream (eg. <code>protocol://host:port/script_name?script_params|auth</code>)
   </li>
   <li>
      or GStreamer pipeline string in gst-launch tool format in case if GStreamer is used as backend
       Note that each video stream or IP camera feed has its own URL scheme. Please refer to the
       documentation of source stream to know the right URL.
   </li>
 </ul>
     implementation if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_IMAGES or cv::CAP_DSHOW.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,org.opencv.core.MatOfInt)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(int&nbsp;index,
 int&nbsp;apiPreference)</span></div>
<div class="block">Opens a camera for video capturing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - id of the video capturing device to open. To open default camera using default backend just pass 0.
     (to backward compatibility usage of camera_id + domain_offset (CAP_*) is valid when apiPreference is CAP_ANY)</dd>
<dd><code>apiPreference</code> - preferred Capture API backends to use. Can be used to enforce a specific reader
     implementation if multiple are available: e.g. cv::CAP_DSHOW or cv::CAP_MSMF or cv::CAP_V4L.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(int&nbsp;index)</span></div>
<div class="block">Opens a camera for video capturing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - id of the video capturing device to open. To open default camera using default backend just pass 0.
     (to backward compatibility usage of camera_id + domain_offset (CAP_*) is valid when apiPreference is CAP_ANY)
     implementation if multiple are available: e.g. cv::CAP_DSHOW or cv::CAP_MSMF or cv::CAP_V4L.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,org.opencv.core.MatOfInt)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(int&nbsp;index,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.videoio.IStreamReader,int,org.opencv.core.MatOfInt)">
<h3>VideoCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoCapture</span><wbr><span class="parameters">(<a href="IStreamReader.html" title="class in org.opencv.videoio">IStreamReader</a>&nbsp;source,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a video using data stream.

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="VideoCapture.html" title="class in org.opencv.videoio">VideoCapture</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.

     

     Parameters are same as the constructor VideoCapture(const String&amp; filename, int apiPreference = CAP_ANY)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.

     

     Parameters are same as the constructor VideoCapture(const String&amp; filename, int apiPreference = CAP_ANY)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,org.opencv.core.MatOfInt)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(int,int)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(int&nbsp;index,
 int&nbsp;apiPreference)</span></div>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(int)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(int&nbsp;index)</span></div>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(int,int,org.opencv.core.MatOfInt)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(int&nbsp;index,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(org.opencv.videoio.IStreamReader,int,org.opencv.core.MatOfInt)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="IStreamReader.html" title="class in org.opencv.videoio">IStreamReader</a>&nbsp;source,
 int&nbsp;apiPreference,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">Opens a video using data stream.

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isOpened()">
<h3>isOpened</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOpened</span>()</div>
<div class="block">Returns true if video capturing has been initialized already.

     If the previous call to VideoCapture constructor or VideoCapture::open() succeeded, the method returns
     true.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">Closes video file or capturing device.

     The method is automatically called by subsequent VideoCapture::open and by VideoCapture
     destructor.

     The C function also deallocates memory and clears \*capture pointer.</div>
</section>
</li>
<li>
<section class="detail" id="grab()">
<h3>grab</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">grab</span>()</div>
<div class="block">Grabs the next frame from video file or capturing device.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> (non-zero) in the case of success.

     The method/function grabs the next frame from video file or camera and returns true (non-zero) in
     the case of success.

     The primary use of the function is in multi-camera environments, especially when the cameras do not
     have hardware synchronization. That is, you call VideoCapture::grab() for each camera and after that
     call the slower method VideoCapture::retrieve() to decode and get frame from each camera. This way
     the overhead on demosaicing or motion jpeg decompression etc. is eliminated and the retrieved frames
     from different cameras will be closer in time.

     Also, when a connected camera is multi-head (for example, a stereo camera or a Kinect device), the
     correct way of retrieving data from it is to call VideoCapture::grab() first and then call
     VideoCapture::retrieve() one or more times with different values of the channel parameter.

     REF: tutorial_kinect_openni</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="retrieve(org.opencv.core.Mat,int)">
<h3>retrieve</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">retrieve</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 int&nbsp;flag)</span></div>
<div class="block">Decodes and returns the grabbed video frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - it could be a frame index or a driver specific flag</dd>
<dd><code>image</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>false</code> if no frames has been grabbed

     The method decodes and returns the just grabbed frame. If no frames has been grabbed
     (camera has been disconnected, or there are no more frames in video file), the method returns false
     and the function returns an empty image (with %cv::Mat, test it with Mat::empty()).

     SEE: read()

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="retrieve(org.opencv.core.Mat)">
<h3>retrieve</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">retrieve</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Decodes and returns the grabbed video frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>false</code> if no frames has been grabbed

     The method decodes and returns the just grabbed frame. If no frames has been grabbed
     (camera has been disconnected, or there are no more frames in video file), the method returns false
     and the function returns an empty image (with %cv::Mat, test it with Mat::empty()).

     SEE: read()

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="read(org.opencv.core.Mat)">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Grabs, decodes and returns the next video frame.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>false</code> if no frames has been grabbed

     The method/function combines VideoCapture::grab() and VideoCapture::retrieve() in one call. This is the
     most convenient method for reading video files or capturing data from decode and returns the just
     grabbed frame. If no frames has been grabbed (camera has been disconnected, or there are no more
     frames in video file), the method returns false and the function returns empty image (with %cv::Mat, test it with Mat::empty()).

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="set(int,double)">
<h3>set</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">set</span><wbr><span class="parameters">(int&nbsp;propId,
 double&nbsp;value)</span></div>
<div class="block">Sets a property in the VideoCapture.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propId</code> - Property identifier from cv::VideoCaptureProperties (eg. cv::CAP_PROP_POS_MSEC, cv::CAP_PROP_POS_FRAMES, ...)
     or one from REF: videoio_flags_others</dd>
<dd><code>value</code> - Value of the property.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the property is supported by backend used by the VideoCapture instance.
     <b>Note:</b> Even if it returns <code>true</code> this doesn't ensure that the property
     value has been accepted by the capture device. See note in VideoCapture::get()</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="get(int)">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;propId)</span></div>
<div class="block">Returns the specified VideoCapture property</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propId</code> - Property identifier from cv::VideoCaptureProperties (eg. cv::CAP_PROP_POS_MSEC, cv::CAP_PROP_POS_FRAMES, ...)
     or one from REF: videoio_flags_others</dd>
<dt>Returns:</dt>
<dd>Value for the specified property. Value 0 is returned when querying a property that is
     not supported by the backend used by the VideoCapture instance.

     <b>Note:</b> Reading / writing properties involves many layers. Some unexpected result might happens
     along this chain.
     <code>
     VideoCapture -&gt; API Backend -&gt; Operating System -&gt; Device Driver -&gt; Device Hardware
     </code>
     The returned value might be different from what really used by the device or it could be encoded
     using device dependent rules (eg. steps or percentage). Effective behaviour depends from device
     driver and API Backend</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBackendName()">
<h3>getBackendName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBackendName</span>()</div>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExceptionMode(boolean)">
<h3>setExceptionMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExceptionMode</span><wbr><span class="parameters">(boolean&nbsp;enable)</span></div>
<div class="block">Switches exceptions mode

 methods raise exceptions if not successful instead of returning an error code</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enable</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExceptionMode()">
<h3>getExceptionMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getExceptionMode</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
