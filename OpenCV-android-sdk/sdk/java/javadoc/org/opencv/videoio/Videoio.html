<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Videoio (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.videoio, class: Videoio">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.videoio</a></div>
<h1 title="Class Videoio" class="title">Class Videoio</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.videoio.Videoio</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Videoio</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_ANDROID" class="member-name-link">CAP_ANDROID</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_ANY" class="member-name-link">CAP_ANY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_ARAVIS" class="member-name-link">CAP_ARAVIS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_AVFOUNDATION" class="member-name-link">CAP_AVFOUNDATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_CMU1394" class="member-name-link">CAP_CMU1394</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_DC1394" class="member-name-link">CAP_DC1394</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_DSHOW" class="member-name-link">CAP_DSHOW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_FFMPEG" class="member-name-link">CAP_FFMPEG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_FIREWARE" class="member-name-link">CAP_FIREWARE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_FIREWIRE" class="member-name-link">CAP_FIREWIRE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_GIGANETIX" class="member-name-link">CAP_GIGANETIX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_GPHOTO2" class="member-name-link">CAP_GPHOTO2</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_GSTREAMER" class="member-name-link">CAP_GSTREAMER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_IEEE1394" class="member-name-link">CAP_IEEE1394</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_IMAGES" class="member-name-link">CAP_IMAGES</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_INTEL_MFX" class="member-name-link">CAP_INTEL_MFX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_INTELPERC" class="member-name-link">CAP_INTELPERC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_INTELPERC_DEPTH_GENERATOR" class="member-name-link">CAP_INTELPERC_DEPTH_GENERATOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_INTELPERC_DEPTH_MAP" class="member-name-link">CAP_INTELPERC_DEPTH_MAP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_INTELPERC_GENERATORS_MASK" class="member-name-link">CAP_INTELPERC_GENERATORS_MASK</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_INTELPERC_IMAGE" class="member-name-link">CAP_INTELPERC_IMAGE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_INTELPERC_IMAGE_GENERATOR" class="member-name-link">CAP_INTELPERC_IMAGE_GENERATOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_INTELPERC_IR_GENERATOR" class="member-name-link">CAP_INTELPERC_IR_GENERATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_INTELPERC_IR_MAP" class="member-name-link">CAP_INTELPERC_IR_MAP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_INTELPERC_UVDEPTH_MAP" class="member-name-link">CAP_INTELPERC_UVDEPTH_MAP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_MSMF" class="member-name-link">CAP_MSMF</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OBSENSOR" class="member-name-link">CAP_OBSENSOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OBSENSOR_BGR_IMAGE" class="member-name-link">CAP_OBSENSOR_BGR_IMAGE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OBSENSOR_DEPTH_GENERATOR" class="member-name-link">CAP_OBSENSOR_DEPTH_GENERATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OBSENSOR_DEPTH_MAP" class="member-name-link">CAP_OBSENSOR_DEPTH_MAP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OBSENSOR_GENERATORS_MASK" class="member-name-link">CAP_OBSENSOR_GENERATORS_MASK</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OBSENSOR_IMAGE_GENERATOR" class="member-name-link">CAP_OBSENSOR_IMAGE_GENERATOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OBSENSOR_IR_GENERATOR" class="member-name-link">CAP_OBSENSOR_IR_GENERATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OBSENSOR_IR_IMAGE" class="member-name-link">CAP_OBSENSOR_IR_IMAGE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENCV_MJPEG" class="member-name-link">CAP_OPENCV_MJPEG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI" class="member-name-link">CAP_OPENNI</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_ASUS" class="member-name-link">CAP_OPENNI_ASUS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_BGR_IMAGE" class="member-name-link">CAP_OPENNI_BGR_IMAGE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR_BASELINE" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR_BASELINE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR_PRESENT" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR_PRESENT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON" class="member-name-link">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_DEPTH_MAP" class="member-name-link">CAP_OPENNI_DEPTH_MAP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_DISPARITY_MAP" class="member-name-link">CAP_OPENNI_DISPARITY_MAP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_DISPARITY_MAP_32F" class="member-name-link">CAP_OPENNI_DISPARITY_MAP_32F</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_GENERATORS_MASK" class="member-name-link">CAP_OPENNI_GENERATORS_MASK</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_GRAY_IMAGE" class="member-name-link">CAP_OPENNI_GRAY_IMAGE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_IMAGE_GENERATOR" class="member-name-link">CAP_OPENNI_IMAGE_GENERATOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE" class="member-name-link">CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_IMAGE_GENERATOR_PRESENT" class="member-name-link">CAP_OPENNI_IMAGE_GENERATOR_PRESENT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_IR_GENERATOR" class="member-name-link">CAP_OPENNI_IR_GENERATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_IR_GENERATOR_PRESENT" class="member-name-link">CAP_OPENNI_IR_GENERATOR_PRESENT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_IR_IMAGE" class="member-name-link">CAP_OPENNI_IR_IMAGE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_POINT_CLOUD_MAP" class="member-name-link">CAP_OPENNI_POINT_CLOUD_MAP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_QVGA_30HZ" class="member-name-link">CAP_OPENNI_QVGA_30HZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_QVGA_60HZ" class="member-name-link">CAP_OPENNI_QVGA_60HZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_SXGA_15HZ" class="member-name-link">CAP_OPENNI_SXGA_15HZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_SXGA_30HZ" class="member-name-link">CAP_OPENNI_SXGA_30HZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI_VALID_DEPTH_MASK" class="member-name-link">CAP_OPENNI_VALID_DEPTH_MASK</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI_VGA_30HZ" class="member-name-link">CAP_OPENNI_VGA_30HZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI2" class="member-name-link">CAP_OPENNI2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_OPENNI2_ASTRA" class="member-name-link">CAP_OPENNI2_ASTRA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_OPENNI2_ASUS" class="member-name-link">CAP_OPENNI2_ASUS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_ANDROID_DEVICE_TORCH" class="member-name-link">CAP_PROP_ANDROID_DEVICE_TORCH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_APERTURE" class="member-name-link">CAP_PROP_APERTURE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_ARAVIS_AUTOTRIGGER" class="member-name-link">CAP_PROP_ARAVIS_AUTOTRIGGER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUDIO_BASE_INDEX" class="member-name-link">CAP_PROP_AUDIO_BASE_INDEX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUDIO_DATA_DEPTH" class="member-name-link">CAP_PROP_AUDIO_DATA_DEPTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUDIO_POS" class="member-name-link">CAP_PROP_AUDIO_POS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUDIO_SAMPLES_PER_SECOND" class="member-name-link">CAP_PROP_AUDIO_SAMPLES_PER_SECOND</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUDIO_SHIFT_NSEC" class="member-name-link">CAP_PROP_AUDIO_SHIFT_NSEC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUDIO_STREAM" class="member-name-link">CAP_PROP_AUDIO_STREAM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUDIO_SYNCHRONIZE" class="member-name-link">CAP_PROP_AUDIO_SYNCHRONIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUDIO_TOTAL_CHANNELS" class="member-name-link">CAP_PROP_AUDIO_TOTAL_CHANNELS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUDIO_TOTAL_STREAMS" class="member-name-link">CAP_PROP_AUDIO_TOTAL_STREAMS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUTO_EXPOSURE" class="member-name-link">CAP_PROP_AUTO_EXPOSURE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_AUTO_WB" class="member-name-link">CAP_PROP_AUTO_WB</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_AUTOFOCUS" class="member-name-link">CAP_PROP_AUTOFOCUS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_BACKEND" class="member-name-link">CAP_PROP_BACKEND</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_BACKLIGHT" class="member-name-link">CAP_PROP_BACKLIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_BITRATE" class="member-name-link">CAP_PROP_BITRATE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_BRIGHTNESS" class="member-name-link">CAP_PROP_BRIGHTNESS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_BUFFERSIZE" class="member-name-link">CAP_PROP_BUFFERSIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_CHANNEL" class="member-name-link">CAP_PROP_CHANNEL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_CODEC_EXTRADATA_INDEX" class="member-name-link">CAP_PROP_CODEC_EXTRADATA_INDEX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_CODEC_PIXEL_FORMAT" class="member-name-link">CAP_PROP_CODEC_PIXEL_FORMAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_CONTRAST" class="member-name-link">CAP_PROP_CONTRAST</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_CONVERT_RGB" class="member-name-link">CAP_PROP_CONVERT_RGB</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_DC1394_MAX" class="member-name-link">CAP_PROP_DC1394_MAX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_DC1394_MODE_AUTO" class="member-name-link">CAP_PROP_DC1394_MODE_AUTO</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_DC1394_MODE_MANUAL" class="member-name-link">CAP_PROP_DC1394_MODE_MANUAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO" class="member-name-link">CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_DC1394_OFF" class="member-name-link">CAP_PROP_DC1394_OFF</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_DTS_DELAY" class="member-name-link">CAP_PROP_DTS_DELAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_EXPOSURE" class="member-name-link">CAP_PROP_EXPOSURE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_EXPOSUREPROGRAM" class="member-name-link">CAP_PROP_EXPOSUREPROGRAM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_FOCUS" class="member-name-link">CAP_PROP_FOCUS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_FORMAT" class="member-name-link">CAP_PROP_FORMAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_FOURCC" class="member-name-link">CAP_PROP_FOURCC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_FPS" class="member-name-link">CAP_PROP_FPS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_FRAME_COUNT" class="member-name-link">CAP_PROP_FRAME_COUNT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_FRAME_HEIGHT" class="member-name-link">CAP_PROP_FRAME_HEIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_FRAME_TYPE" class="member-name-link">CAP_PROP_FRAME_TYPE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_FRAME_WIDTH" class="member-name-link">CAP_PROP_FRAME_WIDTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GAIN" class="member-name-link">CAP_PROP_GAIN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GAMMA" class="member-name-link">CAP_PROP_GAMMA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_HEIGH_MAX" class="member-name-link">CAP_PROP_GIGA_FRAME_HEIGH_MAX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_OFFSET_X" class="member-name-link">CAP_PROP_GIGA_FRAME_OFFSET_X</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_OFFSET_Y" class="member-name-link">CAP_PROP_GIGA_FRAME_OFFSET_Y</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_SENS_HEIGH" class="member-name-link">CAP_PROP_GIGA_FRAME_SENS_HEIGH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_SENS_WIDTH" class="member-name-link">CAP_PROP_GIGA_FRAME_SENS_WIDTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GIGA_FRAME_WIDTH_MAX" class="member-name-link">CAP_PROP_GIGA_FRAME_WIDTH_MAX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GPHOTO2_COLLECT_MSGS" class="member-name-link">CAP_PROP_GPHOTO2_COLLECT_MSGS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GPHOTO2_FLUSH_MSGS" class="member-name-link">CAP_PROP_GPHOTO2_FLUSH_MSGS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GPHOTO2_PREVIEW" class="member-name-link">CAP_PROP_GPHOTO2_PREVIEW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GPHOTO2_RELOAD_CONFIG" class="member-name-link">CAP_PROP_GPHOTO2_RELOAD_CONFIG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE" class="member-name-link">CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GPHOTO2_WIDGET_ENUMERATE" class="member-name-link">CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_GSTREAMER_QUEUE_LENGTH" class="member-name-link">CAP_PROP_GSTREAMER_QUEUE_LENGTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_GUID" class="member-name-link">CAP_PROP_GUID</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_HUE" class="member-name-link">CAP_PROP_HUE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_HW_ACCELERATION" class="member-name-link">CAP_PROP_HW_ACCELERATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_HW_ACCELERATION_USE_OPENCL" class="member-name-link">CAP_PROP_HW_ACCELERATION_USE_OPENCL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_HW_DEVICE" class="member-name-link">CAP_PROP_HW_DEVICE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_IMAGES_BASE" class="member-name-link">CAP_PROP_IMAGES_BASE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_IMAGES_LAST" class="member-name-link">CAP_PROP_IMAGES_LAST</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD" class="member-name-link">CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ" class="member-name-link">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT" class="member-name-link">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE" class="member-name-link">CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE" class="member-name-link">CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_INTELPERC_PROFILE_COUNT" class="member-name-link">CAP_PROP_INTELPERC_PROFILE_COUNT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_INTELPERC_PROFILE_IDX" class="member-name-link">CAP_PROP_INTELPERC_PROFILE_IDX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_IOS_DEVICE_EXPOSURE" class="member-name-link">CAP_PROP_IOS_DEVICE_EXPOSURE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_IOS_DEVICE_FLASH" class="member-name-link">CAP_PROP_IOS_DEVICE_FLASH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_IOS_DEVICE_FOCUS" class="member-name-link">CAP_PROP_IOS_DEVICE_FOCUS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_IOS_DEVICE_TORCH" class="member-name-link">CAP_PROP_IOS_DEVICE_TORCH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_IOS_DEVICE_WHITEBALANCE" class="member-name-link">CAP_PROP_IOS_DEVICE_WHITEBALANCE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_IRIS" class="member-name-link">CAP_PROP_IRIS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_ISO_SPEED" class="member-name-link">CAP_PROP_ISO_SPEED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_LRF_HAS_KEY_FRAME" class="member-name-link">CAP_PROP_LRF_HAS_KEY_FRAME</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_MODE" class="member-name-link">CAP_PROP_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_MONOCHROME" class="member-name-link">CAP_PROP_MONOCHROME</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_N_THREADS" class="member-name-link">CAP_PROP_N_THREADS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OBSENSOR_INTRINSIC_CX" class="member-name-link">CAP_PROP_OBSENSOR_INTRINSIC_CX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OBSENSOR_INTRINSIC_CY" class="member-name-link">CAP_PROP_OBSENSOR_INTRINSIC_CY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OBSENSOR_INTRINSIC_FX" class="member-name-link">CAP_PROP_OBSENSOR_INTRINSIC_FX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OBSENSOR_INTRINSIC_FY" class="member-name-link">CAP_PROP_OBSENSOR_INTRINSIC_FY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPEN_TIMEOUT_MSEC" class="member-name-link">CAP_PROP_OPEN_TIMEOUT_MSEC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_APPROX_FRAME_SYNC" class="member-name-link">CAP_PROP_OPENNI_APPROX_FRAME_SYNC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI_BASELINE" class="member-name-link">CAP_PROP_OPENNI_BASELINE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_CIRCLE_BUFFER" class="member-name-link">CAP_PROP_OPENNI_CIRCLE_BUFFER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI_FOCAL_LENGTH" class="member-name-link">CAP_PROP_OPENNI_FOCAL_LENGTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_FRAME_MAX_DEPTH" class="member-name-link">CAP_PROP_OPENNI_FRAME_MAX_DEPTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI_GENERATOR_PRESENT" class="member-name-link">CAP_PROP_OPENNI_GENERATOR_PRESENT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_MAX_BUFFER_SIZE" class="member-name-link">CAP_PROP_OPENNI_MAX_BUFFER_SIZE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI_MAX_TIME_DURATION" class="member-name-link">CAP_PROP_OPENNI_MAX_TIME_DURATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_OUTPUT_MODE" class="member-name-link">CAP_PROP_OPENNI_OUTPUT_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI_REGISTRATION" class="member-name-link">CAP_PROP_OPENNI_REGISTRATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI_REGISTRATION_ON" class="member-name-link">CAP_PROP_OPENNI_REGISTRATION_ON</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_OPENNI2_MIRROR" class="member-name-link">CAP_PROP_OPENNI2_MIRROR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_OPENNI2_SYNC" class="member-name-link">CAP_PROP_OPENNI2_SYNC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_ORIENTATION_AUTO" class="member-name-link">CAP_PROP_ORIENTATION_AUTO</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_ORIENTATION_META" class="member-name-link">CAP_PROP_ORIENTATION_META</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_PAN" class="member-name-link">CAP_PROP_PAN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_POS_AVI_RATIO" class="member-name-link">CAP_PROP_POS_AVI_RATIO</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_POS_FRAMES" class="member-name-link">CAP_PROP_POS_FRAMES</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_POS_MSEC" class="member-name-link">CAP_PROP_POS_MSEC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_PTS" class="member-name-link">CAP_PROP_PTS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_PVAPI_BINNINGX" class="member-name-link">CAP_PROP_PVAPI_BINNINGX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_PVAPI_BINNINGY" class="member-name-link">CAP_PROP_PVAPI_BINNINGY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_PVAPI_DECIMATIONHORIZONTAL" class="member-name-link">CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_PVAPI_DECIMATIONVERTICAL" class="member-name-link">CAP_PROP_PVAPI_DECIMATIONVERTICAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE" class="member-name-link">CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_PVAPI_MULTICASTIP" class="member-name-link">CAP_PROP_PVAPI_MULTICASTIP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_PVAPI_PIXELFORMAT" class="member-name-link">CAP_PROP_PVAPI_PIXELFORMAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_READ_TIMEOUT_MSEC" class="member-name-link">CAP_PROP_READ_TIMEOUT_MSEC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_RECTIFICATION" class="member-name-link">CAP_PROP_RECTIFICATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_ROLL" class="member-name-link">CAP_PROP_ROLL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_SAR_DEN" class="member-name-link">CAP_PROP_SAR_DEN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_SAR_NUM" class="member-name-link">CAP_PROP_SAR_NUM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_SATURATION" class="member-name-link">CAP_PROP_SATURATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_SETTINGS" class="member-name-link">CAP_PROP_SETTINGS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_SHARPNESS" class="member-name-link">CAP_PROP_SHARPNESS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_SPEED" class="member-name-link">CAP_PROP_SPEED</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_STREAM_OPEN_TIME_USEC" class="member-name-link">CAP_PROP_STREAM_OPEN_TIME_USEC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_TEMPERATURE" class="member-name-link">CAP_PROP_TEMPERATURE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_TILT" class="member-name-link">CAP_PROP_TILT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_TRIGGER" class="member-name-link">CAP_PROP_TRIGGER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_TRIGGER_DELAY" class="member-name-link">CAP_PROP_TRIGGER_DELAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_VIDEO_STREAM" class="member-name-link">CAP_PROP_VIDEO_STREAM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_VIDEO_TOTAL_CHANNELS" class="member-name-link">CAP_PROP_VIDEO_TOTAL_CHANNELS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_VIEWFINDER" class="member-name-link">CAP_PROP_VIEWFINDER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_WB_TEMPERATURE" class="member-name-link">CAP_PROP_WB_TEMPERATURE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_WHITE_BALANCE_BLUE_U" class="member-name-link">CAP_PROP_WHITE_BALANCE_BLUE_U</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_WHITE_BALANCE_RED_V" class="member-name-link">CAP_PROP_WHITE_BALANCE_RED_V</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_ACQ_BUFFER_SIZE" class="member-name-link">CAP_PROP_XI_ACQ_BUFFER_SIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT" class="member-name-link">CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_ACQ_FRAME_BURST_COUNT" class="member-name-link">CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_ACQ_TIMING_MODE" class="member-name-link">CAP_PROP_XI_ACQ_TIMING_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT" class="member-name-link">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE" class="member-name-link">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_AE_MAX_LIMIT" class="member-name-link">CAP_PROP_XI_AE_MAX_LIMIT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AEAG" class="member-name-link">CAP_PROP_XI_AEAG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_AEAG_LEVEL" class="member-name-link">CAP_PROP_XI_AEAG_LEVEL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AEAG_ROI_HEIGHT" class="member-name-link">CAP_PROP_XI_AEAG_ROI_HEIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_AEAG_ROI_OFFSET_X" class="member-name-link">CAP_PROP_XI_AEAG_ROI_OFFSET_X</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AEAG_ROI_OFFSET_Y" class="member-name-link">CAP_PROP_XI_AEAG_ROI_OFFSET_Y</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_AEAG_ROI_WIDTH" class="member-name-link">CAP_PROP_XI_AEAG_ROI_WIDTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AG_MAX_LIMIT" class="member-name-link">CAP_PROP_XI_AG_MAX_LIMIT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_APPLY_CMS" class="member-name-link">CAP_PROP_XI_APPLY_CMS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION" class="member-name-link">CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_AUTO_WB" class="member-name-link">CAP_PROP_XI_AUTO_WB</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_AVAILABLE_BANDWIDTH" class="member-name-link">CAP_PROP_XI_AVAILABLE_BANDWIDTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_BINNING_HORIZONTAL" class="member-name-link">CAP_PROP_XI_BINNING_HORIZONTAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_BINNING_PATTERN" class="member-name-link">CAP_PROP_XI_BINNING_PATTERN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_BINNING_SELECTOR" class="member-name-link">CAP_PROP_XI_BINNING_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_BINNING_VERTICAL" class="member-name-link">CAP_PROP_XI_BINNING_VERTICAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_BPC" class="member-name-link">CAP_PROP_XI_BPC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_BUFFER_POLICY" class="member-name-link">CAP_PROP_XI_BUFFER_POLICY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_BUFFERS_QUEUE_SIZE" class="member-name-link">CAP_PROP_XI_BUFFERS_QUEUE_SIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_00" class="member-name-link">CAP_PROP_XI_CC_MATRIX_00</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_01" class="member-name-link">CAP_PROP_XI_CC_MATRIX_01</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_02" class="member-name-link">CAP_PROP_XI_CC_MATRIX_02</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_03" class="member-name-link">CAP_PROP_XI_CC_MATRIX_03</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_10" class="member-name-link">CAP_PROP_XI_CC_MATRIX_10</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_11" class="member-name-link">CAP_PROP_XI_CC_MATRIX_11</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_12" class="member-name-link">CAP_PROP_XI_CC_MATRIX_12</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_13" class="member-name-link">CAP_PROP_XI_CC_MATRIX_13</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_20" class="member-name-link">CAP_PROP_XI_CC_MATRIX_20</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_21" class="member-name-link">CAP_PROP_XI_CC_MATRIX_21</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_22" class="member-name-link">CAP_PROP_XI_CC_MATRIX_22</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_23" class="member-name-link">CAP_PROP_XI_CC_MATRIX_23</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_30" class="member-name-link">CAP_PROP_XI_CC_MATRIX_30</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_31" class="member-name-link">CAP_PROP_XI_CC_MATRIX_31</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_32" class="member-name-link">CAP_PROP_XI_CC_MATRIX_32</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CC_MATRIX_33" class="member-name-link">CAP_PROP_XI_CC_MATRIX_33</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_CHIP_TEMP" class="member-name-link">CAP_PROP_XI_CHIP_TEMP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_CMS" class="member-name-link">CAP_PROP_XI_CMS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_COLOR_FILTER_ARRAY" class="member-name-link">CAP_PROP_XI_COLOR_FILTER_ARRAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_COLUMN_FPN_CORRECTION" class="member-name-link">CAP_PROP_XI_COLUMN_FPN_CORRECTION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_COOLING" class="member-name-link">CAP_PROP_XI_COOLING</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_COUNTER_SELECTOR" class="member-name-link">CAP_PROP_XI_COUNTER_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_COUNTER_VALUE" class="member-name-link">CAP_PROP_XI_COUNTER_VALUE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DATA_FORMAT" class="member-name-link">CAP_PROP_XI_DATA_FORMAT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DEBOUNCE_EN" class="member-name-link">CAP_PROP_XI_DEBOUNCE_EN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DEBOUNCE_POL" class="member-name-link">CAP_PROP_XI_DEBOUNCE_POL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DEBOUNCE_T0" class="member-name-link">CAP_PROP_XI_DEBOUNCE_T0</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DEBOUNCE_T1" class="member-name-link">CAP_PROP_XI_DEBOUNCE_T1</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DEBUG_LEVEL" class="member-name-link">CAP_PROP_XI_DEBUG_LEVEL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DECIMATION_HORIZONTAL" class="member-name-link">CAP_PROP_XI_DECIMATION_HORIZONTAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DECIMATION_PATTERN" class="member-name-link">CAP_PROP_XI_DECIMATION_PATTERN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DECIMATION_SELECTOR" class="member-name-link">CAP_PROP_XI_DECIMATION_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DECIMATION_VERTICAL" class="member-name-link">CAP_PROP_XI_DECIMATION_VERTICAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DEFAULT_CC_MATRIX" class="member-name-link">CAP_PROP_XI_DEFAULT_CC_MATRIX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DEVICE_MODEL_ID" class="member-name-link">CAP_PROP_XI_DEVICE_MODEL_ID</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DEVICE_RESET" class="member-name-link">CAP_PROP_XI_DEVICE_RESET</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DEVICE_SN" class="member-name-link">CAP_PROP_XI_DEVICE_SN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_DOWNSAMPLING" class="member-name-link">CAP_PROP_XI_DOWNSAMPLING</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_DOWNSAMPLING_TYPE" class="member-name-link">CAP_PROP_XI_DOWNSAMPLING_TYPE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_EXP_PRIORITY" class="member-name-link">CAP_PROP_XI_EXP_PRIORITY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_EXPOSURE" class="member-name-link">CAP_PROP_XI_EXPOSURE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_EXPOSURE_BURST_COUNT" class="member-name-link">CAP_PROP_XI_EXPOSURE_BURST_COUNT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_FFS_ACCESS_KEY" class="member-name-link">CAP_PROP_XI_FFS_ACCESS_KEY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_FFS_FILE_ID" class="member-name-link">CAP_PROP_XI_FFS_FILE_ID</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_FFS_FILE_SIZE" class="member-name-link">CAP_PROP_XI_FFS_FILE_SIZE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_FRAMERATE" class="member-name-link">CAP_PROP_XI_FRAMERATE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_FREE_FFS_SIZE" class="member-name-link">CAP_PROP_XI_FREE_FFS_SIZE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_GAIN" class="member-name-link">CAP_PROP_XI_GAIN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_GAIN_SELECTOR" class="member-name-link">CAP_PROP_XI_GAIN_SELECTOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_GAMMAC" class="member-name-link">CAP_PROP_XI_GAMMAC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_GAMMAY" class="member-name-link">CAP_PROP_XI_GAMMAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_GPI_LEVEL" class="member-name-link">CAP_PROP_XI_GPI_LEVEL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_GPI_MODE" class="member-name-link">CAP_PROP_XI_GPI_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_GPI_SELECTOR" class="member-name-link">CAP_PROP_XI_GPI_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_GPO_MODE" class="member-name-link">CAP_PROP_XI_GPO_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_GPO_SELECTOR" class="member-name-link">CAP_PROP_XI_GPO_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_HDR" class="member-name-link">CAP_PROP_XI_HDR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_HDR_KNEEPOINT_COUNT" class="member-name-link">CAP_PROP_XI_HDR_KNEEPOINT_COUNT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_HDR_T1" class="member-name-link">CAP_PROP_XI_HDR_T1</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_HDR_T2" class="member-name-link">CAP_PROP_XI_HDR_T2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_HEIGHT" class="member-name-link">CAP_PROP_XI_HEIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_HOUS_BACK_SIDE_TEMP" class="member-name-link">CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_HOUS_TEMP" class="member-name-link">CAP_PROP_XI_HOUS_TEMP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_HW_REVISION" class="member-name-link">CAP_PROP_XI_HW_REVISION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_IMAGE_BLACK_LEVEL" class="member-name-link">CAP_PROP_XI_IMAGE_BLACK_LEVEL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH" class="member-name-link">CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_IMAGE_DATA_FORMAT" class="member-name-link">CAP_PROP_XI_IMAGE_DATA_FORMAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA" class="member-name-link">CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_IMAGE_IS_COLOR" class="member-name-link">CAP_PROP_XI_IMAGE_IS_COLOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_IMAGE_PAYLOAD_SIZE" class="member-name-link">CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_IS_COOLED" class="member-name-link">CAP_PROP_XI_IS_COOLED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_IS_DEVICE_EXIST" class="member-name-link">CAP_PROP_XI_IS_DEVICE_EXIST</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_KNEEPOINT1" class="member-name-link">CAP_PROP_XI_KNEEPOINT1</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_KNEEPOINT2" class="member-name-link">CAP_PROP_XI_KNEEPOINT2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LED_MODE" class="member-name-link">CAP_PROP_XI_LED_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LED_SELECTOR" class="member-name-link">CAP_PROP_XI_LED_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LENS_APERTURE_VALUE" class="member-name-link">CAP_PROP_XI_LENS_APERTURE_VALUE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LENS_FEATURE" class="member-name-link">CAP_PROP_XI_LENS_FEATURE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LENS_FEATURE_SELECTOR" class="member-name-link">CAP_PROP_XI_LENS_FEATURE_SELECTOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LENS_FOCAL_LENGTH" class="member-name-link">CAP_PROP_XI_LENS_FOCAL_LENGTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LENS_FOCUS_DISTANCE" class="member-name-link">CAP_PROP_XI_LENS_FOCUS_DISTANCE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LENS_FOCUS_MOVE" class="member-name-link">CAP_PROP_XI_LENS_FOCUS_MOVE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE" class="member-name-link">CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LENS_MODE" class="member-name-link">CAP_PROP_XI_LENS_MODE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LIMIT_BANDWIDTH" class="member-name-link">CAP_PROP_XI_LIMIT_BANDWIDTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LUT_EN" class="member-name-link">CAP_PROP_XI_LUT_EN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_LUT_INDEX" class="member-name-link">CAP_PROP_XI_LUT_INDEX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_LUT_VALUE" class="member-name-link">CAP_PROP_XI_LUT_VALUE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_MANUAL_WB" class="member-name-link">CAP_PROP_XI_MANUAL_WB</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_OFFSET_X" class="member-name-link">CAP_PROP_XI_OFFSET_X</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_OFFSET_Y" class="member-name-link">CAP_PROP_XI_OFFSET_Y</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH" class="member-name-link">CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_OUTPUT_DATA_PACKING" class="member-name-link">CAP_PROP_XI_OUTPUT_DATA_PACKING</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE" class="member-name-link">CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_RECENT_FRAME" class="member-name-link">CAP_PROP_XI_RECENT_FRAME</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_REGION_MODE" class="member-name-link">CAP_PROP_XI_REGION_MODE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_REGION_SELECTOR" class="member-name-link">CAP_PROP_XI_REGION_SELECTOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_ROW_FPN_CORRECTION" class="member-name-link">CAP_PROP_XI_ROW_FPN_CORRECTION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SENSOR_BOARD_TEMP" class="member-name-link">CAP_PROP_XI_SENSOR_BOARD_TEMP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ" class="member-name-link">CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX" class="member-name-link">CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH" class="member-name-link">CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SENSOR_FEATURE_SELECTOR" class="member-name-link">CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_SENSOR_FEATURE_VALUE" class="member-name-link">CAP_PROP_XI_SENSOR_FEATURE_VALUE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SENSOR_MODE" class="member-name-link">CAP_PROP_XI_SENSOR_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT" class="member-name-link">CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SENSOR_TAPS" class="member-name-link">CAP_PROP_XI_SENSOR_TAPS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_SHARPNESS" class="member-name-link">CAP_PROP_XI_SHARPNESS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_SHUTTER_TYPE" class="member-name-link">CAP_PROP_XI_SHUTTER_TYPE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TARGET_TEMP" class="member-name-link">CAP_PROP_XI_TARGET_TEMP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_TEST_PATTERN" class="member-name-link">CAP_PROP_XI_TEST_PATTERN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR" class="member-name-link">CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_TIMEOUT" class="member-name-link">CAP_PROP_XI_TIMEOUT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT" class="member-name-link">CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_TRG_DELAY" class="member-name-link">CAP_PROP_XI_TRG_DELAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TRG_SELECTOR" class="member-name-link">CAP_PROP_XI_TRG_SELECTOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_TRG_SOFTWARE" class="member-name-link">CAP_PROP_XI_TRG_SOFTWARE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TRG_SOURCE" class="member-name-link">CAP_PROP_XI_TRG_SOURCE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_TS_RST_MODE" class="member-name-link">CAP_PROP_XI_TS_RST_MODE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_TS_RST_SOURCE" class="member-name-link">CAP_PROP_XI_TS_RST_SOURCE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_USED_FFS_SIZE" class="member-name-link">CAP_PROP_XI_USED_FFS_SIZE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_WB_KB" class="member-name-link">CAP_PROP_XI_WB_KB</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_WB_KG" class="member-name-link">CAP_PROP_XI_WB_KG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_XI_WB_KR" class="member-name-link">CAP_PROP_XI_WB_KR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PROP_XI_WIDTH" class="member-name-link">CAP_PROP_XI_WIDTH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PROP_ZOOM" class="member-name-link">CAP_PROP_ZOOM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI" class="member-name-link">CAP_PVAPI</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_DECIMATION_2OUTOF16" class="member-name-link">CAP_PVAPI_DECIMATION_2OUTOF16</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_DECIMATION_2OUTOF4" class="member-name-link">CAP_PVAPI_DECIMATION_2OUTOF4</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_DECIMATION_2OUTOF8" class="member-name-link">CAP_PVAPI_DECIMATION_2OUTOF8</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_DECIMATION_OFF" class="member-name-link">CAP_PVAPI_DECIMATION_OFF</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_FSTRIGMODE_FIXEDRATE" class="member-name-link">CAP_PVAPI_FSTRIGMODE_FIXEDRATE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_FSTRIGMODE_FREERUN" class="member-name-link">CAP_PVAPI_FSTRIGMODE_FREERUN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_FSTRIGMODE_SOFTWARE" class="member-name-link">CAP_PVAPI_FSTRIGMODE_SOFTWARE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_FSTRIGMODE_SYNCIN1" class="member-name-link">CAP_PVAPI_FSTRIGMODE_SYNCIN1</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_FSTRIGMODE_SYNCIN2" class="member-name-link">CAP_PVAPI_FSTRIGMODE_SYNCIN2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_BAYER16" class="member-name-link">CAP_PVAPI_PIXELFORMAT_BAYER16</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_BAYER8" class="member-name-link">CAP_PVAPI_PIXELFORMAT_BAYER8</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_BGR24" class="member-name-link">CAP_PVAPI_PIXELFORMAT_BGR24</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_BGRA32" class="member-name-link">CAP_PVAPI_PIXELFORMAT_BGRA32</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_MONO16" class="member-name-link">CAP_PVAPI_PIXELFORMAT_MONO16</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_MONO8" class="member-name-link">CAP_PVAPI_PIXELFORMAT_MONO8</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_RGB24" class="member-name-link">CAP_PVAPI_PIXELFORMAT_RGB24</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_PVAPI_PIXELFORMAT_RGBA32" class="member-name-link">CAP_PVAPI_PIXELFORMAT_RGBA32</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_QT" class="member-name-link">CAP_QT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_REALSENSE" class="member-name-link">CAP_REALSENSE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_UEYE" class="member-name-link">CAP_UEYE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_UNICAP" class="member-name-link">CAP_UNICAP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_V4L" class="member-name-link">CAP_V4L</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_V4L2" class="member-name-link">CAP_V4L2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_VFW" class="member-name-link">CAP_VFW</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_WINRT" class="member-name-link">CAP_WINRT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CAP_XIAPI" class="member-name-link">CAP_XIAPI</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CAP_XINE" class="member-name-link">CAP_XINE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEO_ACCELERATION_ANY" class="member-name-link">VIDEO_ACCELERATION_ANY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEO_ACCELERATION_D3D11" class="member-name-link">VIDEO_ACCELERATION_D3D11</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEO_ACCELERATION_MFX" class="member-name-link">VIDEO_ACCELERATION_MFX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEO_ACCELERATION_NONE" class="member-name-link">VIDEO_ACCELERATION_NONE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEO_ACCELERATION_VAAPI" class="member-name-link">VIDEO_ACCELERATION_VAAPI</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_DEPTH" class="member-name-link">VIDEOWRITER_PROP_DEPTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_DTS_DELAY" class="member-name-link">VIDEOWRITER_PROP_DTS_DELAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_FRAMEBYTES" class="member-name-link">VIDEOWRITER_PROP_FRAMEBYTES</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_HW_ACCELERATION" class="member-name-link">VIDEOWRITER_PROP_HW_ACCELERATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL" class="member-name-link">VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_HW_DEVICE" class="member-name-link">VIDEOWRITER_PROP_HW_DEVICE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_IS_COLOR" class="member-name-link">VIDEOWRITER_PROP_IS_COLOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_KEY_FLAG" class="member-name-link">VIDEOWRITER_PROP_KEY_FLAG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_KEY_INTERVAL" class="member-name-link">VIDEOWRITER_PROP_KEY_INTERVAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_NSTRIPES" class="member-name-link">VIDEOWRITER_PROP_NSTRIPES</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_PTS" class="member-name-link">VIDEOWRITER_PROP_PTS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VIDEOWRITER_PROP_QUALITY" class="member-name-link">VIDEOWRITER_PROP_QUALITY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#VIDEOWRITER_PROP_RAW_VIDEO" class="member-name-link">VIDEOWRITER_PROP_RAW_VIDEO</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Videoio</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getBackendName(int)" class="member-name-link">getBackendName</a><wbr>(int&nbsp;api)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns backend API name or "UnknownVideoAPI(xxx)"</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getCameraBackendPluginVersion(int,int%5B%5D,int%5B%5D)" class="member-name-link">getCameraBackendPluginVersion</a><wbr>(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns description and ABI/API version of videoio plugin's camera interface</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getStreamBackendPluginVersion(int,int%5B%5D,int%5B%5D)" class="member-name-link">getStreamBackendPluginVersion</a><wbr>(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns description and ABI/API version of videoio plugin's stream capture interface</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getStreamBufferedBackendPluginVersion(int,int%5B%5D,int%5B%5D)" class="member-name-link">getStreamBufferedBackendPluginVersion</a><wbr>(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns description and ABI/API version of videoio plugin's buffer capture interface</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getWriterBackendPluginVersion(int,int%5B%5D,int%5B%5D)" class="member-name-link">getWriterBackendPluginVersion</a><wbr>(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns description and ABI/API version of videoio plugin's writer interface</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#hasBackend(int)" class="member-name-link">hasBackend</a><wbr>(int&nbsp;api)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns true if backend is available</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isBackendBuiltIn(int)" class="member-name-link">isBackendBuiltIn</a><wbr>(int&nbsp;api)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns true if backend is built in (false if backend is used as plugin)</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="CAP_PROP_DC1394_OFF">
<h3>CAP_PROP_DC1394_OFF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DC1394_OFF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_OFF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_DC1394_MODE_MANUAL">
<h3>CAP_PROP_DC1394_MODE_MANUAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DC1394_MODE_MANUAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_MANUAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_DC1394_MODE_AUTO">
<h3>CAP_PROP_DC1394_MODE_AUTO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DC1394_MODE_AUTO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_AUTO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">
<h3>CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_DC1394_MAX">
<h3>CAP_PROP_DC1394_MAX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DC1394_MAX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MAX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR">
<h3>CAP_OPENNI_DEPTH_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IMAGE_GENERATOR">
<h3>CAP_OPENNI_IMAGE_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IMAGE_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IR_GENERATOR">
<h3>CAP_OPENNI_IR_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IR_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_GENERATORS_MASK">
<h3>CAP_OPENNI_GENERATORS_MASK</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_GENERATORS_MASK</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_GENERATORS_MASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_OUTPUT_MODE">
<h3>CAP_PROP_OPENNI_OUTPUT_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_OUTPUT_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_OUTPUT_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_FRAME_MAX_DEPTH">
<h3>CAP_PROP_OPENNI_FRAME_MAX_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_FRAME_MAX_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FRAME_MAX_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_BASELINE">
<h3>CAP_PROP_OPENNI_BASELINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_BASELINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_BASELINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_FOCAL_LENGTH">
<h3>CAP_PROP_OPENNI_FOCAL_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_FOCAL_LENGTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FOCAL_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_REGISTRATION">
<h3>CAP_PROP_OPENNI_REGISTRATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_REGISTRATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_REGISTRATION_ON">
<h3>CAP_PROP_OPENNI_REGISTRATION_ON</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_REGISTRATION_ON</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION_ON">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_APPROX_FRAME_SYNC">
<h3>CAP_PROP_OPENNI_APPROX_FRAME_SYNC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_APPROX_FRAME_SYNC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_APPROX_FRAME_SYNC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_MAX_BUFFER_SIZE">
<h3>CAP_PROP_OPENNI_MAX_BUFFER_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_MAX_BUFFER_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_BUFFER_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_CIRCLE_BUFFER">
<h3>CAP_PROP_OPENNI_CIRCLE_BUFFER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_CIRCLE_BUFFER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_CIRCLE_BUFFER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_MAX_TIME_DURATION">
<h3>CAP_PROP_OPENNI_MAX_TIME_DURATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_MAX_TIME_DURATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_TIME_DURATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI_GENERATOR_PRESENT">
<h3>CAP_PROP_OPENNI_GENERATOR_PRESENT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI_GENERATOR_PRESENT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_GENERATOR_PRESENT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI2_SYNC">
<h3>CAP_PROP_OPENNI2_SYNC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI2_SYNC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_SYNC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPENNI2_MIRROR">
<h3>CAP_PROP_OPENNI2_MIRROR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPENNI2_MIRROR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_MIRROR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IMAGE_GENERATOR_PRESENT">
<h3>CAP_OPENNI_IMAGE_GENERATOR_PRESENT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IMAGE_GENERATOR_PRESENT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_PRESENT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">
<h3>CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR_PRESENT">
<h3>CAP_OPENNI_DEPTH_GENERATOR_PRESENT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR_PRESENT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_PRESENT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR_BASELINE">
<h3>CAP_OPENNI_DEPTH_GENERATOR_BASELINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR_BASELINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_BASELINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">
<h3>CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">
<h3>CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">
<h3>CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IR_GENERATOR_PRESENT">
<h3>CAP_OPENNI_IR_GENERATOR_PRESENT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IR_GENERATOR_PRESENT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR_PRESENT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DEPTH_MAP">
<h3>CAP_OPENNI_DEPTH_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DEPTH_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_POINT_CLOUD_MAP">
<h3>CAP_OPENNI_POINT_CLOUD_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_POINT_CLOUD_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_POINT_CLOUD_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DISPARITY_MAP">
<h3>CAP_OPENNI_DISPARITY_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DISPARITY_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_DISPARITY_MAP_32F">
<h3>CAP_OPENNI_DISPARITY_MAP_32F</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_DISPARITY_MAP_32F</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP_32F">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_VALID_DEPTH_MASK">
<h3>CAP_OPENNI_VALID_DEPTH_MASK</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_VALID_DEPTH_MASK</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_VALID_DEPTH_MASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_BGR_IMAGE">
<h3>CAP_OPENNI_BGR_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_BGR_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_BGR_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_GRAY_IMAGE">
<h3>CAP_OPENNI_GRAY_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_GRAY_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_GRAY_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_IR_IMAGE">
<h3>CAP_OPENNI_IR_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_IR_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_VGA_30HZ">
<h3>CAP_OPENNI_VGA_30HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_VGA_30HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_VGA_30HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_SXGA_15HZ">
<h3>CAP_OPENNI_SXGA_15HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_SXGA_15HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_15HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_SXGA_30HZ">
<h3>CAP_OPENNI_SXGA_30HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_SXGA_30HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_30HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_QVGA_30HZ">
<h3>CAP_OPENNI_QVGA_30HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_QVGA_30HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_30HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_QVGA_60HZ">
<h3>CAP_OPENNI_QVGA_60HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_QVGA_60HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_60HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GSTREAMER_QUEUE_LENGTH">
<h3>CAP_PROP_GSTREAMER_QUEUE_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GSTREAMER_QUEUE_LENGTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GSTREAMER_QUEUE_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_MULTICASTIP">
<h3>CAP_PROP_PVAPI_MULTICASTIP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_MULTICASTIP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_MULTICASTIP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">
<h3>CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">
<h3>CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_DECIMATIONVERTICAL">
<h3>CAP_PROP_PVAPI_DECIMATIONVERTICAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_DECIMATIONVERTICAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONVERTICAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_BINNINGX">
<h3>CAP_PROP_PVAPI_BINNINGX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_BINNINGX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_BINNINGY">
<h3>CAP_PROP_PVAPI_BINNINGY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_BINNINGY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PVAPI_PIXELFORMAT">
<h3>CAP_PROP_PVAPI_PIXELFORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PVAPI_PIXELFORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_PIXELFORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_FSTRIGMODE_FREERUN">
<h3>CAP_PVAPI_FSTRIGMODE_FREERUN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_FSTRIGMODE_FREERUN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FREERUN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_FSTRIGMODE_SYNCIN1">
<h3>CAP_PVAPI_FSTRIGMODE_SYNCIN1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_FSTRIGMODE_SYNCIN1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_FSTRIGMODE_SYNCIN2">
<h3>CAP_PVAPI_FSTRIGMODE_SYNCIN2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_FSTRIGMODE_SYNCIN2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_FSTRIGMODE_FIXEDRATE">
<h3>CAP_PVAPI_FSTRIGMODE_FIXEDRATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_FSTRIGMODE_FIXEDRATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FIXEDRATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_FSTRIGMODE_SOFTWARE">
<h3>CAP_PVAPI_FSTRIGMODE_SOFTWARE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_FSTRIGMODE_SOFTWARE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SOFTWARE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_DECIMATION_OFF">
<h3>CAP_PVAPI_DECIMATION_OFF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_DECIMATION_OFF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_OFF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_DECIMATION_2OUTOF4">
<h3>CAP_PVAPI_DECIMATION_2OUTOF4</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_DECIMATION_2OUTOF4</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_DECIMATION_2OUTOF8">
<h3>CAP_PVAPI_DECIMATION_2OUTOF8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_DECIMATION_2OUTOF8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_DECIMATION_2OUTOF16">
<h3>CAP_PVAPI_DECIMATION_2OUTOF16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_DECIMATION_2OUTOF16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_MONO8">
<h3>CAP_PVAPI_PIXELFORMAT_MONO8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_MONO8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_MONO16">
<h3>CAP_PVAPI_PIXELFORMAT_MONO16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_MONO16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_BAYER8">
<h3>CAP_PVAPI_PIXELFORMAT_BAYER8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_BAYER8</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_BAYER16">
<h3>CAP_PVAPI_PIXELFORMAT_BAYER16</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_BAYER16</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER16">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_RGB24">
<h3>CAP_PVAPI_PIXELFORMAT_RGB24</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_RGB24</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGB24">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_BGR24">
<h3>CAP_PVAPI_PIXELFORMAT_BGR24</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_BGR24</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGR24">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_RGBA32">
<h3>CAP_PVAPI_PIXELFORMAT_RGBA32</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_RGBA32</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGBA32">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI_PIXELFORMAT_BGRA32">
<h3>CAP_PVAPI_PIXELFORMAT_BGRA32</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI_PIXELFORMAT_BGRA32</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGRA32">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DOWNSAMPLING">
<h3>CAP_PROP_XI_DOWNSAMPLING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DOWNSAMPLING</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DATA_FORMAT">
<h3>CAP_PROP_XI_DATA_FORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DATA_FORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DATA_FORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_OFFSET_X">
<h3>CAP_PROP_XI_OFFSET_X</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_OFFSET_X</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_X">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_OFFSET_Y">
<h3>CAP_PROP_XI_OFFSET_Y</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_OFFSET_Y</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_Y">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TRG_SOURCE">
<h3>CAP_PROP_XI_TRG_SOURCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TRG_SOURCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOURCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TRG_SOFTWARE">
<h3>CAP_PROP_XI_TRG_SOFTWARE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TRG_SOFTWARE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOFTWARE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GPI_SELECTOR">
<h3>CAP_PROP_XI_GPI_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GPI_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GPI_MODE">
<h3>CAP_PROP_XI_GPI_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GPI_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GPI_LEVEL">
<h3>CAP_PROP_XI_GPI_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GPI_LEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GPO_SELECTOR">
<h3>CAP_PROP_XI_GPO_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GPO_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GPO_MODE">
<h3>CAP_PROP_XI_GPO_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GPO_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LED_SELECTOR">
<h3>CAP_PROP_XI_LED_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LED_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LED_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LED_MODE">
<h3>CAP_PROP_XI_LED_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LED_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LED_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_MANUAL_WB">
<h3>CAP_PROP_XI_MANUAL_WB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_MANUAL_WB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_MANUAL_WB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AUTO_WB">
<h3>CAP_PROP_XI_AUTO_WB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AUTO_WB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_WB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG">
<h3>CAP_PROP_XI_AEAG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_EXP_PRIORITY">
<h3>CAP_PROP_XI_EXP_PRIORITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_EXP_PRIORITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXP_PRIORITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AE_MAX_LIMIT">
<h3>CAP_PROP_XI_AE_MAX_LIMIT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AE_MAX_LIMIT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AE_MAX_LIMIT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AG_MAX_LIMIT">
<h3>CAP_PROP_XI_AG_MAX_LIMIT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AG_MAX_LIMIT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AG_MAX_LIMIT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG_LEVEL">
<h3>CAP_PROP_XI_AEAG_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG_LEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TIMEOUT">
<h3>CAP_PROP_XI_TIMEOUT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TIMEOUT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TIMEOUT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_EXPOSURE">
<h3>CAP_PROP_XI_EXPOSURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_EXPOSURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_EXPOSURE_BURST_COUNT">
<h3>CAP_PROP_XI_EXPOSURE_BURST_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_EXPOSURE_BURST_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE_BURST_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GAIN_SELECTOR">
<h3>CAP_PROP_XI_GAIN_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GAIN_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GAIN">
<h3>CAP_PROP_XI_GAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GAIN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DOWNSAMPLING_TYPE">
<h3>CAP_PROP_XI_DOWNSAMPLING_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DOWNSAMPLING_TYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BINNING_SELECTOR">
<h3>CAP_PROP_XI_BINNING_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BINNING_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BINNING_VERTICAL">
<h3>CAP_PROP_XI_BINNING_VERTICAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BINNING_VERTICAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_VERTICAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BINNING_HORIZONTAL">
<h3>CAP_PROP_XI_BINNING_HORIZONTAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BINNING_HORIZONTAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_HORIZONTAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BINNING_PATTERN">
<h3>CAP_PROP_XI_BINNING_PATTERN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BINNING_PATTERN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_PATTERN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DECIMATION_SELECTOR">
<h3>CAP_PROP_XI_DECIMATION_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DECIMATION_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DECIMATION_VERTICAL">
<h3>CAP_PROP_XI_DECIMATION_VERTICAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DECIMATION_VERTICAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_VERTICAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DECIMATION_HORIZONTAL">
<h3>CAP_PROP_XI_DECIMATION_HORIZONTAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DECIMATION_HORIZONTAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_HORIZONTAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DECIMATION_PATTERN">
<h3>CAP_PROP_XI_DECIMATION_PATTERN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DECIMATION_PATTERN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_PATTERN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">
<h3>CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TEST_PATTERN">
<h3>CAP_PROP_XI_TEST_PATTERN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TEST_PATTERN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_DATA_FORMAT">
<h3>CAP_PROP_XI_IMAGE_DATA_FORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_DATA_FORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SHUTTER_TYPE">
<h3>CAP_PROP_XI_SHUTTER_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SHUTTER_TYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SHUTTER_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_TAPS">
<h3>CAP_PROP_XI_SENSOR_TAPS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_TAPS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_TAPS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG_ROI_OFFSET_X">
<h3>CAP_PROP_XI_AEAG_ROI_OFFSET_X</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG_ROI_OFFSET_X</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_X">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG_ROI_OFFSET_Y">
<h3>CAP_PROP_XI_AEAG_ROI_OFFSET_Y</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG_ROI_OFFSET_Y</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_Y">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG_ROI_WIDTH">
<h3>CAP_PROP_XI_AEAG_ROI_WIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG_ROI_WIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_WIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AEAG_ROI_HEIGHT">
<h3>CAP_PROP_XI_AEAG_ROI_HEIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AEAG_ROI_HEIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_HEIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BPC">
<h3>CAP_PROP_XI_BPC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BPC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BPC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_WB_KR">
<h3>CAP_PROP_XI_WB_KR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_WB_KR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_WB_KG">
<h3>CAP_PROP_XI_WB_KG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_WB_KG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_WB_KB">
<h3>CAP_PROP_XI_WB_KB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_WB_KB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_WIDTH">
<h3>CAP_PROP_XI_WIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_WIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HEIGHT">
<h3>CAP_PROP_XI_HEIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HEIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HEIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_REGION_SELECTOR">
<h3>CAP_PROP_XI_REGION_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_REGION_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_REGION_MODE">
<h3>CAP_PROP_XI_REGION_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_REGION_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LIMIT_BANDWIDTH">
<h3>CAP_PROP_XI_LIMIT_BANDWIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LIMIT_BANDWIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LIMIT_BANDWIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">
<h3>CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">
<h3>CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">
<h3>CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_OUTPUT_DATA_PACKING">
<h3>CAP_PROP_XI_OUTPUT_DATA_PACKING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_OUTPUT_DATA_PACKING</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">
<h3>CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IS_COOLED">
<h3>CAP_PROP_XI_IS_COOLED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IS_COOLED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IS_COOLED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_COOLING">
<h3>CAP_PROP_XI_COOLING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_COOLING</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COOLING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TARGET_TEMP">
<h3>CAP_PROP_XI_TARGET_TEMP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TARGET_TEMP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TARGET_TEMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CHIP_TEMP">
<h3>CAP_PROP_XI_CHIP_TEMP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CHIP_TEMP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CHIP_TEMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HOUS_TEMP">
<h3>CAP_PROP_XI_HOUS_TEMP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HOUS_TEMP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_TEMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">
<h3>CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_BOARD_TEMP">
<h3>CAP_PROP_XI_SENSOR_BOARD_TEMP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_BOARD_TEMP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_BOARD_TEMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CMS">
<h3>CAP_PROP_XI_CMS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CMS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CMS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_APPLY_CMS">
<h3>CAP_PROP_XI_APPLY_CMS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_APPLY_CMS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_APPLY_CMS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_IS_COLOR">
<h3>CAP_PROP_XI_IMAGE_IS_COLOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_IS_COLOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_IS_COLOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_COLOR_FILTER_ARRAY">
<h3>CAP_PROP_XI_COLOR_FILTER_ARRAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_COLOR_FILTER_ARRAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COLOR_FILTER_ARRAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GAMMAY">
<h3>CAP_PROP_XI_GAMMAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GAMMAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_GAMMAC">
<h3>CAP_PROP_XI_GAMMAC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_GAMMAC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SHARPNESS">
<h3>CAP_PROP_XI_SHARPNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SHARPNESS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SHARPNESS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_00">
<h3>CAP_PROP_XI_CC_MATRIX_00</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_00</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_00">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_01">
<h3>CAP_PROP_XI_CC_MATRIX_01</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_01</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_01">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_02">
<h3>CAP_PROP_XI_CC_MATRIX_02</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_02</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_02">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_03">
<h3>CAP_PROP_XI_CC_MATRIX_03</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_03</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_03">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_10">
<h3>CAP_PROP_XI_CC_MATRIX_10</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_10</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_10">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_11">
<h3>CAP_PROP_XI_CC_MATRIX_11</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_11</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_12">
<h3>CAP_PROP_XI_CC_MATRIX_12</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_12</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_12">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_13">
<h3>CAP_PROP_XI_CC_MATRIX_13</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_13</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_13">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_20">
<h3>CAP_PROP_XI_CC_MATRIX_20</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_20</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_20">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_21">
<h3>CAP_PROP_XI_CC_MATRIX_21</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_21</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_21">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_22">
<h3>CAP_PROP_XI_CC_MATRIX_22</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_22</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_22">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_23">
<h3>CAP_PROP_XI_CC_MATRIX_23</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_23</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_23">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_30">
<h3>CAP_PROP_XI_CC_MATRIX_30</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_30</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_30">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_31">
<h3>CAP_PROP_XI_CC_MATRIX_31</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_31</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_31">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_32">
<h3>CAP_PROP_XI_CC_MATRIX_32</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_32</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_32">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_CC_MATRIX_33">
<h3>CAP_PROP_XI_CC_MATRIX_33</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_CC_MATRIX_33</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_33">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEFAULT_CC_MATRIX">
<h3>CAP_PROP_XI_DEFAULT_CC_MATRIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEFAULT_CC_MATRIX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEFAULT_CC_MATRIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TRG_SELECTOR">
<h3>CAP_PROP_XI_TRG_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TRG_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">
<h3>CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEBOUNCE_EN">
<h3>CAP_PROP_XI_DEBOUNCE_EN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEBOUNCE_EN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_EN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEBOUNCE_T0">
<h3>CAP_PROP_XI_DEBOUNCE_T0</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEBOUNCE_T0</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T0">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEBOUNCE_T1">
<h3>CAP_PROP_XI_DEBOUNCE_T1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEBOUNCE_T1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEBOUNCE_POL">
<h3>CAP_PROP_XI_DEBOUNCE_POL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEBOUNCE_POL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_POL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_MODE">
<h3>CAP_PROP_XI_LENS_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_APERTURE_VALUE">
<h3>CAP_PROP_XI_LENS_APERTURE_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_APERTURE_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_APERTURE_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">
<h3>CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FOCUS_MOVE">
<h3>CAP_PROP_XI_LENS_FOCUS_MOVE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FOCUS_MOVE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FOCUS_DISTANCE">
<h3>CAP_PROP_XI_LENS_FOCUS_DISTANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FOCUS_DISTANCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_DISTANCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FOCAL_LENGTH">
<h3>CAP_PROP_XI_LENS_FOCAL_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FOCAL_LENGTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCAL_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FEATURE_SELECTOR">
<h3>CAP_PROP_XI_LENS_FEATURE_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FEATURE_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LENS_FEATURE">
<h3>CAP_PROP_XI_LENS_FEATURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LENS_FEATURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEVICE_MODEL_ID">
<h3>CAP_PROP_XI_DEVICE_MODEL_ID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEVICE_MODEL_ID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_MODEL_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEVICE_SN">
<h3>CAP_PROP_XI_DEVICE_SN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEVICE_SN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_SN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">
<h3>CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">
<h3>CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">
<h3>CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">
<h3>CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">
<h3>CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">
<h3>CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_FRAMERATE">
<h3>CAP_PROP_XI_FRAMERATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_FRAMERATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FRAMERATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_COUNTER_SELECTOR">
<h3>CAP_PROP_XI_COUNTER_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_COUNTER_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_COUNTER_VALUE">
<h3>CAP_PROP_XI_COUNTER_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_COUNTER_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_TIMING_MODE">
<h3>CAP_PROP_XI_ACQ_TIMING_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_TIMING_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TIMING_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AVAILABLE_BANDWIDTH">
<h3>CAP_PROP_XI_AVAILABLE_BANDWIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AVAILABLE_BANDWIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AVAILABLE_BANDWIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BUFFER_POLICY">
<h3>CAP_PROP_XI_BUFFER_POLICY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BUFFER_POLICY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFER_POLICY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LUT_EN">
<h3>CAP_PROP_XI_LUT_EN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LUT_EN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_EN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LUT_INDEX">
<h3>CAP_PROP_XI_LUT_INDEX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LUT_INDEX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_INDEX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_LUT_VALUE">
<h3>CAP_PROP_XI_LUT_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_LUT_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TRG_DELAY">
<h3>CAP_PROP_XI_TRG_DELAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TRG_DELAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_DELAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TS_RST_MODE">
<h3>CAP_PROP_XI_TS_RST_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TS_RST_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_TS_RST_SOURCE">
<h3>CAP_PROP_XI_TS_RST_SOURCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_TS_RST_SOURCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_SOURCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IS_DEVICE_EXIST">
<h3>CAP_PROP_XI_IS_DEVICE_EXIST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IS_DEVICE_EXIST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IS_DEVICE_EXIST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_BUFFER_SIZE">
<h3>CAP_PROP_XI_ACQ_BUFFER_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_BUFFER_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">
<h3>CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">
<h3>CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_BUFFERS_QUEUE_SIZE">
<h3>CAP_PROP_XI_BUFFERS_QUEUE_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_BUFFERS_QUEUE_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFERS_QUEUE_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">
<h3>CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_RECENT_FRAME">
<h3>CAP_PROP_XI_RECENT_FRAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_RECENT_FRAME</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_RECENT_FRAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEVICE_RESET">
<h3>CAP_PROP_XI_DEVICE_RESET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEVICE_RESET</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_RESET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_COLUMN_FPN_CORRECTION">
<h3>CAP_PROP_XI_COLUMN_FPN_CORRECTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_COLUMN_FPN_CORRECTION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COLUMN_FPN_CORRECTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_ROW_FPN_CORRECTION">
<h3>CAP_PROP_XI_ROW_FPN_CORRECTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_ROW_FPN_CORRECTION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ROW_FPN_CORRECTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_MODE">
<h3>CAP_PROP_XI_SENSOR_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HDR">
<h3>CAP_PROP_XI_HDR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HDR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HDR_KNEEPOINT_COUNT">
<h3>CAP_PROP_XI_HDR_KNEEPOINT_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HDR_KNEEPOINT_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_KNEEPOINT_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HDR_T1">
<h3>CAP_PROP_XI_HDR_T1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HDR_T1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HDR_T2">
<h3>CAP_PROP_XI_HDR_T2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HDR_T2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_KNEEPOINT1">
<h3>CAP_PROP_XI_KNEEPOINT1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_KNEEPOINT1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_KNEEPOINT2">
<h3>CAP_PROP_XI_KNEEPOINT2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_KNEEPOINT2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_IMAGE_BLACK_LEVEL">
<h3>CAP_PROP_XI_IMAGE_BLACK_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_IMAGE_BLACK_LEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_BLACK_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_HW_REVISION">
<h3>CAP_PROP_XI_HW_REVISION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_HW_REVISION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HW_REVISION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_DEBUG_LEVEL">
<h3>CAP_PROP_XI_DEBUG_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_DEBUG_LEVEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBUG_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">
<h3>CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_FFS_FILE_ID">
<h3>CAP_PROP_XI_FFS_FILE_ID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_FFS_FILE_ID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_FFS_FILE_SIZE">
<h3>CAP_PROP_XI_FFS_FILE_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_FFS_FILE_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_FREE_FFS_SIZE">
<h3>CAP_PROP_XI_FREE_FFS_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_FREE_FFS_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FREE_FFS_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_USED_FFS_SIZE">
<h3>CAP_PROP_XI_USED_FFS_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_USED_FFS_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_USED_FFS_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_FFS_ACCESS_KEY">
<h3>CAP_PROP_XI_FFS_ACCESS_KEY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_FFS_ACCESS_KEY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_ACCESS_KEY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">
<h3>CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_XI_SENSOR_FEATURE_VALUE">
<h3>CAP_PROP_XI_SENSOR_FEATURE_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_XI_SENSOR_FEATURE_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ARAVIS_AUTOTRIGGER">
<h3>CAP_PROP_ARAVIS_AUTOTRIGGER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ARAVIS_AUTOTRIGGER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ARAVIS_AUTOTRIGGER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ANDROID_DEVICE_TORCH">
<h3>CAP_PROP_ANDROID_DEVICE_TORCH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ANDROID_DEVICE_TORCH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ANDROID_DEVICE_TORCH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IOS_DEVICE_FOCUS">
<h3>CAP_PROP_IOS_DEVICE_FOCUS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IOS_DEVICE_FOCUS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FOCUS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IOS_DEVICE_EXPOSURE">
<h3>CAP_PROP_IOS_DEVICE_EXPOSURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IOS_DEVICE_EXPOSURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_EXPOSURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IOS_DEVICE_FLASH">
<h3>CAP_PROP_IOS_DEVICE_FLASH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IOS_DEVICE_FLASH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FLASH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IOS_DEVICE_WHITEBALANCE">
<h3>CAP_PROP_IOS_DEVICE_WHITEBALANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IOS_DEVICE_WHITEBALANCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_WHITEBALANCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IOS_DEVICE_TORCH">
<h3>CAP_PROP_IOS_DEVICE_TORCH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IOS_DEVICE_TORCH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_TORCH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_OFFSET_X">
<h3>CAP_PROP_GIGA_FRAME_OFFSET_X</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_OFFSET_X</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_X">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_OFFSET_Y">
<h3>CAP_PROP_GIGA_FRAME_OFFSET_Y</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_OFFSET_Y</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_Y">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_WIDTH_MAX">
<h3>CAP_PROP_GIGA_FRAME_WIDTH_MAX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_WIDTH_MAX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_WIDTH_MAX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_HEIGH_MAX">
<h3>CAP_PROP_GIGA_FRAME_HEIGH_MAX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_HEIGH_MAX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_HEIGH_MAX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_SENS_WIDTH">
<h3>CAP_PROP_GIGA_FRAME_SENS_WIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_SENS_WIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_WIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GIGA_FRAME_SENS_HEIGH">
<h3>CAP_PROP_GIGA_FRAME_SENS_HEIGH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GIGA_FRAME_SENS_HEIGH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_HEIGH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_PROFILE_COUNT">
<h3>CAP_PROP_INTELPERC_PROFILE_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_PROFILE_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_PROFILE_IDX">
<h3>CAP_PROP_INTELPERC_PROFILE_IDX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_PROFILE_IDX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_IDX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">
<h3>CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">
<h3>CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">
<h3>CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">
<h3>CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">
<h3>CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_DEPTH_GENERATOR">
<h3>CAP_INTELPERC_DEPTH_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_DEPTH_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_IMAGE_GENERATOR">
<h3>CAP_INTELPERC_IMAGE_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_IMAGE_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_IR_GENERATOR">
<h3>CAP_INTELPERC_IR_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_IR_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IR_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_GENERATORS_MASK">
<h3>CAP_INTELPERC_GENERATORS_MASK</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_GENERATORS_MASK</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_GENERATORS_MASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_DEPTH_MAP">
<h3>CAP_INTELPERC_DEPTH_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_DEPTH_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_UVDEPTH_MAP">
<h3>CAP_INTELPERC_UVDEPTH_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_UVDEPTH_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_UVDEPTH_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_IR_MAP">
<h3>CAP_INTELPERC_IR_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_IR_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IR_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC_IMAGE">
<h3>CAP_INTELPERC_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_PREVIEW">
<h3>CAP_PROP_GPHOTO2_PREVIEW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_PREVIEW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_PREVIEW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">
<h3>CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_RELOAD_CONFIG">
<h3>CAP_PROP_GPHOTO2_RELOAD_CONFIG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_RELOAD_CONFIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_CONFIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">
<h3>CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_COLLECT_MSGS">
<h3>CAP_PROP_GPHOTO2_COLLECT_MSGS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_COLLECT_MSGS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_COLLECT_MSGS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GPHOTO2_FLUSH_MSGS">
<h3>CAP_PROP_GPHOTO2_FLUSH_MSGS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GPHOTO2_FLUSH_MSGS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_FLUSH_MSGS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SPEED">
<h3>CAP_PROP_SPEED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SPEED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SPEED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_APERTURE">
<h3>CAP_PROP_APERTURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_APERTURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_APERTURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_EXPOSUREPROGRAM">
<h3>CAP_PROP_EXPOSUREPROGRAM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_EXPOSUREPROGRAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_EXPOSUREPROGRAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_VIEWFINDER">
<h3>CAP_PROP_VIEWFINDER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_VIEWFINDER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIEWFINDER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IMAGES_BASE">
<h3>CAP_PROP_IMAGES_BASE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IMAGES_BASE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IMAGES_BASE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IMAGES_LAST">
<h3>CAP_PROP_IMAGES_LAST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IMAGES_LAST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IMAGES_LAST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEO_ACCELERATION_NONE">
<h3>VIDEO_ACCELERATION_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEO_ACCELERATION_NONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEO_ACCELERATION_ANY">
<h3>VIDEO_ACCELERATION_ANY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEO_ACCELERATION_ANY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_ANY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEO_ACCELERATION_D3D11">
<h3>VIDEO_ACCELERATION_D3D11</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEO_ACCELERATION_D3D11</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_D3D11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEO_ACCELERATION_VAAPI">
<h3>VIDEO_ACCELERATION_VAAPI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEO_ACCELERATION_VAAPI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_VAAPI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEO_ACCELERATION_MFX">
<h3>VIDEO_ACCELERATION_MFX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEO_ACCELERATION_MFX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_MFX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_ANY">
<h3>CAP_ANY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_ANY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ANY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_VFW">
<h3>CAP_VFW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_VFW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_VFW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_V4L">
<h3>CAP_V4L</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_V4L</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_V4L">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_V4L2">
<h3>CAP_V4L2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_V4L2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_V4L2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_FIREWIRE">
<h3>CAP_FIREWIRE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_FIREWIRE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FIREWIRE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_FIREWARE">
<h3>CAP_FIREWARE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_FIREWARE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FIREWARE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_IEEE1394">
<h3>CAP_IEEE1394</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_IEEE1394</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_IEEE1394">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_DC1394">
<h3>CAP_DC1394</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_DC1394</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_DC1394">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_CMU1394">
<h3>CAP_CMU1394</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_CMU1394</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_CMU1394">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_QT">
<h3>CAP_QT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_QT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_QT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_UNICAP">
<h3>CAP_UNICAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_UNICAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_UNICAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_DSHOW">
<h3>CAP_DSHOW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_DSHOW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_DSHOW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PVAPI">
<h3>CAP_PVAPI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PVAPI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI">
<h3>CAP_OPENNI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI_ASUS">
<h3>CAP_OPENNI_ASUS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI_ASUS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_ASUS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_ANDROID">
<h3>CAP_ANDROID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_ANDROID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ANDROID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_XIAPI">
<h3>CAP_XIAPI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_XIAPI</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_XIAPI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_AVFOUNDATION">
<h3>CAP_AVFOUNDATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_AVFOUNDATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_AVFOUNDATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_GIGANETIX">
<h3>CAP_GIGANETIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_GIGANETIX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GIGANETIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_MSMF">
<h3>CAP_MSMF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_MSMF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_MSMF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_WINRT">
<h3>CAP_WINRT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_WINRT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_WINRT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTELPERC">
<h3>CAP_INTELPERC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTELPERC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_REALSENSE">
<h3>CAP_REALSENSE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_REALSENSE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_REALSENSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI2">
<h3>CAP_OPENNI2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI2_ASUS">
<h3>CAP_OPENNI2_ASUS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI2_ASUS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2_ASUS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENNI2_ASTRA">
<h3>CAP_OPENNI2_ASTRA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENNI2_ASTRA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2_ASTRA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_GPHOTO2">
<h3>CAP_GPHOTO2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_GPHOTO2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GPHOTO2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_GSTREAMER">
<h3>CAP_GSTREAMER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_GSTREAMER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GSTREAMER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_FFMPEG">
<h3>CAP_FFMPEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_FFMPEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FFMPEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_IMAGES">
<h3>CAP_IMAGES</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_IMAGES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_IMAGES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_ARAVIS">
<h3>CAP_ARAVIS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_ARAVIS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ARAVIS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OPENCV_MJPEG">
<h3>CAP_OPENCV_MJPEG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OPENCV_MJPEG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENCV_MJPEG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_INTEL_MFX">
<h3>CAP_INTEL_MFX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_INTEL_MFX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTEL_MFX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_XINE">
<h3>CAP_XINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_XINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_XINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_UEYE">
<h3>CAP_UEYE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_UEYE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_UEYE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR">
<h3>CAP_OBSENSOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_DEPTH_MAP">
<h3>CAP_OBSENSOR_DEPTH_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_DEPTH_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_BGR_IMAGE">
<h3>CAP_OBSENSOR_BGR_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_BGR_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_BGR_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_IR_IMAGE">
<h3>CAP_OBSENSOR_IR_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_IR_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_DEPTH_GENERATOR">
<h3>CAP_OBSENSOR_DEPTH_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_DEPTH_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_IMAGE_GENERATOR">
<h3>CAP_OBSENSOR_IMAGE_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_IMAGE_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IMAGE_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_IR_GENERATOR">
<h3>CAP_OBSENSOR_IR_GENERATOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_IR_GENERATOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_GENERATOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_OBSENSOR_GENERATORS_MASK">
<h3>CAP_OBSENSOR_GENERATORS_MASK</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_OBSENSOR_GENERATORS_MASK</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_GENERATORS_MASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OBSENSOR_INTRINSIC_FX">
<h3>CAP_PROP_OBSENSOR_INTRINSIC_FX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OBSENSOR_INTRINSIC_FX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OBSENSOR_INTRINSIC_FY">
<h3>CAP_PROP_OBSENSOR_INTRINSIC_FY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OBSENSOR_INTRINSIC_FY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OBSENSOR_INTRINSIC_CX">
<h3>CAP_PROP_OBSENSOR_INTRINSIC_CX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OBSENSOR_INTRINSIC_CX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OBSENSOR_INTRINSIC_CY">
<h3>CAP_PROP_OBSENSOR_INTRINSIC_CY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OBSENSOR_INTRINSIC_CY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_POS_MSEC">
<h3>CAP_PROP_POS_MSEC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_POS_MSEC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_MSEC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_POS_FRAMES">
<h3>CAP_PROP_POS_FRAMES</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_POS_FRAMES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_FRAMES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_POS_AVI_RATIO">
<h3>CAP_PROP_POS_AVI_RATIO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_POS_AVI_RATIO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_AVI_RATIO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FRAME_WIDTH">
<h3>CAP_PROP_FRAME_WIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FRAME_WIDTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_WIDTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FRAME_HEIGHT">
<h3>CAP_PROP_FRAME_HEIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FRAME_HEIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_HEIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FPS">
<h3>CAP_PROP_FPS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FPS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FPS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FOURCC">
<h3>CAP_PROP_FOURCC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FOURCC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FOURCC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FRAME_COUNT">
<h3>CAP_PROP_FRAME_COUNT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FRAME_COUNT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_COUNT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FORMAT">
<h3>CAP_PROP_FORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_MODE">
<h3>CAP_PROP_MODE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_MODE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_BRIGHTNESS">
<h3>CAP_PROP_BRIGHTNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_BRIGHTNESS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BRIGHTNESS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_CONTRAST">
<h3>CAP_PROP_CONTRAST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_CONTRAST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CONTRAST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SATURATION">
<h3>CAP_PROP_SATURATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SATURATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SATURATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_HUE">
<h3>CAP_PROP_HUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_HUE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GAIN">
<h3>CAP_PROP_GAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GAIN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GAIN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_EXPOSURE">
<h3>CAP_PROP_EXPOSURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_EXPOSURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_EXPOSURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_CONVERT_RGB">
<h3>CAP_PROP_CONVERT_RGB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_CONVERT_RGB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CONVERT_RGB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_WHITE_BALANCE_BLUE_U">
<h3>CAP_PROP_WHITE_BALANCE_BLUE_U</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_WHITE_BALANCE_BLUE_U</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_BLUE_U">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_RECTIFICATION">
<h3>CAP_PROP_RECTIFICATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_RECTIFICATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_RECTIFICATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_MONOCHROME">
<h3>CAP_PROP_MONOCHROME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_MONOCHROME</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_MONOCHROME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SHARPNESS">
<h3>CAP_PROP_SHARPNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SHARPNESS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SHARPNESS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUTO_EXPOSURE">
<h3>CAP_PROP_AUTO_EXPOSURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUTO_EXPOSURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTO_EXPOSURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GAMMA">
<h3>CAP_PROP_GAMMA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GAMMA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GAMMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_TEMPERATURE">
<h3>CAP_PROP_TEMPERATURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_TEMPERATURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TEMPERATURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_TRIGGER">
<h3>CAP_PROP_TRIGGER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_TRIGGER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TRIGGER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_TRIGGER_DELAY">
<h3>CAP_PROP_TRIGGER_DELAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_TRIGGER_DELAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TRIGGER_DELAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_WHITE_BALANCE_RED_V">
<h3>CAP_PROP_WHITE_BALANCE_RED_V</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_WHITE_BALANCE_RED_V</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_RED_V">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ZOOM">
<h3>CAP_PROP_ZOOM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ZOOM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ZOOM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FOCUS">
<h3>CAP_PROP_FOCUS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FOCUS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FOCUS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_GUID">
<h3>CAP_PROP_GUID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_GUID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GUID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ISO_SPEED">
<h3>CAP_PROP_ISO_SPEED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ISO_SPEED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ISO_SPEED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_BACKLIGHT">
<h3>CAP_PROP_BACKLIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_BACKLIGHT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BACKLIGHT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PAN">
<h3>CAP_PROP_PAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_TILT">
<h3>CAP_PROP_TILT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_TILT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TILT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ROLL">
<h3>CAP_PROP_ROLL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ROLL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ROLL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_IRIS">
<h3>CAP_PROP_IRIS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_IRIS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IRIS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SETTINGS">
<h3>CAP_PROP_SETTINGS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SETTINGS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SETTINGS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_BUFFERSIZE">
<h3>CAP_PROP_BUFFERSIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_BUFFERSIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BUFFERSIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUTOFOCUS">
<h3>CAP_PROP_AUTOFOCUS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUTOFOCUS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTOFOCUS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SAR_NUM">
<h3>CAP_PROP_SAR_NUM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SAR_NUM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SAR_NUM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_SAR_DEN">
<h3>CAP_PROP_SAR_DEN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_SAR_DEN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SAR_DEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_BACKEND">
<h3>CAP_PROP_BACKEND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_BACKEND</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BACKEND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_CHANNEL">
<h3>CAP_PROP_CHANNEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_CHANNEL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CHANNEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUTO_WB">
<h3>CAP_PROP_AUTO_WB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUTO_WB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTO_WB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_WB_TEMPERATURE">
<h3>CAP_PROP_WB_TEMPERATURE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_WB_TEMPERATURE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WB_TEMPERATURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_CODEC_PIXEL_FORMAT">
<h3>CAP_PROP_CODEC_PIXEL_FORMAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_CODEC_PIXEL_FORMAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CODEC_PIXEL_FORMAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_BITRATE">
<h3>CAP_PROP_BITRATE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_BITRATE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BITRATE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ORIENTATION_META">
<h3>CAP_PROP_ORIENTATION_META</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ORIENTATION_META</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_META">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_ORIENTATION_AUTO">
<h3>CAP_PROP_ORIENTATION_AUTO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_ORIENTATION_AUTO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_AUTO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_HW_ACCELERATION">
<h3>CAP_PROP_HW_ACCELERATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_HW_ACCELERATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_HW_DEVICE">
<h3>CAP_PROP_HW_DEVICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_HW_DEVICE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_DEVICE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_HW_ACCELERATION_USE_OPENCL">
<h3>CAP_PROP_HW_ACCELERATION_USE_OPENCL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_HW_ACCELERATION_USE_OPENCL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION_USE_OPENCL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_OPEN_TIMEOUT_MSEC">
<h3>CAP_PROP_OPEN_TIMEOUT_MSEC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_OPEN_TIMEOUT_MSEC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPEN_TIMEOUT_MSEC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_READ_TIMEOUT_MSEC">
<h3>CAP_PROP_READ_TIMEOUT_MSEC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_READ_TIMEOUT_MSEC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_READ_TIMEOUT_MSEC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_STREAM_OPEN_TIME_USEC">
<h3>CAP_PROP_STREAM_OPEN_TIME_USEC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_STREAM_OPEN_TIME_USEC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_STREAM_OPEN_TIME_USEC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_VIDEO_TOTAL_CHANNELS">
<h3>CAP_PROP_VIDEO_TOTAL_CHANNELS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_VIDEO_TOTAL_CHANNELS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIDEO_TOTAL_CHANNELS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_VIDEO_STREAM">
<h3>CAP_PROP_VIDEO_STREAM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_VIDEO_STREAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIDEO_STREAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_STREAM">
<h3>CAP_PROP_AUDIO_STREAM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_STREAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_STREAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_POS">
<h3>CAP_PROP_AUDIO_POS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_POS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_POS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_SHIFT_NSEC">
<h3>CAP_PROP_AUDIO_SHIFT_NSEC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_SHIFT_NSEC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SHIFT_NSEC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_DATA_DEPTH">
<h3>CAP_PROP_AUDIO_DATA_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_DATA_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_DATA_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_SAMPLES_PER_SECOND">
<h3>CAP_PROP_AUDIO_SAMPLES_PER_SECOND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_SAMPLES_PER_SECOND</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SAMPLES_PER_SECOND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_BASE_INDEX">
<h3>CAP_PROP_AUDIO_BASE_INDEX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_BASE_INDEX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_BASE_INDEX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_TOTAL_CHANNELS">
<h3>CAP_PROP_AUDIO_TOTAL_CHANNELS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_TOTAL_CHANNELS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_CHANNELS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_TOTAL_STREAMS">
<h3>CAP_PROP_AUDIO_TOTAL_STREAMS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_TOTAL_STREAMS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_STREAMS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_AUDIO_SYNCHRONIZE">
<h3>CAP_PROP_AUDIO_SYNCHRONIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_AUDIO_SYNCHRONIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SYNCHRONIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_LRF_HAS_KEY_FRAME">
<h3>CAP_PROP_LRF_HAS_KEY_FRAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_LRF_HAS_KEY_FRAME</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_LRF_HAS_KEY_FRAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_CODEC_EXTRADATA_INDEX">
<h3>CAP_PROP_CODEC_EXTRADATA_INDEX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_CODEC_EXTRADATA_INDEX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CODEC_EXTRADATA_INDEX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_FRAME_TYPE">
<h3>CAP_PROP_FRAME_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_FRAME_TYPE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_N_THREADS">
<h3>CAP_PROP_N_THREADS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_N_THREADS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_N_THREADS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_PTS">
<h3>CAP_PROP_PTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_PTS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CAP_PROP_DTS_DELAY">
<h3>CAP_PROP_DTS_DELAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CAP_PROP_DTS_DELAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DTS_DELAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_QUALITY">
<h3>VIDEOWRITER_PROP_QUALITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_QUALITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_QUALITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_FRAMEBYTES">
<h3>VIDEOWRITER_PROP_FRAMEBYTES</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_FRAMEBYTES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_FRAMEBYTES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_NSTRIPES">
<h3>VIDEOWRITER_PROP_NSTRIPES</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_NSTRIPES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_NSTRIPES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_IS_COLOR">
<h3>VIDEOWRITER_PROP_IS_COLOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_IS_COLOR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_IS_COLOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_DEPTH">
<h3>VIDEOWRITER_PROP_DEPTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_DEPTH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_DEPTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_HW_ACCELERATION">
<h3>VIDEOWRITER_PROP_HW_ACCELERATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_HW_ACCELERATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_HW_DEVICE">
<h3>VIDEOWRITER_PROP_HW_DEVICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_HW_DEVICE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_DEVICE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">
<h3>VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_RAW_VIDEO">
<h3>VIDEOWRITER_PROP_RAW_VIDEO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_RAW_VIDEO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_RAW_VIDEO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_KEY_INTERVAL">
<h3>VIDEOWRITER_PROP_KEY_INTERVAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_KEY_INTERVAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_KEY_INTERVAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_KEY_FLAG">
<h3>VIDEOWRITER_PROP_KEY_FLAG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_KEY_FLAG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_KEY_FLAG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_PTS">
<h3>VIDEOWRITER_PROP_PTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_PTS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_PTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VIDEOWRITER_PROP_DTS_DELAY">
<h3>VIDEOWRITER_PROP_DTS_DELAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VIDEOWRITER_PROP_DTS_DELAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_DTS_DELAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Videoio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Videoio</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getBackendName(int)">
<h3>getBackendName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBackendName</span><wbr><span class="parameters">(int&nbsp;api)</span></div>
<div class="block">Returns backend API name or "UnknownVideoAPI(xxx)"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - backend ID (#VideoCaptureAPIs)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasBackend(int)">
<h3>hasBackend</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasBackend</span><wbr><span class="parameters">(int&nbsp;api)</span></div>
<div class="block">Returns true if backend is available</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isBackendBuiltIn(int)">
<h3>isBackendBuiltIn</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBackendBuiltIn</span><wbr><span class="parameters">(int&nbsp;api)</span></div>
<div class="block">Returns true if backend is built in (false if backend is used as plugin)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCameraBackendPluginVersion(int,int[],int[])">
<h3>getCameraBackendPluginVersion</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCameraBackendPluginVersion</span><wbr><span class="parameters">(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</span></div>
<div class="block">Returns description and ABI/API version of videoio plugin's camera interface</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStreamBackendPluginVersion(int,int[],int[])">
<h3>getStreamBackendPluginVersion</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStreamBackendPluginVersion</span><wbr><span class="parameters">(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</span></div>
<div class="block">Returns description and ABI/API version of videoio plugin's stream capture interface</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStreamBufferedBackendPluginVersion(int,int[],int[])">
<h3>getStreamBufferedBackendPluginVersion</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStreamBufferedBackendPluginVersion</span><wbr><span class="parameters">(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</span></div>
<div class="block">Returns description and ABI/API version of videoio plugin's buffer capture interface</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWriterBackendPluginVersion(int,int[],int[])">
<h3>getWriterBackendPluginVersion</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWriterBackendPluginVersion</span><wbr><span class="parameters">(int&nbsp;api,
 int[]&nbsp;version_ABI,
 int[]&nbsp;version_API)</span></div>
<div class="block">Returns description and ABI/API version of videoio plugin's writer interface</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
