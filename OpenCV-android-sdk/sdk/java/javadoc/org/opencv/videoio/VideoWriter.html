<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>VideoWriter (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.videoio, class: VideoWriter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.videoio</a></div>
<h1 title="Class VideoWriter" class="title">Class VideoWriter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.videoio.VideoWriter</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">VideoWriter</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Video writer class.

 The class provides C++ API for writing video files or image sequences.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">VideoWriter</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructors

     The constructors/functions initialize video writers.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,double,org.opencv.core.Size)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,double,org.opencv.core.Size,boolean)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color">
<div class="block">The <code>params</code> parameter allows to specify extra encoder parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ...</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,int,double,org.opencv.core.Size)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code></div>
<div class="col-last even-row-color">
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,int,double,org.opencv.core.Size,boolean)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</code></div>
<div class="col-last odd-row-color">
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,int,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)" class="member-name-link">VideoWriter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="VideoWriter.html" title="class in org.opencv.videoio">VideoWriter</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fourcc(char,char,char,char)" class="member-name-link">fourcc</a><wbr>(char&nbsp;c1,
 char&nbsp;c2,
 char&nbsp;c3,
 char&nbsp;c4)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Concatenates 4 chars to a fourcc code</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(int)" class="member-name-link">get</a><wbr>(int&nbsp;propId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the specified VideoWriter property</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBackendName()" class="member-name-link">getBackendName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isOpened()" class="member-name-link">isOpened</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if video writer has been successfully initialized.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,double,org.opencv.core.Size)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initializes or reinitializes video writer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,double,org.opencv.core.Size,boolean)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initializes or reinitializes video writer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,int,double,org.opencv.core.Size)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,int,double,org.opencv.core.Size,boolean)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#open(java.lang.String,int,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)" class="member-name-link">open</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes the video writer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set(int,double)" class="member-name-link">set</a><wbr>(int&nbsp;propId,
 double&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a property in the VideoWriter.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(org.opencv.core.Mat)" class="member-name-link">write</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the next video frame</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span>()</div>
<div class="block">Default constructors

     The constructors/functions initialize video writers.
 <ul>
   <li>
        On Linux FFMPEG is used to write videos;
   </li>
   <li>
        On Windows FFMPEG or MSWF or DSHOW is used;
   </li>
   <li>
        On MacOSX AVFoundation is used.
   </li>
 </ul></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,double,org.opencv.core.Size,boolean)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the output video file.</dd>
<dd><code>fourcc</code> - 4-character code of codec used to compress the frames. For example,
     VideoWriter::fourcc('P','I','M','1') is a MPEG-1 codec, VideoWriter::fourcc('M','J','P','G')
     is a motion-jpeg codec etc. List of codes can be obtained at
     [MSDN](https://docs.microsoft.com/en-us/windows/win32/medfound/video-fourccs) page
     or with this [page](https://fourcc.org/codecs.php)
     of the fourcc site for a more complete list). FFMPEG backend with MP4 container natively uses
     other values as fourcc code: see [ObjectType](http://mp4ra.org/#/codecs),
     so you may receive a warning message from OpenCV about fourcc code conversion.</dd>
<dd><code>fps</code> - Framerate of the created video stream.</dd>
<dd><code>frameSize</code> - Size of the video frames.</dd>
<dd><code>isColor</code> - If it is not zero, the encoder will expect and encode color frames, otherwise it
     will work with grayscale frames.

     <b>Tips</b>:
 <ul>
   <li>
      With some backends <code>fourcc=-1</code> pops up the codec selection dialog from the system.
   </li>
   <li>
      To save image sequence use a proper filename (eg. <code>img_%02d.jpg</code>) and <code>fourcc=0</code>
       OR <code>fps=0</code>. Use uncompressed image format (eg. <code>img_%02d.BMP</code>) to save raw frames.
   </li>
   <li>
      Most codecs are lossy. If you want lossless video file you need to use a lossless codecs
       (eg. FFMPEG FFV1, Huffman HFYU, Lagarith LAGS, etc...)
   </li>
   <li>
      If FFMPEG is enabled, using <code>codec=0; fps=0;</code> you can create an uncompressed (raw) video file.
   </li>
   <li>
      If FFMPEG is used, we allow frames of odd width or height, but in this case we truncate
       the rightmost column/the bottom row. Probably, this should be handled more elegantly,
       but some internal functions inside FFMPEG swscale require even width/height.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,double,org.opencv.core.Size)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the output video file.</dd>
<dd><code>fourcc</code> - 4-character code of codec used to compress the frames. For example,
     VideoWriter::fourcc('P','I','M','1') is a MPEG-1 codec, VideoWriter::fourcc('M','J','P','G')
     is a motion-jpeg codec etc. List of codes can be obtained at
     [MSDN](https://docs.microsoft.com/en-us/windows/win32/medfound/video-fourccs) page
     or with this [page](https://fourcc.org/codecs.php)
     of the fourcc site for a more complete list). FFMPEG backend with MP4 container natively uses
     other values as fourcc code: see [ObjectType](http://mp4ra.org/#/codecs),
     so you may receive a warning message from OpenCV about fourcc code conversion.</dd>
<dd><code>fps</code> - Framerate of the created video stream.</dd>
<dd><code>frameSize</code> - Size of the video frames.
     will work with grayscale frames.

     <b>Tips</b>:
 <ul>
   <li>
      With some backends <code>fourcc=-1</code> pops up the codec selection dialog from the system.
   </li>
   <li>
      To save image sequence use a proper filename (eg. <code>img_%02d.jpg</code>) and <code>fourcc=0</code>
       OR <code>fps=0</code>. Use uncompressed image format (eg. <code>img_%02d.BMP</code>) to save raw frames.
   </li>
   <li>
      Most codecs are lossy. If you want lossless video file you need to use a lossless codecs
       (eg. FFMPEG FFV1, Huffman HFYU, Lagarith LAGS, etc...)
   </li>
   <li>
      If FFMPEG is enabled, using <code>codec=0; fps=0;</code> you can create an uncompressed (raw) video file.
   </li>
   <li>
      If FFMPEG is used, we allow frames of odd width or height, but in this case we truncate
       the rightmost column/the bottom row. Probably, this should be handled more elegantly,
       but some internal functions inside FFMPEG swscale require even width/height.
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,int,double,org.opencv.core.Size,boolean)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</span></div>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use. Can be used to enforce a specific reader implementation
     if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_GSTREAMER.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>isColor</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,int,double,org.opencv.core.Size)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</span></div>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use. Can be used to enforce a specific reader implementation
     if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_GSTREAMER.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
<div class="block">The <code>params</code> parameter allows to specify extra encoder parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ... .)
 see cv::VideoWriterProperties</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,int,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)">
<h3>VideoWriter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoWriter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="VideoWriter.html" title="class in org.opencv.videoio">VideoWriter</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,double,org.opencv.core.Size,boolean)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</span></div>
<div class="block">Initializes or reinitializes video writer.

     The method opens video writer. Parameters are the same as in the constructor
     VideoWriter::VideoWriter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>isColor</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if video writer has been successfully initialized

     The method first calls VideoWriter::release to close the already opened file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,double,org.opencv.core.Size)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</span></div>
<div class="block">Initializes or reinitializes video writer.

     The method opens video writer. Parameters are the same as in the constructor
     VideoWriter::VideoWriter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd><code>true</code> if video writer has been successfully initialized

     The method first calls VideoWriter::release to close the already opened file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,int,double,org.opencv.core.Size,boolean)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 boolean&nbsp;isColor)</span></div>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,int,double,org.opencv.core.Size)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
</section>
</li>
<li>
<section class="detail" id="open(java.lang.String,int,int,double,org.opencv.core.Size,org.opencv.core.MatOfInt)">
<h3>open</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">open</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 int&nbsp;apiPreference,
 int&nbsp;fourcc,
 double&nbsp;fps,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</span></div>
</section>
</li>
<li>
<section class="detail" id="isOpened()">
<h3>isOpened</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOpened</span>()</div>
<div class="block">Returns true if video writer has been successfully initialized.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">Closes the video writer.

     The method is automatically called by subsequent VideoWriter::open and by the VideoWriter
     destructor.</div>
</section>
</li>
<li>
<section class="detail" id="write(org.opencv.core.Mat)">
<h3>write</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</span></div>
<div class="block">Writes the next video frame</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - The written frame. In general, color images are expected in BGR format.

     The function/method writes the specified image to video file. It must have the same size as has
     been specified when opening the video writer.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="set(int,double)">
<h3>set</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">set</span><wbr><span class="parameters">(int&nbsp;propId,
 double&nbsp;value)</span></div>
<div class="block">Sets a property in the VideoWriter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propId</code> - Property identifier from cv::VideoWriterProperties (eg. cv::VIDEOWRITER_PROP_QUALITY)
      or one of REF: videoio_flags_others</dd>
<dd><code>value</code> - Value of the property.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the property is supported by the backend used by the VideoWriter instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="get(int)">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(int&nbsp;propId)</span></div>
<div class="block">Returns the specified VideoWriter property</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propId</code> - Property identifier from cv::VideoWriterProperties (eg. cv::VIDEOWRITER_PROP_QUALITY)
      or one of REF: videoio_flags_others</dd>
<dt>Returns:</dt>
<dd>Value for the specified property. Value 0 is returned when querying a property that is
      not supported by the backend used by the VideoWriter instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fourcc(char,char,char,char)">
<h3>fourcc</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">fourcc</span><wbr><span class="parameters">(char&nbsp;c1,
 char&nbsp;c2,
 char&nbsp;c3,
 char&nbsp;c4)</span></div>
<div class="block">Concatenates 4 chars to a fourcc code</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c1</code> - automatically generated</dd>
<dd><code>c2</code> - automatically generated</dd>
<dd><code>c3</code> - automatically generated</dd>
<dd><code>c4</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>a fourcc code

     This static method constructs the fourcc code of the codec to be used in the constructor
     VideoWriter::VideoWriter or VideoWriter::open.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBackendName()">
<h3>getBackendName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBackendName</span>()</div>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
