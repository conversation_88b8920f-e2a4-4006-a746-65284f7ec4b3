<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>CharucoDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: CharucoDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class CharucoDetector" class="title">Class CharucoDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance">org.opencv.objdetect.CharucoDetector</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CharucoDetector</span>
<span class="extends-implements">extends <a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.CharucoBoard)" class="member-name-link">CharucoDetector</a><wbr>(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</code></div>
<div class="col-last even-row-color">
<div class="block">Basic CharucoDetector constructor</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters)" class="member-name-link">CharucoDetector</a><wbr>(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams)</code></div>
<div class="col-last odd-row-color">
<div class="block">Basic CharucoDetector constructor</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters,org.opencv.objdetect.DetectorParameters)" class="member-name-link">CharucoDetector</a><wbr>(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</code></div>
<div class="col-last even-row-color">
<div class="block">Basic CharucoDetector constructor</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters,org.opencv.objdetect.DetectorParameters,org.opencv.objdetect.RefineParameters)" class="member-name-link">CharucoDetector</a><wbr>(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
 <a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</code></div>
<div class="col-last odd-row-color">
<div class="block">Basic CharucoDetector constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detectBoard</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,java.util.List)" class="member-name-link">detectBoard</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectBoard</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detect ChArUco Diamond markers</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)" class="member-name-link">detectDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detect ChArUco Diamond markers</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detect ChArUco Diamond markers</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBoard()" class="member-name-link">getBoard</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCharucoParameters()" class="member-name-link">getCharucoParameters</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDetectorParameters()" class="member-name-link">getDetectorParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRefineParameters()" class="member-name-link">getRefineParameters</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBoard(org.opencv.objdetect.CharucoBoard)" class="member-name-link">setBoard</a><wbr>(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCharucoParameters(org.opencv.objdetect.CharucoParameters)" class="member-name-link">setCharucoParameters</a><wbr>(<a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDetectorParameters(org.opencv.objdetect.DetectorParameters)" class="member-name-link">setDetectorParameters</a><wbr>(<a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefineParameters(org.opencv.objdetect.RefineParameters)" class="member-name-link">setRefineParameters</a><wbr>(<a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters,org.opencv.objdetect.DetectorParameters,org.opencv.objdetect.RefineParameters)">
<h3>CharucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CharucoDetector</span><wbr><span class="parameters">(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
 <a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</span></div>
<div class="block">Basic CharucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
<dd><code>refineParams</code> - marker refine detection parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters,org.opencv.objdetect.DetectorParameters)">
<h3>CharucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CharucoDetector</span><wbr><span class="parameters">(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</span></div>
<div class="block">Basic CharucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.CharucoBoard,org.opencv.objdetect.CharucoParameters)">
<h3>CharucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CharucoDetector</span><wbr><span class="parameters">(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
 <a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams)</span></div>
<div class="block">Basic CharucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.CharucoBoard)">
<h3>CharucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CharucoDetector</span><wbr><span class="parameters">(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</span></div>
<div class="block">Basic CharucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>board</code> - ChAruco board</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getBoard()">
<h3>getBoard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></span>&nbsp;<span class="element-name">getBoard</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setBoard(org.opencv.objdetect.CharucoBoard)">
<h3>setBoard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBoard</span><wbr><span class="parameters">(<a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</span></div>
</section>
</li>
<li>
<section class="detail" id="getCharucoParameters()">
<h3>getCharucoParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a></span>&nbsp;<span class="element-name">getCharucoParameters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setCharucoParameters(org.opencv.objdetect.CharucoParameters)">
<h3>setCharucoParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCharucoParameters</span><wbr><span class="parameters">(<a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParameters)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDetectorParameters()">
<h3>getDetectorParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></span>&nbsp;<span class="element-name">getDetectorParameters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDetectorParameters(org.opencv.objdetect.DetectorParameters)">
<h3>setDetectorParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDetectorParameters</span><wbr><span class="parameters">(<a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</span></div>
</section>
</li>
<li>
<section class="detail" id="getRefineParameters()">
<h3>getRefineParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></span>&nbsp;<span class="element-name">getRefineParameters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setRefineParameters(org.opencv.objdetect.RefineParameters)">
<h3>setRefineParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefineParameters</span><wbr><span class="parameters">(<a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</span></div>
</section>
</li>
<li>
<section class="detail" id="detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>detectBoard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectBoard</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</span></div>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.</dd>
<dd><code>markerCorners</code> - vector of already detected markers corners. For each marker, its four
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.</dd>
<dd><code>markerIds</code> - list of identifiers for each marker in corners.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners
 <b>Note:</b> After OpenCV 4.6.0, there was an incompatible change in the ChArUco pattern generation algorithm for even row counts.
 Use cv::aruco::CharucoBoard::setLegacyPattern() to ensure compatibility with patterns created using OpenCV versions prior to 4.6.0.
 For more information, see the issue: https://github.com/opencv/opencv/issues/23152</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,java.util.List)">
<h3>detectBoard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectBoard</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</span></div>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.</dd>
<dd><code>markerCorners</code> - vector of already detected markers corners. For each marker, its four
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners
 <b>Note:</b> After OpenCV 4.6.0, there was an incompatible change in the ChArUco pattern generation algorithm for even row counts.
 Use cv::aruco::CharucoBoard::setLegacyPattern() to ensure compatibility with patterns created using OpenCV versions prior to 4.6.0.
 For more information, see the issue: https://github.com/opencv/opencv/issues/23152</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectBoard(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detectBoard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectBoard</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</span></div>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners
 <b>Note:</b> After OpenCV 4.6.0, there was an incompatible change in the ChArUco pattern generation algorithm for even row counts.
 Use cv::aruco::CharucoBoard::setLegacyPattern() to ensure compatibility with patterns created using OpenCV versions prior to 4.6.0.
 For more information, see the issue: https://github.com/opencv/opencv/issues/23152</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>detectDiamonds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</span></div>
<div class="block">Detect ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.</dd>
<dd><code>markerCorners</code> - list of detected marker corners from detectMarkers function.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.</dd>
<dd><code>markerIds</code> - list of marker ids in markerCorners.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)">
<h3>detectDiamonds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</span></div>
<div class="block">Detect ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.</dd>
<dd><code>markerCorners</code> - list of detected marker corners from detectMarkers function.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>detectDiamonds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</span></div>
<div class="block">Detect ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
