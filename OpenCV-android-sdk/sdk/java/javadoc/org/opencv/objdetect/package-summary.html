<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.objdetect (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.opencv.objdetect" class="title">Package org.opencv.objdetect</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.opencv.objdetect</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.opencv</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The main functionality of ArucoDetector class is detection of markers in an image with detectMarkers() method.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseCascadeClassifier.html" title="class in org.opencv.objdetect">BaseCascadeClassifier</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Board.html" title="class in org.opencv.objdetect">Board</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Board of ArUco markers

 A board is a set of markers in the 3D space with a common coordinate system.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Cascade classifier class for object detection.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">ChArUco board is a planar chessboard where the markers are placed inside the white squares of a chessboard.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">struct DetectorParameters is used by ArucoDetector</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Dictionary is a set of unique ArUco markers of the same size

 <code>bytesList</code> storing as 2-dimensions Mat with 4-th channels (CV_8UC4 type was used) and contains the marker codewords where:
 - bytesList.rows is the dictionary size
 - each marker is encoded using <code>nbytes = ceil(markerSize*markerSize/8.)</code> bytes
 - each row contains all 4 rotations of the marker, so its length is <code>4*nbytes</code>
 - the byte order in the bytesList[i] row:
 <code>//bytes without rotation/bytes with rotation 1/bytes with rotation 2/bytes with rotation 3//</code>
 So <code>bytesList.ptr(i)[k*nbytes + j]</code> is the j-th byte of i-th marker, in its k-th rotation.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">DNN-based face detector

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_detection_yunet</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">DNN-based face recognizer

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_recognition_sface</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Planar board with grid arrangement of markers

 More common type of board.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Implementation of HOG (Histogram of Oriented Gradients) descriptor and object detector.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Objdetect.html" title="class in org.opencv.objdetect">Objdetect</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="QRCodeDetector.html" title="class in org.opencv.objdetect">QRCodeDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="QRCodeDetectorAruco.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="QRCodeDetectorAruco_Params.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco_Params</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Groups the object candidate rectangles.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="QRCodeEncoder_Params.html" title="class in org.opencv.objdetect">QRCodeEncoder_Params</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">QR code encoder parameters.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">struct RefineParameters is used by ArucoDetector</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
