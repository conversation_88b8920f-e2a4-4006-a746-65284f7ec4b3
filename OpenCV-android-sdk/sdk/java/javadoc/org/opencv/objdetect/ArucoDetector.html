<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>ArucoDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: ArucoDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class ArucoDetector" class="title">Class ArucoDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance">org.opencv.objdetect.ArucoDetector</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ArucoDetector</span>
<span class="extends-implements">extends <a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></span></div>
<div class="block">The main functionality of ArucoDetector class is detection of markers in an image with detectMarkers() method.

 After detecting some markers in the image, you can try to find undetected markers from this dictionary with
 refineDetectedMarkers() method.

 SEE: DetectorParameters, RefineParameters</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ArucoDetector</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Basic ArucoDetector constructor</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.Dictionary)" class="member-name-link">ArucoDetector</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</code></div>
<div class="col-last odd-row-color">
<div class="block">Basic ArucoDetector constructor</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.Dictionary,org.opencv.objdetect.DetectorParameters)" class="member-name-link">ArucoDetector</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</code></div>
<div class="col-last even-row-color">
<div class="block">Basic ArucoDetector constructor</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.objdetect.Dictionary,org.opencv.objdetect.DetectorParameters,org.opencv.objdetect.RefineParameters)" class="member-name-link">ArucoDetector</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
 <a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</code></div>
<div class="col-last odd-row-color">
<div class="block">Basic ArucoDetector constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Basic marker detection</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)" class="member-name-link">detectMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedImgPoints)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Basic marker detection</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDetectorParameters()" class="member-name-link">getDetectorParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDictionary()" class="member-name-link">getDictionary</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRefineParameters()" class="member-name-link">getRefineParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List)" class="member-name-link">refineDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">refineDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">refineDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">refineDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;recoveredIdxs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDetectorParameters(org.opencv.objdetect.DetectorParameters)" class="member-name-link">setDetectorParameters</a><wbr>(<a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDictionary(org.opencv.objdetect.Dictionary)" class="member-name-link">setDictionary</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefineParameters(org.opencv.objdetect.RefineParameters)" class="member-name-link">setRefineParameters</a><wbr>(<a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#empty()">empty</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.Dictionary,org.opencv.objdetect.DetectorParameters,org.opencv.objdetect.RefineParameters)">
<h3>ArucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ArucoDetector</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
 <a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</span></div>
<div class="block">Basic ArucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
<dd><code>refineParams</code> - marker refine detection parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.Dictionary,org.opencv.objdetect.DetectorParameters)">
<h3>ArucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ArucoDetector</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</span></div>
<div class="block">Basic ArucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.objdetect.Dictionary)">
<h3>ArucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ArucoDetector</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</span></div>
<div class="block">Basic ArucoDetector constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ArucoDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ArucoDetector</span>()</div>
<div class="block">Basic ArucoDetector constructor</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="detectMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)">
<h3>detectMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedImgPoints)</span></div>
<div class="block">Basic marker detection</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>corners</code> - vector of detected marker corners. For each marker, its four corners
 are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array is Nx4. The order of the corners is clockwise.</dd>
<dd><code>ids</code> - vector of identifiers of the detected markers. The identifier is of type int
 (e.g. std::vector&lt;int&gt;). For N detected markers, the size of ids is also N.
 The identifiers have the same order than the markers in the imgPoints array.</dd>
<dd><code>rejectedImgPoints</code> - contains the imgPoints of those squares whose inner code has not a
 correct codification. Useful for debugging purposes.

 Performs marker detection in the input image. Only markers included in the specific dictionary
 are searched. For each detected marker, it returns the 2D position of its corner in the image
 and its corresponding identifier.
 Note that this function does not perform pose estimation.
 <b>Note:</b> The function does not correct lens distortion or takes it into account. It's recommended to undistort
 input image with corresponding camera model, if camera parameters are known
 SEE: undistort, estimatePoseSingleMarkers,  estimatePoseBoard</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>detectMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</span></div>
<div class="block">Basic marker detection</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>corners</code> - vector of detected marker corners. For each marker, its four corners
 are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array is Nx4. The order of the corners is clockwise.</dd>
<dd><code>ids</code> - vector of identifiers of the detected markers. The identifier is of type int
 (e.g. std::vector&lt;int&gt;). For N detected markers, the size of ids is also N.
 The identifiers have the same order than the markers in the imgPoints array.
 correct codification. Useful for debugging purposes.

 Performs marker detection in the input image. Only markers included in the specific dictionary
 are searched. For each detected marker, it returns the 2D position of its corner in the image
 and its corresponding identifier.
 Note that this function does not perform pose estimation.
 <b>Note:</b> The function does not correct lens distortion or takes it into account. It's recommended to undistort
 input image with corresponding camera model, if camera parameters are known
 SEE: undistort, estimatePoseSingleMarkers,  estimatePoseBoard</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>refineDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">refineDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;recoveredIdxs)</span></div>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)</dd>
<dd><code>distCoeffs</code> - optional vector of distortion coefficients
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements</dd>
<dd><code>recoveredIdxs</code> - Optional array to returns the indexes of the recovered candidates in the
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>refineDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">refineDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs)</span></div>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)</dd>
<dd><code>distCoeffs</code> - optional vector of distortion coefficients
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>refineDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">refineDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</span></div>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="refineDetectedMarkers(org.opencv.core.Mat,org.opencv.objdetect.Board,java.util.List,org.opencv.core.Mat,java.util.List)">
<h3>refineDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">refineDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners)</span></div>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDictionary()">
<h3>getDictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">getDictionary</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDictionary(org.opencv.objdetect.Dictionary)">
<h3>setDictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDictionary</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDetectorParameters()">
<h3>getDetectorParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></span>&nbsp;<span class="element-name">getDetectorParameters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDetectorParameters(org.opencv.objdetect.DetectorParameters)">
<h3>setDetectorParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDetectorParameters</span><wbr><span class="parameters">(<a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</span></div>
</section>
</li>
<li>
<section class="detail" id="getRefineParameters()">
<h3>getRefineParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></span>&nbsp;<span class="element-name">getRefineParameters</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setRefineParameters(org.opencv.objdetect.RefineParameters)">
<h3>setRefineParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefineParameters</span><wbr><span class="parameters">(<a href="RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
