<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>BarcodeDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: BarcodeDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class BarcodeDetector" class="title">Class BarcodeDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">org.opencv.objdetect.GraphicalCodeDetector</a>
<div class="inheritance">org.opencv.objdetect.BarcodeDetector</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">BarcodeDetector</span>
<span class="extends-implements">extends <a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">BarcodeDetector</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Initialize the BarcodeDetector.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">BarcodeDetector</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt_path,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model_path)</code></div>
<div class="col-last odd-row-color">
<div class="block">Initialize the BarcodeDetector.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#decodeWithType(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,java.util.List)" class="member-name-link">decodeWithType</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes barcode in image once it's found by the detect() method.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecodeWithType(org.opencv.core.Mat,java.util.List,java.util.List)" class="member-name-link">detectAndDecodeWithType</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes barcode</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecodeWithType(org.opencv.core.Mat,java.util.List,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectAndDecodeWithType</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes barcode</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDetectorScales(org.opencv.core.MatOfFloat)" class="member-name-link">getDetectorScales</a><wbr>(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;sizes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns detector box filter sizes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDownsamplingThreshold()" class="member-name-link">getDownsamplingThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get detector downsampling threshold.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGradientThreshold()" class="member-name-link">getGradientThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get detector gradient magnitude threshold.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDetectorScales(org.opencv.core.MatOfFloat)" class="member-name-link">setDetectorScales</a><wbr>(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;sizes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set detector box filter sizes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDownsamplingThreshold(double)" class="member-name-link">setDownsamplingThreshold</a><wbr>(double&nbsp;thresh)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set detector downsampling threshold.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGradientThreshold(double)" class="member-name-link">setGradientThreshold</a><wbr>(double&nbsp;thresh)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set detector gradient magnitude threshold.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.objdetect.GraphicalCodeDetector">Methods inherited from class&nbsp;org.opencv.objdetect.<a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></h3>
<code><a href="GraphicalCodeDetector.html#decode(org.opencv.core.Mat,org.opencv.core.Mat)">decode</a>, <a href="GraphicalCodeDetector.html#decode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">decode</a>, <a href="GraphicalCodeDetector.html#decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List)">decodeMulti</a>, <a href="GraphicalCodeDetector.html#decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,java.util.List)">decodeMulti</a>, <a href="GraphicalCodeDetector.html#detect(org.opencv.core.Mat,org.opencv.core.Mat)">detect</a>, <a href="GraphicalCodeDetector.html#detectAndDecode(org.opencv.core.Mat)">detectAndDecode</a>, <a href="GraphicalCodeDetector.html#detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat)">detectAndDecode</a>, <a href="GraphicalCodeDetector.html#detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">detectAndDecode</a>, <a href="GraphicalCodeDetector.html#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List)">detectAndDecodeMulti</a>, <a href="GraphicalCodeDetector.html#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">detectAndDecodeMulti</a>, <a href="GraphicalCodeDetector.html#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)">detectAndDecodeMulti</a>, <a href="GraphicalCodeDetector.html#detectMulti(org.opencv.core.Mat,org.opencv.core.Mat)">detectMulti</a>, <a href="GraphicalCodeDetector.html#getNativeObjAddr()">getNativeObjAddr</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>BarcodeDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BarcodeDetector</span>()</div>
<div class="block">Initialize the BarcodeDetector.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>BarcodeDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BarcodeDetector</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prototxt_path,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model_path)</span></div>
<div class="block">Initialize the BarcodeDetector.

 Parameters allow to load _optional_ Super Resolution DNN model for better quality.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prototxt_path</code> - prototxt file path for the super resolution model</dd>
<dd><code>model_path</code> - model file path for the super resolution model</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="decodeWithType(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,java.util.List)">
<h3>decodeWithType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">decodeWithType</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type)</span></div>
<div class="block">Decodes barcode in image once it's found by the detect() method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing bar code.</dd>
<dd><code>points</code> - vector of rotated rectangle vertices found by detect() method (or some other algorithm).
 For N detected barcodes, the dimensions of this array should be [N][4].
 Order of four points in vector&lt;Point2f&gt; is bottomLeft, topLeft, topRight, bottomRight.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>decoded_type</code> - vector strings, specifies the type of these barcodes</dd>
<dt>Returns:</dt>
<dd>true if at least one valid barcode have been found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecodeWithType(org.opencv.core.Mat,java.util.List,java.util.List,org.opencv.core.Mat)">
<h3>detectAndDecodeWithType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectAndDecodeWithType</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Both detects and decodes barcode</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing barcode.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string(s) or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>decoded_type</code> - vector of strings, specifies the type of these barcodes</dd>
<dd><code>points</code> - optional output vector of vertices of the found  barcode rectangle. Will be empty if not found.</dd>
<dt>Returns:</dt>
<dd>true if at least one valid barcode have been found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecodeWithType(org.opencv.core.Mat,java.util.List,java.util.List)">
<h3>detectAndDecodeWithType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectAndDecodeWithType</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_type)</span></div>
<div class="block">Both detects and decodes barcode</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing barcode.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string(s) or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>decoded_type</code> - vector of strings, specifies the type of these barcodes</dd>
<dt>Returns:</dt>
<dd>true if at least one valid barcode have been found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDownsamplingThreshold()">
<h3>getDownsamplingThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getDownsamplingThreshold</span>()</div>
<div class="block">Get detector downsampling threshold.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>detector downsampling threshold</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDownsamplingThreshold(double)">
<h3>setDownsamplingThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></span>&nbsp;<span class="element-name">setDownsamplingThreshold</span><wbr><span class="parameters">(double&nbsp;thresh)</span></div>
<div class="block">Set detector downsampling threshold.

 By default, the detect method resizes the input image to this limit if the smallest image size is is greater than the threshold.
 Increasing this value can improve detection accuracy and the number of results at the expense of performance.
 Correlates with detector scales. Setting this to a large value will disable downsampling.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thresh</code> - downsampling limit to apply (default 512)
 SEE: setDetectorScales</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDetectorScales(org.opencv.core.MatOfFloat)">
<h3>getDetectorScales</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getDetectorScales</span><wbr><span class="parameters">(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;sizes)</span></div>
<div class="block">Returns detector box filter sizes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sizes</code> - output parameter for returning the sizes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDetectorScales(org.opencv.core.MatOfFloat)">
<h3>setDetectorScales</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></span>&nbsp;<span class="element-name">setDetectorScales</span><wbr><span class="parameters">(<a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;sizes)</span></div>
<div class="block">Set detector box filter sizes.

 Adjusts the value and the number of box filters used in the detect step.
 The filter sizes directly correlate with the expected line widths for a barcode. Corresponds to expected barcode distance.
 If the downsampling limit is increased, filter sizes need to be adjusted in an inversely proportional way.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sizes</code> - box filter sizes, relative to minimum dimension of the image (default [0.01, 0.03, 0.06, 0.08])</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getGradientThreshold()">
<h3>getGradientThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getGradientThreshold</span>()</div>
<div class="block">Get detector gradient magnitude threshold.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>detector gradient magnitude threshold.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGradientThreshold(double)">
<h3>setGradientThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></span>&nbsp;<span class="element-name">setGradientThreshold</span><wbr><span class="parameters">(double&nbsp;thresh)</span></div>
<div class="block">Set detector gradient magnitude threshold.

 Sets the coherence threshold for detected bounding boxes.
 Increasing this value will generate a closer fitted bounding box width and can reduce false-positives.
 Values between 16 and 1024 generally work, while too high of a value will remove valid detections.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thresh</code> - gradient magnitude threshold (default 64).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
