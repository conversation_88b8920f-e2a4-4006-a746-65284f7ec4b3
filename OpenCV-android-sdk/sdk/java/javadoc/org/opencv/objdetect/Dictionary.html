<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Dictionary (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: Dictionary">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class Dictionary" class="title">Class Dictionary</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.Dictionary</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Dictionary</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Dictionary is a set of unique ArUco markers of the same size

 <code>bytesList</code> storing as 2-dimensions Mat with 4-th channels (CV_8UC4 type was used) and contains the marker codewords where:
 - bytesList.rows is the dictionary size
 - each marker is encoded using <code>nbytes = ceil(markerSize*markerSize/8.)</code> bytes
 - each row contains all 4 rotations of the marker, so its length is <code>4*nbytes</code>
 - the byte order in the bytesList[i] row:
 <code>//bytes without rotation/bytes with rotation 1/bytes with rotation 2/bytes with rotation 3//</code>
 So <code>bytesList.ptr(i)[k*nbytes + j]</code> is the j-th byte of i-th marker, in its k-th rotation.
 <b>Note:</b> Python bindings generate matrix with shape of bytesList <code>dictionary_size x nbytes x 4</code>,
 but it should be indexed like C++ version. Python example for j-th byte of i-th marker, in its k-th rotation:
 <code>aruco_dict.bytesList[id].ravel()[k*nbytes + j]</code></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Dictionary</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,int)" class="member-name-link">Dictionary</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList,
 int&nbsp;_markerSize)</code></div>
<div class="col-last odd-row-color">
<div class="block">Basic ArUco dictionary constructor</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat,int,int)" class="member-name-link">Dictionary</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList,
 int&nbsp;_markerSize,
 int&nbsp;maxcorr)</code></div>
<div class="col-last even-row-color">
<div class="block">Basic ArUco dictionary constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateImageMarker(int,int,org.opencv.core.Mat)" class="member-name-link">generateImageMarker</a><wbr>(int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_img)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate a canonical marker image</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateImageMarker(int,int,org.opencv.core.Mat,int)" class="member-name-link">generateImageMarker</a><wbr>(int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_img,
 int&nbsp;borderBits)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate a canonical marker image</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_bytesList()" class="member-name-link">get_bytesList</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_markerSize()" class="member-name-link">get_markerSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_maxCorrectionBits()" class="member-name-link">get_maxCorrectionBits</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getBitsFromByteList(org.opencv.core.Mat,int)" class="member-name-link">getBitsFromByteList</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;byteList,
 int&nbsp;markerSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Transform list of bytes to matrix of bits</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getByteListFromBits(org.opencv.core.Mat)" class="member-name-link">getByteListFromBits</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Transform matrix of bits to list of bytes with 4 marker rotations</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDistanceToId(org.opencv.core.Mat,int)" class="member-name-link">getDistanceToId</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits,
 int&nbsp;id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns Hamming distance of the input bits to the specific id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDistanceToId(org.opencv.core.Mat,int,boolean)" class="member-name-link">getDistanceToId</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits,
 int&nbsp;id,
 boolean&nbsp;allRotations)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns Hamming distance of the input bits to the specific id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#identify(org.opencv.core.Mat,int%5B%5D,int%5B%5D,double)" class="member-name-link">identify</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;onlyBits,
 int[]&nbsp;idx,
 int[]&nbsp;rotation,
 double&nbsp;maxCorrectionRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a matrix of bits.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_bytesList(org.opencv.core.Mat)" class="member-name-link">set_bytesList</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_markerSize(int)" class="member-name-link">set_markerSize</a><wbr>(int&nbsp;markerSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_maxCorrectionBits(int)" class="member-name-link">set_maxCorrectionBits</a><wbr>(int&nbsp;maxCorrectionBits)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Dictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Dictionary</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,int,int)">
<h3>Dictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Dictionary</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList,
 int&nbsp;_markerSize,
 int&nbsp;maxcorr)</span></div>
<div class="block">Basic ArUco dictionary constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bytesList</code> - bits for all ArUco markers in dictionary see memory layout in the class description</dd>
<dd><code>_markerSize</code> - ArUco marker size in units</dd>
<dd><code>maxcorr</code> - maximum number of bits that can be corrected</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat,int)">
<h3>Dictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Dictionary</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList,
 int&nbsp;_markerSize)</span></div>
<div class="block">Basic ArUco dictionary constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bytesList</code> - bits for all ArUco markers in dictionary see memory layout in the class description</dd>
<dd><code>_markerSize</code> - ArUco marker size in units</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="identify(org.opencv.core.Mat,int[],int[],double)">
<h3>identify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">identify</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;onlyBits,
 int[]&nbsp;idx,
 int[]&nbsp;rotation,
 double&nbsp;maxCorrectionRate)</span></div>
<div class="block">Given a matrix of bits. Returns whether if marker is identified or not.

 Returns reference to the marker id in the dictionary (if any) and its rotation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>onlyBits</code> - automatically generated</dd>
<dd><code>idx</code> - automatically generated</dd>
<dd><code>rotation</code> - automatically generated</dd>
<dd><code>maxCorrectionRate</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDistanceToId(org.opencv.core.Mat,int,boolean)">
<h3>getDistanceToId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDistanceToId</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits,
 int&nbsp;id,
 boolean&nbsp;allRotations)</span></div>
<div class="block">Returns Hamming distance of the input bits to the specific id.

 If <code>allRotations</code> flag is set, the four posible marker rotations are considered</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bits</code> - automatically generated</dd>
<dd><code>id</code> - automatically generated</dd>
<dd><code>allRotations</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDistanceToId(org.opencv.core.Mat,int)">
<h3>getDistanceToId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDistanceToId</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits,
 int&nbsp;id)</span></div>
<div class="block">Returns Hamming distance of the input bits to the specific id.

 If <code>allRotations</code> flag is set, the four posible marker rotations are considered</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bits</code> - automatically generated</dd>
<dd><code>id</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImageMarker(int,int,org.opencv.core.Mat,int)">
<h3>generateImageMarker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImageMarker</span><wbr><span class="parameters">(int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_img,
 int&nbsp;borderBits)</span></div>
<div class="block">Generate a canonical marker image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - automatically generated</dd>
<dd><code>sidePixels</code> - automatically generated</dd>
<dd><code>_img</code> - automatically generated</dd>
<dd><code>borderBits</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImageMarker(int,int,org.opencv.core.Mat)">
<h3>generateImageMarker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImageMarker</span><wbr><span class="parameters">(int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_img)</span></div>
<div class="block">Generate a canonical marker image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - automatically generated</dd>
<dd><code>sidePixels</code> - automatically generated</dd>
<dd><code>_img</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getByteListFromBits(org.opencv.core.Mat)">
<h3>getByteListFromBits</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getByteListFromBits</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bits)</span></div>
<div class="block">Transform matrix of bits to list of bytes with 4 marker rotations</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bits</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBitsFromByteList(org.opencv.core.Mat,int)">
<h3>getBitsFromByteList</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getBitsFromByteList</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;byteList,
 int&nbsp;markerSize)</span></div>
<div class="block">Transform list of bytes to matrix of bits</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>byteList</code> - automatically generated</dd>
<dd><code>markerSize</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="get_bytesList()">
<h3>get_bytesList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">get_bytesList</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_bytesList(org.opencv.core.Mat)">
<h3>set_bytesList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_bytesList</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bytesList)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_markerSize()">
<h3>get_markerSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_markerSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_markerSize(int)">
<h3>set_markerSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_markerSize</span><wbr><span class="parameters">(int&nbsp;markerSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_maxCorrectionBits()">
<h3>get_maxCorrectionBits</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_maxCorrectionBits</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_maxCorrectionBits(int)">
<h3>set_maxCorrectionBits</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_maxCorrectionBits</span><wbr><span class="parameters">(int&nbsp;maxCorrectionBits)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
