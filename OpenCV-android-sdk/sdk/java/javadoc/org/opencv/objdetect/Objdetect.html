<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Objdetect (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: Objdetect">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class Objdetect" class="title">Class Objdetect</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.Objdetect</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Objdetect</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CASCADE_DO_CANNY_PRUNING" class="member-name-link">CASCADE_DO_CANNY_PRUNING</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CASCADE_DO_ROUGH_SEARCH" class="member-name-link">CASCADE_DO_ROUGH_SEARCH</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CASCADE_FIND_BIGGEST_OBJECT" class="member-name-link">CASCADE_FIND_BIGGEST_OBJECT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CASCADE_SCALE_IMAGE" class="member-name-link">CASCADE_SCALE_IMAGE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CORNER_REFINE_APRILTAG" class="member-name-link">CORNER_REFINE_APRILTAG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CORNER_REFINE_CONTOUR" class="member-name-link">CORNER_REFINE_CONTOUR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CORNER_REFINE_NONE" class="member-name-link">CORNER_REFINE_NONE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CORNER_REFINE_SUBPIX" class="member-name-link">CORNER_REFINE_SUBPIX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DetectionBasedTracker_DETECTED" class="member-name-link">DetectionBasedTracker_DETECTED</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DetectionBasedTracker_DETECTED_NOT_SHOWN_YET" class="member-name-link">DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DetectionBasedTracker_DETECTED_TEMPORARY_LOST" class="member-name-link">DetectionBasedTracker_DETECTED_TEMPORARY_LOST</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DetectionBasedTracker_WRONG_OBJECT" class="member-name-link">DetectionBasedTracker_WRONG_OBJECT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_4X4_100" class="member-name-link">DICT_4X4_100</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_4X4_1000" class="member-name-link">DICT_4X4_1000</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_4X4_250" class="member-name-link">DICT_4X4_250</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_4X4_50" class="member-name-link">DICT_4X4_50</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_5X5_100" class="member-name-link">DICT_5X5_100</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_5X5_1000" class="member-name-link">DICT_5X5_1000</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_5X5_250" class="member-name-link">DICT_5X5_250</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_5X5_50" class="member-name-link">DICT_5X5_50</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_6X6_100" class="member-name-link">DICT_6X6_100</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_6X6_1000" class="member-name-link">DICT_6X6_1000</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_6X6_250" class="member-name-link">DICT_6X6_250</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_6X6_50" class="member-name-link">DICT_6X6_50</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_7X7_100" class="member-name-link">DICT_7X7_100</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_7X7_1000" class="member-name-link">DICT_7X7_1000</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_7X7_250" class="member-name-link">DICT_7X7_250</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_7X7_50" class="member-name-link">DICT_7X7_50</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_APRILTAG_16h5" class="member-name-link">DICT_APRILTAG_16h5</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_APRILTAG_25h9" class="member-name-link">DICT_APRILTAG_25h9</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_APRILTAG_36h10" class="member-name-link">DICT_APRILTAG_36h10</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_APRILTAG_36h11" class="member-name-link">DICT_APRILTAG_36h11</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DICT_ARUCO_MIP_36h12" class="member-name-link">DICT_ARUCO_MIP_36h12</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DICT_ARUCO_ORIGINAL" class="member-name-link">DICT_ARUCO_ORIGINAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Objdetect</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">drawDetectedCornersCharuco</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws a set of Charuco corners</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">drawDetectedCornersCharuco</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws a set of Charuco corners</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawDetectedCornersCharuco</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;cornerColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draws a set of Charuco corners</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedDiamonds(org.opencv.core.Mat,java.util.List)" class="member-name-link">drawDetectedDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">drawDetectedDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawDetectedDiamonds</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedMarkers(org.opencv.core.Mat,java.util.List)" class="member-name-link">drawDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw detected markers in image</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">drawDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw detected markers in image</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#drawDetectedMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)" class="member-name-link">drawDetectedMarkers</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Draw detected markers in image</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#extendDictionary(int,int)" class="member-name-link">extendDictionary</a><wbr>(int&nbsp;nMarkers,
 int&nbsp;markerSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Extend base dictionary by new nMarkers</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#extendDictionary(int,int,org.opencv.objdetect.Dictionary)" class="member-name-link">extendDictionary</a><wbr>(int&nbsp;nMarkers,
 int&nbsp;markerSize,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Extend base dictionary by new nMarkers</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#extendDictionary(int,int,org.opencv.objdetect.Dictionary,int)" class="member-name-link">extendDictionary</a><wbr>(int&nbsp;nMarkers,
 int&nbsp;markerSize,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary,
 int&nbsp;randomSeed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Extend base dictionary by new nMarkers</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#generateImageMarker(org.opencv.objdetect.Dictionary,int,int,org.opencv.core.Mat)" class="member-name-link">generateImageMarker</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Generate a canonical marker image</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#generateImageMarker(org.opencv.objdetect.Dictionary,int,int,org.opencv.core.Mat,int)" class="member-name-link">generateImageMarker</a><wbr>(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;borderBits)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Generate a canonical marker image</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getPredefinedDictionary(int)" class="member-name-link">getPredefinedDictionary</a><wbr>(int&nbsp;dict)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns one of the predefined dictionaries referenced by DICT_*.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#groupRectangles(org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,int)" class="member-name-link">groupRectangles</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
 int&nbsp;groupThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#groupRectangles(org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,int,double)" class="member-name-link">groupRectangles</a><wbr>(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
 int&nbsp;groupThreshold,
 double&nbsp;eps)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="CASCADE_DO_CANNY_PRUNING">
<h3>CASCADE_DO_CANNY_PRUNING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CASCADE_DO_CANNY_PRUNING</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_DO_CANNY_PRUNING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CASCADE_SCALE_IMAGE">
<h3>CASCADE_SCALE_IMAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CASCADE_SCALE_IMAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_SCALE_IMAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CASCADE_FIND_BIGGEST_OBJECT">
<h3>CASCADE_FIND_BIGGEST_OBJECT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CASCADE_FIND_BIGGEST_OBJECT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_FIND_BIGGEST_OBJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CASCADE_DO_ROUGH_SEARCH">
<h3>CASCADE_DO_ROUGH_SEARCH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CASCADE_DO_ROUGH_SEARCH</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_DO_ROUGH_SEARCH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">
<h3>DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DetectionBasedTracker_DETECTED">
<h3>DetectionBasedTracker_DETECTED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DetectionBasedTracker_DETECTED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DetectionBasedTracker_DETECTED_TEMPORARY_LOST">
<h3>DetectionBasedTracker_DETECTED_TEMPORARY_LOST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DetectionBasedTracker_DETECTED_TEMPORARY_LOST</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_TEMPORARY_LOST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DetectionBasedTracker_WRONG_OBJECT">
<h3>DetectionBasedTracker_WRONG_OBJECT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DetectionBasedTracker_WRONG_OBJECT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_WRONG_OBJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CORNER_REFINE_NONE">
<h3>CORNER_REFINE_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CORNER_REFINE_NONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CORNER_REFINE_SUBPIX">
<h3>CORNER_REFINE_SUBPIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CORNER_REFINE_SUBPIX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_SUBPIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CORNER_REFINE_CONTOUR">
<h3>CORNER_REFINE_CONTOUR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CORNER_REFINE_CONTOUR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_CONTOUR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CORNER_REFINE_APRILTAG">
<h3>CORNER_REFINE_APRILTAG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CORNER_REFINE_APRILTAG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_APRILTAG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_4X4_50">
<h3>DICT_4X4_50</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_4X4_50</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_50">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_4X4_100">
<h3>DICT_4X4_100</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_4X4_100</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_100">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_4X4_250">
<h3>DICT_4X4_250</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_4X4_250</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_250">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_4X4_1000">
<h3>DICT_4X4_1000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_4X4_1000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_1000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_5X5_50">
<h3>DICT_5X5_50</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_5X5_50</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_50">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_5X5_100">
<h3>DICT_5X5_100</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_5X5_100</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_100">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_5X5_250">
<h3>DICT_5X5_250</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_5X5_250</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_250">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_5X5_1000">
<h3>DICT_5X5_1000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_5X5_1000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_1000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_6X6_50">
<h3>DICT_6X6_50</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_6X6_50</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_50">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_6X6_100">
<h3>DICT_6X6_100</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_6X6_100</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_100">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_6X6_250">
<h3>DICT_6X6_250</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_6X6_250</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_250">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_6X6_1000">
<h3>DICT_6X6_1000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_6X6_1000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_1000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_7X7_50">
<h3>DICT_7X7_50</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_7X7_50</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_50">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_7X7_100">
<h3>DICT_7X7_100</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_7X7_100</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_100">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_7X7_250">
<h3>DICT_7X7_250</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_7X7_250</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_250">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_7X7_1000">
<h3>DICT_7X7_1000</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_7X7_1000</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_1000">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_ARUCO_ORIGINAL">
<h3>DICT_ARUCO_ORIGINAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_ARUCO_ORIGINAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_ARUCO_ORIGINAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_APRILTAG_16h5">
<h3>DICT_APRILTAG_16h5</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_APRILTAG_16h5</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_16h5">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_APRILTAG_25h9">
<h3>DICT_APRILTAG_25h9</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_APRILTAG_25h9</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_25h9">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_APRILTAG_36h10">
<h3>DICT_APRILTAG_36h10</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_APRILTAG_36h10</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h10">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_APRILTAG_36h11">
<h3>DICT_APRILTAG_36h11</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_APRILTAG_36h11</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DICT_ARUCO_MIP_36h12">
<h3>DICT_ARUCO_MIP_36h12</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DICT_ARUCO_MIP_36h12</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_ARUCO_MIP_36h12">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Objdetect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Objdetect</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="groupRectangles(org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,int,double)">
<h3>groupRectangles</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">groupRectangles</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
 int&nbsp;groupThreshold,
 double&nbsp;eps)</span></div>
</section>
</li>
<li>
<section class="detail" id="groupRectangles(org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,int)">
<h3>groupRectangles</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">groupRectangles</span><wbr><span class="parameters">(<a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
 int&nbsp;groupThreshold)</span></div>
</section>
</li>
<li>
<section class="detail" id="drawDetectedMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</span></div>
<div class="block">Draw detected markers in image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>ids</code> - vector of identifiers for markers in markersCorners .
 Optional, if not provided, ids are not painted.</dd>
<dd><code>borderColor</code> - color of marker borders. Rest of colors (text color and first corner color)
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedMarkers(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>drawDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</span></div>
<div class="block">Draw detected markers in image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>ids</code> - vector of identifiers for markers in markersCorners .
 Optional, if not provided, ids are not painted.
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedMarkers(org.opencv.core.Mat,java.util.List)">
<h3>drawDetectedMarkers</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedMarkers</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners)</span></div>
<div class="block">Draw detected markers in image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.
 Optional, if not provided, ids are not painted.
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImageMarker(org.opencv.objdetect.Dictionary,int,int,org.opencv.core.Mat,int)">
<h3>generateImageMarker</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImageMarker</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;borderBits)</span></div>
<div class="block">Generate a canonical marker image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
<dd><code>id</code> - identifier of the marker that will be returned. It has to be a valid id in the specified dictionary.</dd>
<dd><code>sidePixels</code> - size of the image in pixels</dd>
<dd><code>img</code> - output image with the marker</dd>
<dd><code>borderBits</code> - width of the marker border.

 This function returns a marker image in its canonical form (i.e. ready to be printed)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImageMarker(org.opencv.objdetect.Dictionary,int,int,org.opencv.core.Mat)">
<h3>generateImageMarker</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImageMarker</span><wbr><span class="parameters">(<a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 int&nbsp;id,
 int&nbsp;sidePixels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</span></div>
<div class="block">Generate a canonical marker image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
<dd><code>id</code> - identifier of the marker that will be returned. It has to be a valid id in the specified dictionary.</dd>
<dd><code>sidePixels</code> - size of the image in pixels</dd>
<dd><code>img</code> - output image with the marker

 This function returns a marker image in its canonical form (i.e. ready to be printed)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawDetectedCornersCharuco</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedCornersCharuco</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;cornerColor)</span></div>
<div class="block">Draws a set of Charuco corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners</dd>
<dd><code>charucoIds</code> - list of identifiers for each corner in charucoCorners</dd>
<dd><code>cornerColor</code> - color of the square surrounding each corner

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>drawDetectedCornersCharuco</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedCornersCharuco</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</span></div>
<div class="block">Draws a set of Charuco corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners</dd>
<dd><code>charucoIds</code> - list of identifiers for each corner in charucoCorners

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedCornersCharuco(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>drawDetectedCornersCharuco</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedCornersCharuco</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners)</span></div>
<div class="block">Draws a set of Charuco corners</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,org.opencv.core.Scalar)">
<h3>drawDetectedDiamonds</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
 <a href="../core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</span></div>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>diamondIds</code> - vector of identifiers for diamonds in diamondCorners, in the same format
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.</dd>
<dd><code>borderColor</code> - color of marker borders. Rest of colors (text color and first corner color)
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedDiamonds(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>drawDetectedDiamonds</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</span></div>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>diamondIds</code> - vector of identifiers for diamonds in diamondCorners, in the same format
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="drawDetectedDiamonds(org.opencv.core.Mat,java.util.List)">
<h3>drawDetectedDiamonds</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">drawDetectedDiamonds</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners)</span></div>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPredefinedDictionary(int)">
<h3>getPredefinedDictionary</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">getPredefinedDictionary</span><wbr><span class="parameters">(int&nbsp;dict)</span></div>
<div class="block">Returns one of the predefined dictionaries referenced by DICT_*.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dict</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extendDictionary(int,int,org.opencv.objdetect.Dictionary,int)">
<h3>extendDictionary</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">extendDictionary</span><wbr><span class="parameters">(int&nbsp;nMarkers,
 int&nbsp;markerSize,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary,
 int&nbsp;randomSeed)</span></div>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers</dd>
<dd><code>baseDictionary</code> - Include the markers in this dictionary at the beginning (optional)</dd>
<dd><code>randomSeed</code> - a user supplied seed for theRNG()

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extendDictionary(int,int,org.opencv.objdetect.Dictionary)">
<h3>extendDictionary</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">extendDictionary</span><wbr><span class="parameters">(int&nbsp;nMarkers,
 int&nbsp;markerSize,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary)</span></div>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers</dd>
<dd><code>baseDictionary</code> - Include the markers in this dictionary at the beginning (optional)

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extendDictionary(int,int)">
<h3>extendDictionary</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">extendDictionary</span><wbr><span class="parameters">(int&nbsp;nMarkers,
 int&nbsp;markerSize)</span></div>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
