<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>HOGDescriptor (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: HOGDescriptor">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class HOGDescriptor" class="title">Class HOGDescriptor</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.HOGDescriptor</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">HOGDescriptor</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Implementation of HOG (Histogram of Oriented Gradients) descriptor and object detector.

 the HOG descriptor algorithm introduced by Navneet Dalal and Bill Triggs CITE: Dalal2005 .

 useful links:

 https://hal.inria.fr/inria-00548512/document/

 https://en.wikipedia.org/wiki/Histogram_of_oriented_gradients

 https://software.intel.com/en-us/ipp-dev-reference-histogram-of-oriented-gradients-hog-descriptor

 http://www.learnopencv.com/histogram-of-oriented-gradients

 http://www.learnopencv.com/handwritten-digits-classification-an-opencv-c-python-tutorial</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_NLEVELS" class="member-name-link">DEFAULT_NLEVELS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DESCR_FORMAT_COL_BY_COL" class="member-name-link">DESCR_FORMAT_COL_BY_COL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DESCR_FORMAT_ROW_BY_ROW" class="member-name-link">DESCR_FORMAT_ROW_BY_ROW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#L2Hys" class="member-name-link">L2Hys</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">HOGDescriptor</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates the HOG descriptor and detector with default parameters.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates the HOG descriptor and detector and loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean,int)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection,
 int&nbsp;_nlevels)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean,int,boolean)" class="member-name-link">HOGDescriptor</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection,
 int&nbsp;_nlevels,
 boolean&nbsp;_signedGradient)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkDetectorSize()" class="member-name-link">checkDetectorSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks if detector size equal to descriptor size.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat)" class="member-name-link">compute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes HOG descriptors of given image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size)" class="member-name-link">compute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes HOG descriptors of given image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">compute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes HOG descriptors of given image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.MatOfPoint)" class="member-name-link">compute</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;locations)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes HOG descriptors of given image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">computeGradient</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes gradients and quantized gradient orientations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Size)" class="member-name-link">computeGradient</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes gradients and quantized gradient orientations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">computeGradient</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingBR)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Computes gradients and quantized gradient orientations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs object detection without a multi-scale window.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs object detection without a multi-scale window.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs object detection without a multi-scale window.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs object detection without a multi-scale window.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.MatOfPoint)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;searchLocations)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs object detection without a multi-scale window.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double,double)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale,
 double&nbsp;groupThreshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double,double,boolean)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale,
 double&nbsp;groupThreshold,
 boolean&nbsp;useMeanshiftGrouping)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_blockSize()" class="member-name-link">get_blockSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_blockStride()" class="member-name-link">get_blockStride</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_cellSize()" class="member-name-link">get_cellSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_derivAperture()" class="member-name-link">get_derivAperture</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_gammaCorrection()" class="member-name-link">get_gammaCorrection</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_histogramNormType()" class="member-name-link">get_histogramNormType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_L2HysThreshold()" class="member-name-link">get_L2HysThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_nbins()" class="member-name-link">get_nbins</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_nlevels()" class="member-name-link">get_nlevels</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_signedGradient()" class="member-name-link">get_signedGradient</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_svmDetector()" class="member-name-link">get_svmDetector</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_winSigma()" class="member-name-link">get_winSigma</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_winSize()" class="member-name-link">get_winSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDaimlerPeopleDetector()" class="member-name-link">getDaimlerPeopleDetector</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns coefficients of the classifier trained for people detection (for 48x96 windows).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDefaultPeopleDetector()" class="member-name-link">getDefaultPeopleDetector</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns coefficients of the classifier trained for people detection (for 64x128 windows).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDescriptorSize()" class="member-name-link">getDescriptorSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the number of coefficients required for the classification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWinSigma()" class="member-name-link">getWinSigma</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns winSigma value</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#load(java.lang.String,java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#save(java.lang.String)" class="member-name-link">save</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#save(java.lang.String,java.lang.String)" class="member-name-link">save</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSVMDetector(org.opencv.core.Mat)" class="member-name-link">setSVMDetector</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svmdetector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets coefficients for the linear SVM classifier.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_NLEVELS">
<h3>DEFAULT_NLEVELS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_NLEVELS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DEFAULT_NLEVELS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DESCR_FORMAT_COL_BY_COL">
<h3>DESCR_FORMAT_COL_BY_COL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DESCR_FORMAT_COL_BY_COL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_COL_BY_COL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DESCR_FORMAT_ROW_BY_ROW">
<h3>DESCR_FORMAT_ROW_BY_ROW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DESCR_FORMAT_ROW_BY_ROW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_ROW_BY_ROW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="L2Hys">
<h3>L2Hys</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">L2Hys</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.L2Hys">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span>()</div>
<div class="block">Creates the HOG descriptor and detector with default parameters.

     aqual to HOGDescriptor(Size(64,128), Size(16,16), Size(8,8), Size(8,8), 9 )</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean,int,boolean)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection,
 int&nbsp;_nlevels,
 boolean&nbsp;_signedGradient)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
<dd><code>_nlevels</code> - sets nlevels with given value.</dd>
<dd><code>_signedGradient</code> - sets signedGradient with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean,int)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection,
 int&nbsp;_nlevels)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
<dd><code>_nlevels</code> - sets nlevels with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double,boolean)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold,
 boolean&nbsp;_gammaCorrection)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int,double)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType,
 double&nbsp;_L2HysThreshold)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double,int)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma,
 int&nbsp;_histogramNormType)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int,double)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture,
 double&nbsp;_winSigma)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int,int)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins,
 int&nbsp;_derivAperture)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.Size,int)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
 int&nbsp;_nbins)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>HOGDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">HOGDescriptor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Creates the HOG descriptor and detector and loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The file name containing HOGDescriptor properties and coefficients for the linear SVM classifier.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDescriptorSize()">
<h3>getDescriptorSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getDescriptorSize</span>()</div>
<div class="block">Returns the number of coefficients required for the classification.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkDetectorSize()">
<h3>checkDetectorSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">checkDetectorSize</span>()</div>
<div class="block">Checks if detector size equal to descriptor size.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWinSigma()">
<h3>getWinSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getWinSigma</span>()</div>
<div class="block">Returns winSigma value</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSVMDetector(org.opencv.core.Mat)">
<h3>setSVMDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSVMDetector</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svmdetector)</span></div>
<div class="block">Sets coefficients for the linear SVM classifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>svmdetector</code> - coefficients for the linear SVM classifier.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String,java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objname)</span></div>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file to read.</dd>
<dd><code>objname</code> - The optional name of the node to read (if empty, the first top-level node will be used).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file to read.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="save(java.lang.String,java.lang.String)">
<h3>save</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">save</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objname)</span></div>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - File name</dd>
<dd><code>objname</code> - Object name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="save(java.lang.String)">
<h3>save</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">save</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - File name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.MatOfPoint)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;locations)</span></div>
<div class="block">Computes HOG descriptors of given image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>locations</code> - Vector of Point</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size,org.opencv.core.Size)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</span></div>
<div class="block">Computes HOG descriptors of given image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat,org.opencv.core.Size)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</span></div>
<div class="block">Computes HOG descriptors of given image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compute(org.opencv.core.Mat,org.opencv.core.MatOfFloat)">
<h3>compute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compute</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors)</span></div>
<div class="block">Computes HOG descriptors of given image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,org.opencv.core.MatOfPoint)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;searchLocations)</span></div>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>searchLocations</code> - Vector of Point includes set of requested locations to be evaluated.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</span></div>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double,org.opencv.core.Size)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</span></div>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble,double)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
 double&nbsp;hitThreshold)</span></div>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.MatOfPoint,org.opencv.core.MatOfDouble)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights)</span></div>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double,double,boolean)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale,
 double&nbsp;groupThreshold,
 boolean&nbsp;useMeanshiftGrouping)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.</dd>
<dd><code>groupThreshold</code> - Coefficient to regulate the similarity threshold. When detected, some objects can be covered
     by many rectangles. 0 means not to perform grouping.</dd>
<dd><code>useMeanshiftGrouping</code> - indicates grouping algorithm</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double,double)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale,
 double&nbsp;groupThreshold)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.</dd>
<dd><code>groupThreshold</code> - Coefficient to regulate the similarity threshold. When detected, some objects can be covered
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size,double)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
 double&nbsp;scale)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size,org.opencv.core.Size)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double,org.opencv.core.Size)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble,double)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
 double&nbsp;hitThreshold)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfDouble)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Size,org.opencv.core.Size)">
<h3>computeGradient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">computeGradient</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingBR)</span></div>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
<dd><code>paddingTL</code> - Padding from top-left</dd>
<dd><code>paddingBR</code> - Padding from bottom-right</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Size)">
<h3>computeGradient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">computeGradient</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL)</span></div>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
<dd><code>paddingTL</code> - Padding from top-left</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeGradient(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>computeGradient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">computeGradient</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs)</span></div>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultPeopleDetector()">
<h3>getDefaultPeopleDetector</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></span>&nbsp;<span class="element-name">getDefaultPeopleDetector</span>()</div>
<div class="block">Returns coefficients of the classifier trained for people detection (for 64x128 windows).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDaimlerPeopleDetector()">
<h3>getDaimlerPeopleDetector</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></span>&nbsp;<span class="element-name">getDaimlerPeopleDetector</span>()</div>
<div class="block">Returns coefficients of the classifier trained for people detection (for 48x96 windows).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="get_winSize()">
<h3>get_winSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">get_winSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_blockSize()">
<h3>get_blockSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">get_blockSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_blockStride()">
<h3>get_blockStride</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">get_blockStride</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_cellSize()">
<h3>get_cellSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">get_cellSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_nbins()">
<h3>get_nbins</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_nbins</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_derivAperture()">
<h3>get_derivAperture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_derivAperture</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_winSigma()">
<h3>get_winSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_winSigma</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_histogramNormType()">
<h3>get_histogramNormType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_histogramNormType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_L2HysThreshold()">
<h3>get_L2HysThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_L2HysThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_gammaCorrection()">
<h3>get_gammaCorrection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">get_gammaCorrection</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_svmDetector()">
<h3>get_svmDetector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></span>&nbsp;<span class="element-name">get_svmDetector</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_nlevels()">
<h3>get_nlevels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_nlevels</span>()</div>
</section>
</li>
<li>
<section class="detail" id="get_signedGradient()">
<h3>get_signedGradient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">get_signedGradient</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
