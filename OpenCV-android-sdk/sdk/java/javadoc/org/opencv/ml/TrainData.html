<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>TrainData (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: TrainData">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class TrainData" class="title">Class TrainData</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.ml.TrainData</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TrainData</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Class encapsulating training data.

 Please note that the class only specifies the interface of training data, but not implementation.
 All the statistical model classes in _ml_ module accepts Ptr&lt;TrainData&gt; as parameter. In other
 words, you can create your own class derived from TrainData and pass smart pointer to the instance
 of this class into StatModel::train.

 SEE: REF: ml_intro_data</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Mat,int,org.opencv.core.Mat)" class="member-name-link">create</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates training data from in-memory arrays.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">create</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates training data from in-memory arrays.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">create</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates training data from in-memory arrays.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">create</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates training data from in-memory arrays.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">create</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates training data from in-memory arrays.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCatCount(int)" class="member-name-link">getCatCount</a><wbr>(int&nbsp;vi)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCatMap()" class="member-name-link">getCatMap</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCatOfs()" class="member-name-link">getCatOfs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLabels()" class="member-name-link">getClassLabels</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the vector of class labels

     The function returns vector of unique labels occurred in the responses.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultSubstValues()" class="member-name-link">getDefaultSubstValues</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayout()" class="member-name-link">getLayout</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMissing()" class="member-name-link">getMissing</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNAllVars()" class="member-name-link">getNAllVars</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNames(java.util.List)" class="member-name-link">getNames</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;names)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns vector of symbolic names captured in loadFromCSV()</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNormCatResponses()" class="member-name-link">getNormCatResponses</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNSamples()" class="member-name-link">getNSamples</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNTestSamples()" class="member-name-link">getNTestSamples</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNTrainSamples()" class="member-name-link">getNTrainSamples</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNVars()" class="member-name-link">getNVars</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResponses()" class="member-name-link">getResponses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResponseType()" class="member-name-link">getResponseType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSample(org.opencv.core.Mat,int,float)" class="member-name-link">getSample</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 int&nbsp;sidx,
 float&nbsp;buf)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSamples()" class="member-name-link">getSamples</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSampleWeights()" class="member-name-link">getSampleWeights</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getSubMatrix(org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">getSubMatrix</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;matrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx,
 int&nbsp;layout)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Extract from matrix rows/cols specified by passed indexes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getSubVector(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">getSubVector</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vec,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Extract from 1D vector elements specified by passed indexes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestNormCatResponses()" class="member-name-link">getTestNormCatResponses</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestResponses()" class="member-name-link">getTestResponses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestSampleIdx()" class="member-name-link">getTestSampleIdx</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestSamples()" class="member-name-link">getTestSamples</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns matrix of test samples</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestSampleWeights()" class="member-name-link">getTestSampleWeights</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainNormCatResponses()" class="member-name-link">getTrainNormCatResponses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the vector of normalized categorical responses

     The function returns vector of responses.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainResponses()" class="member-name-link">getTrainResponses</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the vector of responses

     The function returns ordered or the original categorical responses.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSampleIdx()" class="member-name-link">getTrainSampleIdx</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSamples()" class="member-name-link">getTrainSamples</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns matrix of train samples

         transposed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSamples(int)" class="member-name-link">getTrainSamples</a><wbr>(int&nbsp;layout)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns matrix of train samples</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSamples(int,boolean)" class="member-name-link">getTrainSamples</a><wbr>(int&nbsp;layout,
 boolean&nbsp;compressSamples)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns matrix of train samples</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSamples(int,boolean,boolean)" class="member-name-link">getTrainSamples</a><wbr>(int&nbsp;layout,
 boolean&nbsp;compressSamples,
 boolean&nbsp;compressVars)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns matrix of train samples</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainSampleWeights()" class="member-name-link">getTrainSampleWeights</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getValues(int,org.opencv.core.Mat,float)" class="member-name-link">getValues</a><wbr>(int&nbsp;vi,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sidx,
 float&nbsp;values)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarIdx()" class="member-name-link">getVarIdx</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarSymbolFlags()" class="member-name-link">getVarSymbolFlags</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVarType()" class="member-name-link">getVarType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainTestSplit(int)" class="member-name-link">setTrainTestSplit</a><wbr>(int&nbsp;count)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainTestSplit(int,boolean)" class="member-name-link">setTrainTestSplit</a><wbr>(int&nbsp;count,
 boolean&nbsp;shuffle)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainTestSplitRatio(double)" class="member-name-link">setTrainTestSplitRatio</a><wbr>(double&nbsp;ratio)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainTestSplitRatio(double,boolean)" class="member-name-link">setTrainTestSplitRatio</a><wbr>(double&nbsp;ratio,
 boolean&nbsp;shuffle)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shuffleTrainTest()" class="member-name-link">shuffleTrainTest</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getLayout()">
<h3>getLayout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getLayout</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNTrainSamples()">
<h3>getNTrainSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNTrainSamples</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNTestSamples()">
<h3>getNTestSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNTestSamples</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNSamples()">
<h3>getNSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNSamples</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNVars()">
<h3>getNVars</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNVars</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNAllVars()">
<h3>getNAllVars</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNAllVars</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getSample(org.opencv.core.Mat,int,float)">
<h3>getSample</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getSample</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 int&nbsp;sidx,
 float&nbsp;buf)</span></div>
</section>
</li>
<li>
<section class="detail" id="getSamples()">
<h3>getSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getSamples</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getMissing()">
<h3>getMissing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getMissing</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTrainSamples(int,boolean,boolean)">
<h3>getTrainSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSamples</span><wbr><span class="parameters">(int&nbsp;layout,
 boolean&nbsp;compressSamples,
 boolean&nbsp;compressVars)</span></div>
<div class="block">Returns matrix of train samples</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.</dd>
<dd><code>compressSamples</code> - if true, the function returns only the training samples (specified by
         sampleIdx)</dd>
<dd><code>compressVars</code> - if true, the function returns the shorter training samples, containing only
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainSamples(int,boolean)">
<h3>getTrainSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSamples</span><wbr><span class="parameters">(int&nbsp;layout,
 boolean&nbsp;compressSamples)</span></div>
<div class="block">Returns matrix of train samples</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.</dd>
<dd><code>compressSamples</code> - if true, the function returns only the training samples (specified by
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainSamples(int)">
<h3>getTrainSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSamples</span><wbr><span class="parameters">(int&nbsp;layout)</span></div>
<div class="block">Returns matrix of train samples</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainSamples()">
<h3>getTrainSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSamples</span>()</div>
<div class="block">Returns matrix of train samples

         transposed. See ml::SampleTypes.
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainResponses()">
<h3>getTrainResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainResponses</span>()</div>
<div class="block">Returns the vector of responses

     The function returns ordered or the original categorical responses. Usually it's used in
     regression algorithms.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainNormCatResponses()">
<h3>getTrainNormCatResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainNormCatResponses</span>()</div>
<div class="block">Returns the vector of normalized categorical responses

     The function returns vector of responses. Each response is integer from <code>0</code> to `&lt;number of
     classes&gt;-1`. The actual label value can be retrieved then from the class label vector, see
     TrainData::getClassLabels.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTestResponses()">
<h3>getTestResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTestResponses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTestNormCatResponses()">
<h3>getTestNormCatResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTestNormCatResponses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getResponses()">
<h3>getResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getResponses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getNormCatResponses()">
<h3>getNormCatResponses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getNormCatResponses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getSampleWeights()">
<h3>getSampleWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getSampleWeights</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTrainSampleWeights()">
<h3>getTrainSampleWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSampleWeights</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTestSampleWeights()">
<h3>getTestSampleWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTestSampleWeights</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getVarIdx()">
<h3>getVarIdx</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getVarIdx</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getVarType()">
<h3>getVarType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getVarType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getVarSymbolFlags()">
<h3>getVarSymbolFlags</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getVarSymbolFlags</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getResponseType()">
<h3>getResponseType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getResponseType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTrainSampleIdx()">
<h3>getTrainSampleIdx</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTrainSampleIdx</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTestSampleIdx()">
<h3>getTestSampleIdx</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTestSampleIdx</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getValues(int,org.opencv.core.Mat,float)">
<h3>getValues</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getValues</span><wbr><span class="parameters">(int&nbsp;vi,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sidx,
 float&nbsp;values)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDefaultSubstValues()">
<h3>getDefaultSubstValues</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getDefaultSubstValues</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCatCount(int)">
<h3>getCatCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getCatCount</span><wbr><span class="parameters">(int&nbsp;vi)</span></div>
</section>
</li>
<li>
<section class="detail" id="getClassLabels()">
<h3>getClassLabels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getClassLabels</span>()</div>
<div class="block">Returns the vector of class labels

     The function returns vector of unique labels occurred in the responses.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCatOfs()">
<h3>getCatOfs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getCatOfs</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCatMap()">
<h3>getCatMap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getCatMap</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setTrainTestSplit(int,boolean)">
<h3>setTrainTestSplit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainTestSplit</span><wbr><span class="parameters">(int&nbsp;count,
 boolean&nbsp;shuffle)</span></div>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>count</code> - automatically generated</dd>
<dd><code>shuffle</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrainTestSplit(int)">
<h3>setTrainTestSplit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainTestSplit</span><wbr><span class="parameters">(int&nbsp;count)</span></div>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>count</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrainTestSplitRatio(double,boolean)">
<h3>setTrainTestSplitRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainTestSplitRatio</span><wbr><span class="parameters">(double&nbsp;ratio,
 boolean&nbsp;shuffle)</span></div>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set. If the function is not called, all the data is used for training. Please, note that for
     each of TrainData::getTrain\* there is corresponding TrainData::getTest\*, so that the test
     subset can be retrieved and processed as well.
     SEE: TrainData::setTrainTestSplit</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ratio</code> - automatically generated</dd>
<dd><code>shuffle</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrainTestSplitRatio(double)">
<h3>setTrainTestSplitRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainTestSplitRatio</span><wbr><span class="parameters">(double&nbsp;ratio)</span></div>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set. If the function is not called, all the data is used for training. Please, note that for
     each of TrainData::getTrain\* there is corresponding TrainData::getTest\*, so that the test
     subset can be retrieved and processed as well.
     SEE: TrainData::setTrainTestSplit</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ratio</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shuffleTrainTest()">
<h3>shuffleTrainTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">shuffleTrainTest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTestSamples()">
<h3>getTestSamples</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getTestSamples</span>()</div>
<div class="block">Returns matrix of test samples</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNames(java.util.List)">
<h3>getNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getNames</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;names)</span></div>
<div class="block">Returns vector of symbolic names captured in loadFromCSV()</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>names</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSubVector(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>getSubVector</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getSubVector</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vec,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</span></div>
<div class="block">Extract from 1D vector elements specified by passed indexes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vec</code> - input vector (supported types: CV_32S, CV_32F, CV_64F)</dd>
<dd><code>idx</code> - 1D index vector</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSubMatrix(org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>getSubMatrix</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getSubMatrix</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;matrix,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx,
 int&nbsp;layout)</span></div>
<div class="block">Extract from matrix rows/cols specified by passed indexes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>matrix</code> - input matrix (supported types: CV_32S, CV_32F, CV_64F)</dd>
<dd><code>idx</code> - 1D index vector</dd>
<dd><code>layout</code> - specifies to extract rows (cv::ml::ROW_SAMPLES) or to extract columns (cv::ml::COL_SAMPLES)</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varType)</span></div>
<div class="block">Creates training data from in-memory arrays.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.</dd>
<dd><code>sampleWeights</code> - optional vector with weights for each sample. It should have CV_32F type.</dd>
<dd><code>varType</code> - optional vector of type CV_8U and size `&lt;number_of_variables_in_samples&gt; +
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights)</span></div>
<div class="block">Creates training data from in-memory arrays.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.</dd>
<dd><code>sampleWeights</code> - optional vector with weights for each sample. It should have CV_32F type.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx)</span></div>
<div class="block">Creates training data from in-memory arrays.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx)</span></div>
<div class="block">Creates training data from in-memory arrays.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(org.opencv.core.Mat,int,org.opencv.core.Mat)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TrainData.html" title="class in org.opencv.ml">TrainData</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</span></div>
<div class="block">Creates training data from in-memory arrays.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
