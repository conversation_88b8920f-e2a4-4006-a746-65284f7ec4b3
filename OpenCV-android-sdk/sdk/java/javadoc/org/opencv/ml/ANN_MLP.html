<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>ANN_MLP (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: ANN_MLP">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class ANN_MLP" class="title">Class ANN_MLP</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.ANN_MLP</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ANN_MLP</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">Artificial Neural Networks - Multi-Layer Perceptrons.

 Unlike many other models in ML that are constructed and trained at once, in the MLP model these
 steps are separated. First, a network with the specified topology is created using the non-default
 constructor or the method ANN_MLP::create. All the weights are set to zeros. Then, the network is
 trained using a set of input and output vectors. The training procedure can be repeated more than
 once, that is, the weights can be adjusted based on the new training data.

 Additional flags for StatModel::train are available: ANN_MLP::TrainFlags.

 SEE: REF: ml_intro_ann</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ANNEAL" class="member-name-link">ANNEAL</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#BACKPROP" class="member-name-link">BACKPROP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#GAUSSIAN" class="member-name-link">GAUSSIAN</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#IDENTITY" class="member-name-link">IDENTITY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LEAKYRELU" class="member-name-link">LEAKYRELU</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NO_INPUT_SCALE" class="member-name-link">NO_INPUT_SCALE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NO_OUTPUT_SCALE" class="member-name-link">NO_OUTPUT_SCALE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#RELU" class="member-name-link">RELU</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#RPROP" class="member-name-link">RPROP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SIGMOID_SYM" class="member-name-link">SIGMOID_SYM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#UPDATE_WEIGHTS" class="member-name-link">UPDATE_WEIGHTS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates empty model

     Use StatModel::train to train the model, Algorithm::load&lt;ANN_MLP&gt;(filename) to load the pre-trained model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAnnealCoolingRatio()" class="member-name-link">getAnnealCoolingRatio</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setAnnealCoolingRatio</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAnnealFinalT()" class="member-name-link">getAnnealFinalT</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setAnnealFinalT</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAnnealInitialT()" class="member-name-link">getAnnealInitialT</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setAnnealInitialT</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAnnealItePerStep()" class="member-name-link">getAnnealItePerStep</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setAnnealItePerStep</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBackpropMomentumScale()" class="member-name-link">getBackpropMomentumScale</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setBackpropMomentumScale</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBackpropWeightScale()" class="member-name-link">getBackpropWeightScale</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setBackpropWeightScale</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayerSizes()" class="member-name-link">getLayerSizes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRpropDW0()" class="member-name-link">getRpropDW0</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setRpropDW0</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRpropDWMax()" class="member-name-link">getRpropDWMax</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setRpropDWMax</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRpropDWMin()" class="member-name-link">getRpropDWMin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setRpropDWMin</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRpropDWMinus()" class="member-name-link">getRpropDWMinus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setRpropDWMinus</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRpropDWPlus()" class="member-name-link">getRpropDWPlus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setRpropDWPlus</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTermCriteria()" class="member-name-link">getTermCriteria</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setTermCriteria</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTrainMethod()" class="member-name-link">getTrainMethod</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns current training method</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWeights(int)" class="member-name-link">getWeights</a><wbr>(int&nbsp;layerIdx)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized ANN from a file

 Use ANN::save to serialize and store an ANN to disk.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setActivationFunction(int)" class="member-name-link">setActivationFunction</a><wbr>(int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize the activation function for each neuron.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setActivationFunction(int,double)" class="member-name-link">setActivationFunction</a><wbr>(int&nbsp;type,
 double&nbsp;param1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize the activation function for each neuron.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setActivationFunction(int,double,double)" class="member-name-link">setActivationFunction</a><wbr>(int&nbsp;type,
 double&nbsp;param1,
 double&nbsp;param2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize the activation function for each neuron.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAnnealCoolingRatio(double)" class="member-name-link">setAnnealCoolingRatio</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getAnnealCoolingRatio SEE: getAnnealCoolingRatio</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAnnealFinalT(double)" class="member-name-link">setAnnealFinalT</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getAnnealFinalT SEE: getAnnealFinalT</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAnnealInitialT(double)" class="member-name-link">setAnnealInitialT</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getAnnealInitialT SEE: getAnnealInitialT</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAnnealItePerStep(int)" class="member-name-link">setAnnealItePerStep</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getAnnealItePerStep SEE: getAnnealItePerStep</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBackpropMomentumScale(double)" class="member-name-link">setBackpropMomentumScale</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getBackpropMomentumScale SEE: getBackpropMomentumScale</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBackpropWeightScale(double)" class="member-name-link">setBackpropWeightScale</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getBackpropWeightScale SEE: getBackpropWeightScale</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLayerSizes(org.opencv.core.Mat)" class="member-name-link">setLayerSizes</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRpropDW0(double)" class="member-name-link">setRpropDW0</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getRpropDW0 SEE: getRpropDW0</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRpropDWMax(double)" class="member-name-link">setRpropDWMax</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getRpropDWMax SEE: getRpropDWMax</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRpropDWMin(double)" class="member-name-link">setRpropDWMin</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getRpropDWMin SEE: getRpropDWMin</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRpropDWMinus(double)" class="member-name-link">setRpropDWMinus</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getRpropDWMinus SEE: getRpropDWMinus</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRpropDWPlus(double)" class="member-name-link">setRpropDWPlus</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getRpropDWPlus SEE: getRpropDWPlus</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTermCriteria(org.opencv.core.TermCriteria)" class="member-name-link">setTermCriteria</a><wbr>(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainMethod(int)" class="member-name-link">setTrainMethod</a><wbr>(int&nbsp;method)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets training method and common parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainMethod(int,double)" class="member-name-link">setTrainMethod</a><wbr>(int&nbsp;method,
 double&nbsp;param1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets training method and common parameters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrainMethod(int,double,double)" class="member-name-link">setTrainMethod</a><wbr>(int&nbsp;method,
 double&nbsp;param1,
 double&nbsp;param2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets training method and common parameters.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="IDENTITY">
<h3>IDENTITY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">IDENTITY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.IDENTITY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SIGMOID_SYM">
<h3>SIGMOID_SYM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SIGMOID_SYM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.SIGMOID_SYM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GAUSSIAN">
<h3>GAUSSIAN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GAUSSIAN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.GAUSSIAN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RELU">
<h3>RELU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">RELU</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.RELU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LEAKYRELU">
<h3>LEAKYRELU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LEAKYRELU</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.LEAKYRELU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="UPDATE_WEIGHTS">
<h3>UPDATE_WEIGHTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">UPDATE_WEIGHTS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.UPDATE_WEIGHTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NO_INPUT_SCALE">
<h3>NO_INPUT_SCALE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NO_INPUT_SCALE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_INPUT_SCALE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NO_OUTPUT_SCALE">
<h3>NO_OUTPUT_SCALE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NO_OUTPUT_SCALE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_OUTPUT_SCALE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BACKPROP">
<h3>BACKPROP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BACKPROP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.BACKPROP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RPROP">
<h3>RPROP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">RPROP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.RPROP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANNEAL">
<h3>ANNEAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ANNEAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.ANNEAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setTrainMethod(int,double,double)">
<h3>setTrainMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainMethod</span><wbr><span class="parameters">(int&nbsp;method,
 double&nbsp;param1,
 double&nbsp;param2)</span></div>
<div class="block">Sets training method and common parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
<dd><code>param1</code> - passed to setRpropDW0 for ANN_MLP::RPROP and to setBackpropWeightScale for ANN_MLP::BACKPROP and to initialT for ANN_MLP::ANNEAL.</dd>
<dd><code>param2</code> - passed to setRpropDWMin for ANN_MLP::RPROP and to setBackpropMomentumScale for ANN_MLP::BACKPROP and to finalT for ANN_MLP::ANNEAL.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrainMethod(int,double)">
<h3>setTrainMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainMethod</span><wbr><span class="parameters">(int&nbsp;method,
 double&nbsp;param1)</span></div>
<div class="block">Sets training method and common parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
<dd><code>param1</code> - passed to setRpropDW0 for ANN_MLP::RPROP and to setBackpropWeightScale for ANN_MLP::BACKPROP and to initialT for ANN_MLP::ANNEAL.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrainMethod(int)">
<h3>setTrainMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrainMethod</span><wbr><span class="parameters">(int&nbsp;method)</span></div>
<div class="block">Sets training method and common parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTrainMethod()">
<h3>getTrainMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getTrainMethod</span>()</div>
<div class="block">Returns current training method</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setActivationFunction(int,double,double)">
<h3>setActivationFunction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setActivationFunction</span><wbr><span class="parameters">(int&nbsp;type,
 double&nbsp;param1,
 double&nbsp;param2)</span></div>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
<dd><code>param1</code> - The first parameter of the activation function, \(\alpha\). Default value is 0.</dd>
<dd><code>param2</code> - The second parameter of the activation function, \(\beta\). Default value is 0.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setActivationFunction(int,double)">
<h3>setActivationFunction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setActivationFunction</span><wbr><span class="parameters">(int&nbsp;type,
 double&nbsp;param1)</span></div>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
<dd><code>param1</code> - The first parameter of the activation function, \(\alpha\). Default value is 0.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setActivationFunction(int)">
<h3>setActivationFunction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setActivationFunction</span><wbr><span class="parameters">(int&nbsp;type)</span></div>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLayerSizes(org.opencv.core.Mat)">
<h3>setLayerSizes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLayerSizes</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</span></div>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.
     The very first element specifies the number of elements in the input layer.
     The last element - number of elements in the output layer. Default value is empty Mat.
 SEE: getLayerSizes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>_layer_sizes</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLayerSizes()">
<h3>getLayerSizes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getLayerSizes</span>()</div>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.
     The very first element specifies the number of elements in the input layer.
     The last element - number of elements in the output layer.
 SEE: setLayerSizes</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTermCriteria()">
<h3>getTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></span>&nbsp;<span class="element-name">getTermCriteria</span>()</div>
<div class="block">SEE: setTermCriteria</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTermCriteria(org.opencv.core.TermCriteria)">
<h3>setTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTermCriteria</span><wbr><span class="parameters">(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</span></div>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBackpropWeightScale()">
<h3>getBackpropWeightScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getBackpropWeightScale</span>()</div>
<div class="block">SEE: setBackpropWeightScale</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBackpropWeightScale(double)">
<h3>setBackpropWeightScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBackpropWeightScale</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getBackpropWeightScale SEE: getBackpropWeightScale</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBackpropMomentumScale()">
<h3>getBackpropMomentumScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getBackpropMomentumScale</span>()</div>
<div class="block">SEE: setBackpropMomentumScale</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBackpropMomentumScale(double)">
<h3>setBackpropMomentumScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBackpropMomentumScale</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getBackpropMomentumScale SEE: getBackpropMomentumScale</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRpropDW0()">
<h3>getRpropDW0</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getRpropDW0</span>()</div>
<div class="block">SEE: setRpropDW0</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRpropDW0(double)">
<h3>setRpropDW0</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRpropDW0</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getRpropDW0 SEE: getRpropDW0</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRpropDWPlus()">
<h3>getRpropDWPlus</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getRpropDWPlus</span>()</div>
<div class="block">SEE: setRpropDWPlus</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRpropDWPlus(double)">
<h3>setRpropDWPlus</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRpropDWPlus</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getRpropDWPlus SEE: getRpropDWPlus</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRpropDWMinus()">
<h3>getRpropDWMinus</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getRpropDWMinus</span>()</div>
<div class="block">SEE: setRpropDWMinus</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRpropDWMinus(double)">
<h3>setRpropDWMinus</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRpropDWMinus</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getRpropDWMinus SEE: getRpropDWMinus</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRpropDWMin()">
<h3>getRpropDWMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getRpropDWMin</span>()</div>
<div class="block">SEE: setRpropDWMin</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRpropDWMin(double)">
<h3>setRpropDWMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRpropDWMin</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getRpropDWMin SEE: getRpropDWMin</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRpropDWMax()">
<h3>getRpropDWMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getRpropDWMax</span>()</div>
<div class="block">SEE: setRpropDWMax</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRpropDWMax(double)">
<h3>setRpropDWMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRpropDWMax</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getRpropDWMax SEE: getRpropDWMax</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAnnealInitialT()">
<h3>getAnnealInitialT</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getAnnealInitialT</span>()</div>
<div class="block">SEE: setAnnealInitialT</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAnnealInitialT(double)">
<h3>setAnnealInitialT</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAnnealInitialT</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getAnnealInitialT SEE: getAnnealInitialT</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAnnealFinalT()">
<h3>getAnnealFinalT</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getAnnealFinalT</span>()</div>
<div class="block">SEE: setAnnealFinalT</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAnnealFinalT(double)">
<h3>setAnnealFinalT</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAnnealFinalT</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getAnnealFinalT SEE: getAnnealFinalT</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAnnealCoolingRatio()">
<h3>getAnnealCoolingRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getAnnealCoolingRatio</span>()</div>
<div class="block">SEE: setAnnealCoolingRatio</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAnnealCoolingRatio(double)">
<h3>setAnnealCoolingRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAnnealCoolingRatio</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getAnnealCoolingRatio SEE: getAnnealCoolingRatio</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAnnealItePerStep()">
<h3>getAnnealItePerStep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getAnnealItePerStep</span>()</div>
<div class="block">SEE: setAnnealItePerStep</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAnnealItePerStep(int)">
<h3>setAnnealItePerStep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAnnealItePerStep</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getAnnealItePerStep SEE: getAnnealItePerStep</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWeights(int)">
<h3>getWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getWeights</span><wbr><span class="parameters">(int&nbsp;layerIdx)</span></div>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates empty model

     Use StatModel::train to train the model, Algorithm::load&lt;ANN_MLP&gt;(filename) to load the pre-trained model.
     Note that the train method has optional flags: ANN_MLP::TrainFlags.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized ANN from a file

 Use ANN::save to serialize and store an ANN to disk.
 Load the ANN from this file again, by calling this function with the path to the file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized ANN</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
