<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>SVM (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: SVM">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class SVM" class="title">Class SVM</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.SVM</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SVM</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">Support Vector Machines.

 SEE: REF: ml_intro_svm</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#C" class="member-name-link">C</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#C_SVC" class="member-name-link">C_SVC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CHI2" class="member-name-link">CHI2</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#COEF" class="member-name-link">COEF</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CUSTOM" class="member-name-link">CUSTOM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEGREE" class="member-name-link">DEGREE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#EPS_SVR" class="member-name-link">EPS_SVR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#GAMMA" class="member-name-link">GAMMA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#INTER" class="member-name-link">INTER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LINEAR" class="member-name-link">LINEAR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NU" class="member-name-link">NU</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NU_SVC" class="member-name-link">NU_SVC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NU_SVR" class="member-name-link">NU_SVR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#ONE_CLASS" class="member-name-link">ONE_CLASS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#P" class="member-name-link">P</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#POLY" class="member-name-link">POLY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#RBF" class="member-name-link">RBF</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SIGMOID" class="member-name-link">SIGMOID</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVM.html" title="class in org.opencv.ml">SVM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVM.html" title="class in org.opencv.ml">SVM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates empty model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getC()" class="member-name-link">getC</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setC</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassWeights()" class="member-name-link">getClassWeights</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setClassWeights</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCoef0()" class="member-name-link">getCoef0</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setCoef0</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDecisionFunction(int,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">getDecisionFunction</a><wbr>(int&nbsp;i,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;alpha,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svidx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves the decision function</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDefaultGridPtr(int)" class="member-name-link">getDefaultGridPtr</a><wbr>(int&nbsp;param_id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Generates a grid for %SVM parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDegree()" class="member-name-link">getDegree</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setDegree</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGamma()" class="member-name-link">getGamma</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setGamma</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getKernelType()" class="member-name-link">getKernelType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Type of a %SVM kernel.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNu()" class="member-name-link">getNu</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setNu</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getP()" class="member-name-link">getP</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setP</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSupportVectors()" class="member-name-link">getSupportVectors</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves all the support vectors

     The method returns all the support vectors as a floating-point matrix, where support vectors are
     stored as matrix rows.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTermCriteria()" class="member-name-link">getTermCriteria</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setTermCriteria</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getType()" class="member-name-link">getType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setType</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUncompressedSupportVectors()" class="member-name-link">getUncompressedSupportVectors</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves all the uncompressed support vectors of a linear %SVM

     The method returns all the uncompressed support vectors of a linear %SVM that the compressed
     support vector, used for prediction, was derived from.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVM.html" title="class in org.opencv.ml">SVM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized svm from a file

 Use SVM::save to serialize and store an SVM to disk.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setC(double)" class="member-name-link">setC</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getC SEE: getC</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassWeights(org.opencv.core.Mat)" class="member-name-link">setClassWeights</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getClassWeights SEE: getClassWeights</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCoef0(double)" class="member-name-link">setCoef0</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getCoef0 SEE: getCoef0</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDegree(double)" class="member-name-link">setDegree</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getDegree SEE: getDegree</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGamma(double)" class="member-name-link">setGamma</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getGamma SEE: getGamma</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKernel(int)" class="member-name-link">setKernel</a><wbr>(int&nbsp;kernelType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize with one of predefined kernels.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNu(double)" class="member-name-link">setNu</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getNu SEE: getNu</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setP(double)" class="member-name-link">setP</a><wbr>(double&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getP SEE: getP</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTermCriteria(org.opencv.core.TermCriteria)" class="member-name-link">setTermCriteria</a><wbr>(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setType(int)" class="member-name-link">setType</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getType SEE: getType</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,boolean)" class="member-name-link">trainAuto</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid,
 boolean&nbsp;balanced)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trains an %SVM with optimal parameters</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="CUSTOM">
<h3>CUSTOM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CUSTOM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.CUSTOM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LINEAR">
<h3>LINEAR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LINEAR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.LINEAR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="POLY">
<h3>POLY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">POLY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.POLY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RBF">
<h3>RBF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">RBF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.RBF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SIGMOID">
<h3>SIGMOID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SIGMOID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.SIGMOID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CHI2">
<h3>CHI2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CHI2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.CHI2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="INTER">
<h3>INTER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">INTER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.INTER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="C">
<h3>C</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">C</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.C">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GAMMA">
<h3>GAMMA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GAMMA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.GAMMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="P">
<h3>P</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">P</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.P">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NU">
<h3>NU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NU</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.NU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COEF">
<h3>COEF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">COEF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.COEF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEGREE">
<h3>DEGREE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEGREE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.DEGREE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="C_SVC">
<h3>C_SVC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">C_SVC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.C_SVC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NU_SVC">
<h3>NU_SVC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NU_SVC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.NU_SVC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_CLASS">
<h3>ONE_CLASS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ONE_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.ONE_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EPS_SVR">
<h3>EPS_SVR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EPS_SVR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.EPS_SVR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NU_SVR">
<h3>NU_SVR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NU_SVR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVM.NU_SVR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVM.html" title="class in org.opencv.ml">SVM</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getType()">
<h3>getType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getType</span>()</div>
<div class="block">SEE: setType</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setType(int)">
<h3>setType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setType</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getType SEE: getType</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getGamma()">
<h3>getGamma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getGamma</span>()</div>
<div class="block">SEE: setGamma</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGamma(double)">
<h3>setGamma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGamma</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getGamma SEE: getGamma</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCoef0()">
<h3>getCoef0</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getCoef0</span>()</div>
<div class="block">SEE: setCoef0</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCoef0(double)">
<h3>setCoef0</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCoef0</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getCoef0 SEE: getCoef0</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDegree()">
<h3>getDegree</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getDegree</span>()</div>
<div class="block">SEE: setDegree</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDegree(double)">
<h3>setDegree</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDegree</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getDegree SEE: getDegree</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getC()">
<h3>getC</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getC</span>()</div>
<div class="block">SEE: setC</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setC(double)">
<h3>setC</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setC</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getC SEE: getC</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNu()">
<h3>getNu</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getNu</span>()</div>
<div class="block">SEE: setNu</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNu(double)">
<h3>setNu</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNu</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getNu SEE: getNu</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getP()">
<h3>getP</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getP</span>()</div>
<div class="block">SEE: setP</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setP(double)">
<h3>setP</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setP</span><wbr><span class="parameters">(double&nbsp;val)</span></div>
<div class="block">getP SEE: getP</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassWeights()">
<h3>getClassWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getClassWeights</span>()</div>
<div class="block">SEE: setClassWeights</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassWeights(org.opencv.core.Mat)">
<h3>setClassWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassWeights</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</span></div>
<div class="block">getClassWeights SEE: getClassWeights</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTermCriteria()">
<h3>getTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></span>&nbsp;<span class="element-name">getTermCriteria</span>()</div>
<div class="block">SEE: setTermCriteria</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTermCriteria(org.opencv.core.TermCriteria)">
<h3>setTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTermCriteria</span><wbr><span class="parameters">(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</span></div>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getKernelType()">
<h3>getKernelType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getKernelType</span>()</div>
<div class="block">Type of a %SVM kernel.
 See SVM::KernelTypes. Default value is SVM::RBF.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKernel(int)">
<h3>setKernel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKernel</span><wbr><span class="parameters">(int&nbsp;kernelType)</span></div>
<div class="block">Initialize with one of predefined kernels.
 See SVM::KernelTypes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>kernelType</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,boolean)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid,
 boolean&nbsp;balanced)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff</dd>
<dd><code>degreeGrid</code> - grid for degree</dd>
<dd><code>balanced</code> - If true and the problem is 2-class classification then the method creates more
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff</dd>
<dd><code>degreeGrid</code> - grid for degree
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int,org.opencv.ml.ParamGrid)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold,
 <a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat,int)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
 int&nbsp;kFold)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainAuto(org.opencv.core.Mat,int,org.opencv.core.Mat)">
<h3>trainAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainAuto</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;layout,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</span></div>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSupportVectors()">
<h3>getSupportVectors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getSupportVectors</span>()</div>
<div class="block">Retrieves all the support vectors

     The method returns all the support vectors as a floating-point matrix, where support vectors are
     stored as matrix rows.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUncompressedSupportVectors()">
<h3>getUncompressedSupportVectors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getUncompressedSupportVectors</span>()</div>
<div class="block">Retrieves all the uncompressed support vectors of a linear %SVM

     The method returns all the uncompressed support vectors of a linear %SVM that the compressed
     support vector, used for prediction, was derived from. They are returned in a floating-point
     matrix, where the support vectors are stored as matrix rows.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDecisionFunction(int,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>getDecisionFunction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">getDecisionFunction</span><wbr><span class="parameters">(int&nbsp;i,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;alpha,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svidx)</span></div>
<div class="block">Retrieves the decision function</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>i</code> - the index of the decision function. If the problem solved is regression, 1-class or
         2-class classification, then there will be just one decision function and the index should
         always be 0. Otherwise, in the case of N-class classification, there will be \(N(N-1)/2\)
         decision functions.</dd>
<dd><code>alpha</code> - the optional output vector for weights, corresponding to different support vectors.
         In the case of linear %SVM all the alpha's will be 1's.</dd>
<dd><code>svidx</code> - the optional output vector of indices of support vectors within the matrix of
         support vectors (which can be retrieved by SVM::getSupportVectors). In the case of linear
         %SVM each decision function consists of a single "compressed" support vector.

     The method returns rho parameter of the decision function, a scalar subtracted from the weighted
     sum of kernel responses.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultGridPtr(int)">
<h3>getDefaultGridPtr</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a></span>&nbsp;<span class="element-name">getDefaultGridPtr</span><wbr><span class="parameters">(int&nbsp;param_id)</span></div>
<div class="block">Generates a grid for %SVM parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>param_id</code> - %SVM parameters IDs that must be one of the SVM::ParamTypes. The grid is
     generated for the parameter with this ID.

     The function generates a grid pointer for the specified parameter of the %SVM algorithm.
     The grid may be passed to the function SVM::trainAuto.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVM.html" title="class in org.opencv.ml">SVM</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates empty model.
     Use StatModel::train to train the model. Since %SVM has several parameters, you may want to
 find the best parameters for your problem, it can be done with SVM::trainAuto.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVM.html" title="class in org.opencv.ml">SVM</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized svm from a file

 Use SVM::save to serialize and store an SVM to disk.
 Load the SVM from this file again, by calling this function with the path to the file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized svm</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
