<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>EM (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: EM">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class EM" class="title">Class EM</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.EM</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">EM</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">The class implements the Expectation Maximization algorithm.

 SEE: REF: ml_intro_em</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#COV_MAT_DEFAULT" class="member-name-link">COV_MAT_DEFAULT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#COV_MAT_DIAGONAL" class="member-name-link">COV_MAT_DIAGONAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#COV_MAT_GENERIC" class="member-name-link">COV_MAT_GENERIC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#COV_MAT_SPHERICAL" class="member-name-link">COV_MAT_SPHERICAL</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_MAX_ITERS" class="member-name-link">DEFAULT_MAX_ITERS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_NCLUSTERS" class="member-name-link">DEFAULT_NCLUSTERS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#START_AUTO_STEP" class="member-name-link">START_AUTO_STEP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#START_E_STEP" class="member-name-link">START_E_STEP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#START_M_STEP" class="member-name-link">START_M_STEP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="EM.html" title="class in org.opencv.ml">EM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="EM.html" title="class in org.opencv.ml">EM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates empty %EM model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClustersNumber()" class="member-name-link">getClustersNumber</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setClustersNumber</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCovarianceMatrixType()" class="member-name-link">getCovarianceMatrixType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setCovarianceMatrixType</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCovs(java.util.List)" class="member-name-link">getCovs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;covs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns covariation matrices

     Returns vector of covariation matrices.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMeans()" class="member-name-link">getMeans</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the cluster centers (means of the Gaussian mixture)

     Returns matrix with the number of rows equal to the number of mixtures and number of columns
     equal to the space dimensionality.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTermCriteria()" class="member-name-link">getTermCriteria</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setTermCriteria</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWeights()" class="member-name-link">getWeights</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns weights of the mixtures

     Returns vector with the number of elements equal to the number of mixtures.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="EM.html" title="class in org.opencv.ml">EM</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="EM.html" title="class in org.opencv.ml">EM</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String,java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predict(org.opencv.core.Mat)" class="member-name-link">predict</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns posterior probabilities for the provided samples</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predict(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">predict</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns posterior probabilities for the provided samples</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">predict</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns posterior probabilities for the provided samples</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predict2(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">predict2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sample,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a likelihood logarithm value and an index of the most probable mixture component
     for the given sample.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClustersNumber(int)" class="member-name-link">setClustersNumber</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getClustersNumber SEE: getClustersNumber</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCovarianceMatrixType(int)" class="member-name-link">setCovarianceMatrixType</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getCovarianceMatrixType SEE: getCovarianceMatrixType</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTermCriteria(org.opencv.core.TermCriteria)" class="member-name-link">setTermCriteria</a><wbr>(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainE</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainEM(org.opencv.core.Mat)" class="member-name-link">trainEM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainEM(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainEM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainEM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainEM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainEM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainEM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainM(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">trainM</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_NCLUSTERS">
<h3>DEFAULT_NCLUSTERS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_NCLUSTERS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.DEFAULT_NCLUSTERS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_MAX_ITERS">
<h3>DEFAULT_MAX_ITERS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_MAX_ITERS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.DEFAULT_MAX_ITERS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="START_E_STEP">
<h3>START_E_STEP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">START_E_STEP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.START_E_STEP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="START_M_STEP">
<h3>START_M_STEP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">START_M_STEP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.START_M_STEP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="START_AUTO_STEP">
<h3>START_AUTO_STEP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">START_AUTO_STEP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.START_AUTO_STEP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COV_MAT_SPHERICAL">
<h3>COV_MAT_SPHERICAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">COV_MAT_SPHERICAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_SPHERICAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COV_MAT_DIAGONAL">
<h3>COV_MAT_DIAGONAL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">COV_MAT_DIAGONAL</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_DIAGONAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COV_MAT_GENERIC">
<h3>COV_MAT_GENERIC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">COV_MAT_GENERIC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_GENERIC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COV_MAT_DEFAULT">
<h3>COV_MAT_DEFAULT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">COV_MAT_DEFAULT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_DEFAULT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="EM.html" title="class in org.opencv.ml">EM</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getClustersNumber()">
<h3>getClustersNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getClustersNumber</span>()</div>
<div class="block">SEE: setClustersNumber</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClustersNumber(int)">
<h3>setClustersNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClustersNumber</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getClustersNumber SEE: getClustersNumber</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCovarianceMatrixType()">
<h3>getCovarianceMatrixType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getCovarianceMatrixType</span>()</div>
<div class="block">SEE: setCovarianceMatrixType</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCovarianceMatrixType(int)">
<h3>setCovarianceMatrixType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCovarianceMatrixType</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getCovarianceMatrixType SEE: getCovarianceMatrixType</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTermCriteria()">
<h3>getTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></span>&nbsp;<span class="element-name">getTermCriteria</span>()</div>
<div class="block">SEE: setTermCriteria</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTermCriteria(org.opencv.core.TermCriteria)">
<h3>setTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTermCriteria</span><wbr><span class="parameters">(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</span></div>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWeights()">
<h3>getWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getWeights</span>()</div>
<div class="block">Returns weights of the mixtures

     Returns vector with the number of elements equal to the number of mixtures.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMeans()">
<h3>getMeans</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getMeans</span>()</div>
<div class="block">Returns the cluster centers (means of the Gaussian mixture)

     Returns matrix with the number of rows equal to the number of mixtures and number of columns
     equal to the space dimensionality.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCovs(java.util.List)">
<h3>getCovs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getCovs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;covs)</span></div>
<div class="block">Returns covariation matrices

     Returns vector of covariation matrices. Number of matrices is the number of gaussian mixtures,
     each matrix is a square floating-point matrix NxN, where N is the space dimensionality.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>covs</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>predict</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">predict</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 int&nbsp;flags)</span></div>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a></code>&nbsp;in class&nbsp;<code><a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt>Parameters:</dt>
<dd><code>samples</code> - The input samples, floating-point matrix</dd>
<dd><code>results</code> - The optional output \( nSamples \times nClusters\) matrix of results. It contains
     posterior probabilities for each sample from the input</dd>
<dd><code>flags</code> - This parameter will be ignored</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="predict(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>predict</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">predict</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</span></div>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a></code>&nbsp;in class&nbsp;<code><a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt>Parameters:</dt>
<dd><code>samples</code> - The input samples, floating-point matrix</dd>
<dd><code>results</code> - The optional output \( nSamples \times nClusters\) matrix of results. It contains
     posterior probabilities for each sample from the input</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="predict(org.opencv.core.Mat)">
<h3>predict</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">predict</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</span></div>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a></code>&nbsp;in class&nbsp;<code><a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt>Parameters:</dt>
<dd><code>samples</code> - The input samples, floating-point matrix
     posterior probabilities for each sample from the input</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="predict2(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>predict2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double[]</span>&nbsp;<span class="element-name">predict2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sample,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</span></div>
<div class="block">Returns a likelihood logarithm value and an index of the most probable mixture component
     for the given sample.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sample</code> - A sample for classification. It should be a one-channel matrix of
         \(1 \times dims\) or \(dims \times 1\) size.</dd>
<dd><code>probs</code> - Optional output matrix that contains posterior probabilities of each component
         given the sample. It has \(1 \times nclusters\) size and CV_64FC1 type.

     The method returns a two-element double vector. Zero element is a likelihood logarithm value for
     the sample. First element is an index of the most probable mixture component for the given
     sample.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainEM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainEM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainEM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainEM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainEM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainEM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainEM(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainEM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainEM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainEM(org.opencv.core.Mat)">
<h3>trainEM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainEM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainE(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainE</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainE</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainM(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trainM(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>trainM</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">trainM</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0)</span></div>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="EM.html" title="class in org.opencv.ml">EM</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates empty %EM model.
     The model should be trained then using StatModel::train(traindata, flags) method. Alternatively, you
     can use one of the EM::train\* methods or load it from file using Algorithm::load&lt;EM&gt;(filename).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String,java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="EM.html" title="class in org.opencv.ml">EM</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</span></div>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.
 Load the EM from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized EM</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="EM.html" title="class in org.opencv.ml">EM</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.
 Load the EM from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized EM</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
