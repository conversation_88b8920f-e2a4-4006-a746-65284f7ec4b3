<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>SVMSGD (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: SVMSGD">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class SVMSGD" class="title">Class SVMSGD</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.SVMSGD</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SVMSGD</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">*************************************************************************************\
 Stochastic Gradient Descent SVM Classifier                      *
 \***************************************************************************************</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ASGD" class="member-name-link">ASGD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#HARD_MARGIN" class="member-name-link">HARD_MARGIN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SGD" class="member-name-link">SGD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SOFT_MARGIN" class="member-name-link">SOFT_MARGIN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates empty model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInitialStepSize()" class="member-name-link">getInitialStepSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setInitialStepSize</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMarginRegularization()" class="member-name-link">getMarginRegularization</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setMarginRegularization</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMarginType()" class="member-name-link">getMarginType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setMarginType</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShift()" class="member-name-link">getShift</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStepDecreasingPower()" class="member-name-link">getStepDecreasingPower</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setStepDecreasingPower</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSvmsgdType()" class="member-name-link">getSvmsgdType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setSvmsgdType</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTermCriteria()" class="member-name-link">getTermCriteria</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setTermCriteria</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWeights()" class="member-name-link">getWeights</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String,java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInitialStepSize(float)" class="member-name-link">setInitialStepSize</a><wbr>(float&nbsp;InitialStepSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getInitialStepSize SEE: getInitialStepSize</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMarginRegularization(float)" class="member-name-link">setMarginRegularization</a><wbr>(float&nbsp;marginRegularization)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getMarginRegularization SEE: getMarginRegularization</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMarginType(int)" class="member-name-link">setMarginType</a><wbr>(int&nbsp;marginType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getMarginType SEE: getMarginType</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOptimalParameters()" class="member-name-link">setOptimalParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOptimalParameters(int)" class="member-name-link">setOptimalParameters</a><wbr>(int&nbsp;svmsgdType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOptimalParameters(int,int)" class="member-name-link">setOptimalParameters</a><wbr>(int&nbsp;svmsgdType,
 int&nbsp;marginType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStepDecreasingPower(float)" class="member-name-link">setStepDecreasingPower</a><wbr>(float&nbsp;stepDecreasingPower)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getStepDecreasingPower SEE: getStepDecreasingPower</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSvmsgdType(int)" class="member-name-link">setSvmsgdType</a><wbr>(int&nbsp;svmsgdType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getSvmsgdType SEE: getSvmsgdType</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTermCriteria(org.opencv.core.TermCriteria)" class="member-name-link">setTermCriteria</a><wbr>(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="SOFT_MARGIN">
<h3>SOFT_MARGIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SOFT_MARGIN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.SOFT_MARGIN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="HARD_MARGIN">
<h3>HARD_MARGIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">HARD_MARGIN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.HARD_MARGIN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SGD">
<h3>SGD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SGD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.SGD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ASGD">
<h3>ASGD</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ASGD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.ASGD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getWeights()">
<h3>getWeights</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Mat.html" title="class in org.opencv.core">Mat</a></span>&nbsp;<span class="element-name">getWeights</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the weights of the trained model (decision function f(x) = weights * x + shift).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getShift()">
<h3>getShift</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getShift</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the shift of the trained model (decision function f(x) = weights * x + shift).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates empty model.
 Use StatModel::train to train the model. Since %SVMSGD has several parameters, you may want to
 find the best parameters for your problem or use setOptimalParameters() to set some default parameters.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String,java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</span></div>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.
 Load the SVMSGD from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized SVMSGD</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.
 Load the SVMSGD from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized SVMSGD</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOptimalParameters(int,int)">
<h3>setOptimalParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOptimalParameters</span><wbr><span class="parameters">(int&nbsp;svmsgdType,
 int&nbsp;marginType)</span></div>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>svmsgdType</code> - is the type of SVMSGD classifier.</dd>
<dd><code>marginType</code> - is the type of margin constraint.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOptimalParameters(int)">
<h3>setOptimalParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOptimalParameters</span><wbr><span class="parameters">(int&nbsp;svmsgdType)</span></div>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>svmsgdType</code> - is the type of SVMSGD classifier.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOptimalParameters()">
<h3>setOptimalParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOptimalParameters</span>()</div>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</section>
</li>
<li>
<section class="detail" id="getSvmsgdType()">
<h3>getSvmsgdType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getSvmsgdType</span>()</div>
<div class="block">SEE: setSvmsgdType</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSvmsgdType(int)">
<h3>setSvmsgdType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSvmsgdType</span><wbr><span class="parameters">(int&nbsp;svmsgdType)</span></div>
<div class="block">getSvmsgdType SEE: getSvmsgdType</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>svmsgdType</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMarginType()">
<h3>getMarginType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMarginType</span>()</div>
<div class="block">SEE: setMarginType</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMarginType(int)">
<h3>setMarginType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMarginType</span><wbr><span class="parameters">(int&nbsp;marginType)</span></div>
<div class="block">getMarginType SEE: getMarginType</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>marginType</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMarginRegularization()">
<h3>getMarginRegularization</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getMarginRegularization</span>()</div>
<div class="block">SEE: setMarginRegularization</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMarginRegularization(float)">
<h3>setMarginRegularization</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMarginRegularization</span><wbr><span class="parameters">(float&nbsp;marginRegularization)</span></div>
<div class="block">getMarginRegularization SEE: getMarginRegularization</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>marginRegularization</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInitialStepSize()">
<h3>getInitialStepSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getInitialStepSize</span>()</div>
<div class="block">SEE: setInitialStepSize</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInitialStepSize(float)">
<h3>setInitialStepSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInitialStepSize</span><wbr><span class="parameters">(float&nbsp;InitialStepSize)</span></div>
<div class="block">getInitialStepSize SEE: getInitialStepSize</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>InitialStepSize</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStepDecreasingPower()">
<h3>getStepDecreasingPower</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getStepDecreasingPower</span>()</div>
<div class="block">SEE: setStepDecreasingPower</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStepDecreasingPower(float)">
<h3>setStepDecreasingPower</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStepDecreasingPower</span><wbr><span class="parameters">(float&nbsp;stepDecreasingPower)</span></div>
<div class="block">getStepDecreasingPower SEE: getStepDecreasingPower</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stepDecreasingPower</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTermCriteria()">
<h3>getTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></span>&nbsp;<span class="element-name">getTermCriteria</span>()</div>
<div class="block">SEE: setTermCriteria</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTermCriteria(org.opencv.core.TermCriteria)">
<h3>setTermCriteria</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTermCriteria</span><wbr><span class="parameters">(<a href="../core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</span></div>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
