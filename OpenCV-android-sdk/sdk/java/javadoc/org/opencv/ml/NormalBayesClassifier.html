<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>NormalBayesClassifier (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: NormalBayesClassifier">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class NormalBayesClassifier" class="title">Class NormalBayesClassifier</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.NormalBayesClassifier</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">NormalBayesClassifier</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">Bayes classifier for normally distributed data.

 SEE: REF: ml_intro_bayes</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates empty model
 Use StatModel::train to train the model after creation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized NormalBayesClassifier from a file

 Use NormalBayesClassifier::save to serialize and store an NormalBayesClassifier to disk.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String,java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized NormalBayesClassifier from a file

 Use NormalBayesClassifier::save to serialize and store an NormalBayesClassifier to disk.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predictProb(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">predictProb</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputProbs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Predicts the response for sample(s).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#predictProb(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">predictProb</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputProbs,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Predicts the response for sample(s).</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="predictProb(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>predictProb</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">predictProb</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputProbs,
 int&nbsp;flags)</span></div>
<div class="block">Predicts the response for sample(s).

     The method estimates the most probable classes for input vectors. Input vectors (one or more)
     are stored as rows of the matrix inputs. In case of multiple input vectors, there should be one
     output vector outputs. The predicted class for a single input vector is returned by the method.
     The vector outputProbs contains the output probabilities corresponding to each element of
     result.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputs</code> - automatically generated</dd>
<dd><code>outputs</code> - automatically generated</dd>
<dd><code>outputProbs</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="predictProb(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>predictProb</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">predictProb</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outputProbs)</span></div>
<div class="block">Predicts the response for sample(s).

     The method estimates the most probable classes for input vectors. Input vectors (one or more)
     are stored as rows of the matrix inputs. In case of multiple input vectors, there should be one
     output vector outputs. The predicted class for a single input vector is returned by the method.
     The vector outputProbs contains the output probabilities corresponding to each element of
     result.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputs</code> - automatically generated</dd>
<dd><code>outputs</code> - automatically generated</dd>
<dd><code>outputProbs</code> - automatically generated</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates empty model
 Use StatModel::train to train the model after creation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String,java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nodeName)</span></div>
<div class="block">Loads and creates a serialized NormalBayesClassifier from a file

 Use NormalBayesClassifier::save to serialize and store an NormalBayesClassifier to disk.
 Load the NormalBayesClassifier from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized NormalBayesClassifier</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="NormalBayesClassifier.html" title="class in org.opencv.ml">NormalBayesClassifier</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized NormalBayesClassifier from a file

 Use NormalBayesClassifier::save to serialize and store an NormalBayesClassifier to disk.
 Load the NormalBayesClassifier from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized NormalBayesClassifier</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
