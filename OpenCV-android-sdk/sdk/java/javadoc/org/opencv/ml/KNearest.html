<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>KNearest (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.ml, class: KNearest">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.ml</a></div>
<h1 title="Class KNearest" class="title">Class KNearest</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a>
<div class="inheritance"><a href="StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a>
<div class="inheritance">org.opencv.ml.KNearest</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">KNearest</span>
<span class="extends-implements">extends <a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></span></div>
<div class="block">The class implements K-Nearest Neighbors model

 SEE: REF: ml_intro_knn</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#BRUTE_FORCE" class="member-name-link">BRUTE_FORCE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#KDTREE" class="member-name-link">KDTREE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.ml.StatModel">Fields inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create()" class="member-name-link">create</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates the empty model

     The static method creates empty %KNearest classifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat)" class="member-name-link">findNearest</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">findNearest</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">findNearest</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAlgorithmType()" class="member-name-link">getAlgorithmType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setAlgorithmType</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultK()" class="member-name-link">getDefaultK</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setDefaultK</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEmax()" class="member-name-link">getEmax</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setEmax</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIsClassifier()" class="member-name-link">getIsClassifier</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SEE: setIsClassifier</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Loads and creates a serialized knearest from a file

 Use KNearest::save to serialize and store an KNearest to disk.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAlgorithmType(int)" class="member-name-link">setAlgorithmType</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getAlgorithmType SEE: getAlgorithmType</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultK(int)" class="member-name-link">setDefaultK</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getDefaultK SEE: getDefaultK</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEmax(int)" class="member-name-link">setEmax</a><wbr>(int&nbsp;val)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getEmax SEE: getEmax</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIsClassifier(boolean)" class="member-name-link">setIsClassifier</a><wbr>(boolean&nbsp;val)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">getIsClassifier SEE: getIsClassifier</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.ml.StatModel">Methods inherited from class&nbsp;org.opencv.ml.<a href="StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="StatModel.html#calcError(org.opencv.ml.TrainData,boolean,org.opencv.core.Mat)">calcError</a>, <a href="StatModel.html#empty()">empty</a>, <a href="StatModel.html#getVarCount()">getVarCount</a>, <a href="StatModel.html#isClassifier()">isClassifier</a>, <a href="StatModel.html#isTrained()">isTrained</a>, <a href="StatModel.html#predict(org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat)">predict</a>, <a href="StatModel.html#predict(org.opencv.core.Mat,org.opencv.core.Mat,int)">predict</a>, <a href="StatModel.html#train(org.opencv.core.Mat,int,org.opencv.core.Mat)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData)">train</a>, <a href="StatModel.html#train(org.opencv.ml.TrainData,int)">train</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Algorithm">Methods inherited from class&nbsp;org.opencv.core.<a href="../core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../core/Algorithm.html#clear()">clear</a>, <a href="../core/Algorithm.html#getDefaultName()">getDefaultName</a>, <a href="../core/Algorithm.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="../core/Algorithm.html#save(java.lang.String)">save</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="BRUTE_FORCE">
<h3>BRUTE_FORCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">BRUTE_FORCE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.KNearest.BRUTE_FORCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="KDTREE">
<h3>KDTREE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">KDTREE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.ml.KNearest.KDTREE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDefaultK()">
<h3>getDefaultK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDefaultK</span>()</div>
<div class="block">SEE: setDefaultK</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDefaultK(int)">
<h3>setDefaultK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultK</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getDefaultK SEE: getDefaultK</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIsClassifier()">
<h3>getIsClassifier</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIsClassifier</span>()</div>
<div class="block">SEE: setIsClassifier</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIsClassifier(boolean)">
<h3>setIsClassifier</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIsClassifier</span><wbr><span class="parameters">(boolean&nbsp;val)</span></div>
<div class="block">getIsClassifier SEE: getIsClassifier</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEmax()">
<h3>getEmax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getEmax</span>()</div>
<div class="block">SEE: setEmax</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEmax(int)">
<h3>setEmax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEmax</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getEmax SEE: getEmax</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAlgorithmType()">
<h3>getAlgorithmType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getAlgorithmType</span>()</div>
<div class="block">SEE: setAlgorithmType</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAlgorithmType(int)">
<h3>setAlgorithmType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAlgorithmType</span><wbr><span class="parameters">(int&nbsp;val)</span></div>
<div class="block">getAlgorithmType SEE: getAlgorithmType</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>findNearest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">findNearest</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist)</span></div>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.</dd>
<dd><code>neighborResponses</code> - Optional output values for corresponding neighbors. It is a single-
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>dist</code> - Optional output distances from the input vectors to the corresponding neighbors. It
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>findNearest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">findNearest</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses)</span></div>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.</dd>
<dd><code>neighborResponses</code> - Optional output values for corresponding neighbors. It is a single-
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findNearest(org.opencv.core.Mat,int,org.opencv.core.Mat)">
<h3>findNearest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">findNearest</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
 int&nbsp;k,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</span></div>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create()">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></span>&nbsp;<span class="element-name">create</span>()</div>
<div class="block">Creates the empty model

     The static method creates empty %KNearest classifier. It should be then trained using StatModel::train method.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="KNearest.html" title="class in org.opencv.ml">KNearest</a></span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filepath)</span></div>
<div class="block">Loads and creates a serialized knearest from a file

 Use KNearest::save to serialize and store an KNearest to disk.
 Load the KNearest from this file again, by calling this function with the path to the file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filepath</code> - path to serialized KNearest</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
