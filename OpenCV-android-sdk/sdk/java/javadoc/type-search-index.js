typeSearchIndex = [{"p":"org.opencv.features2d","l":"AffineFeature"},{"p":"org.opencv.features2d","l":"AgastFeatureDetector"},{"p":"org.opencv.features2d","l":"AKAZE"},{"p":"org.opencv.core","l":"Algorithm"},{"p":"org.opencv.photo","l":"AlignExposures"},{"p":"org.opencv.photo","l":"AlignMTB"},{"l":"All Classes and Interfaces","u":"allclasses-index.html"},{"p":"org.opencv.imgcodecs","l":"Animation"},{"p":"org.opencv.ml","l":"ANN_MLP"},{"p":"org.opencv.objdetect","l":"ArucoDetector"},{"p":"org.opencv.core","l":"Mat.Atable"},{"p":"org.opencv.video","l":"BackgroundSubtractor"},{"p":"org.opencv.video","l":"BackgroundSubtractorKNN"},{"p":"org.opencv.video","l":"BackgroundSubtractorMOG2"},{"p":"org.opencv.objdetect","l":"BarcodeDetector"},{"p":"org.opencv.objdetect","l":"BaseCascadeClassifier"},{"p":"org.opencv.features2d","l":"BFMatcher"},{"p":"org.opencv.objdetect","l":"Board"},{"p":"org.opencv.ml","l":"Boost"},{"p":"org.opencv.features2d","l":"BOWImgDescriptorExtractor"},{"p":"org.opencv.features2d","l":"BOWKMeansTrainer"},{"p":"org.opencv.features2d","l":"BOWTrainer"},{"p":"org.opencv.features2d","l":"BRISK"},{"p":"org.opencv","l":"BuildConfig"},{"p":"org.opencv.calib3d","l":"Calib3d"},{"p":"org.opencv.photo","l":"CalibrateCRF"},{"p":"org.opencv.photo","l":"CalibrateDebevec"},{"p":"org.opencv.photo","l":"CalibrateRobertson"},{"p":"org.opencv.android","l":"Camera2Renderer"},{"p":"org.opencv.android","l":"CameraActivity"},{"p":"org.opencv.android","l":"CameraBridgeViewBase"},{"p":"org.opencv.android","l":"CameraGLRendererBase"},{"p":"org.opencv.android","l":"CameraGLSurfaceView"},{"p":"org.opencv.android","l":"CameraRenderer"},{"p":"org.opencv.android","l":"CameraGLSurfaceView.CameraTextureListener"},{"p":"org.opencv.objdetect","l":"CascadeClassifier"},{"p":"org.opencv.objdetect","l":"CharucoBoard"},{"p":"org.opencv.objdetect","l":"CharucoDetector"},{"p":"org.opencv.objdetect","l":"CharucoParameters"},{"p":"org.opencv.imgproc","l":"CLAHE"},{"p":"org.opencv.dnn","l":"ClassificationModel"},{"p":"org.opencv.utils","l":"Converters"},{"p":"org.opencv.core","l":"Core"},{"p":"org.opencv.android","l":"CameraBridgeViewBase.CvCameraViewFrame"},{"p":"org.opencv.android","l":"CameraBridgeViewBase.CvCameraViewListener"},{"p":"org.opencv.android","l":"CameraBridgeViewBase.CvCameraViewListener2"},{"p":"org.opencv.core","l":"CvException"},{"p":"org.opencv.core","l":"CvType"},{"p":"org.opencv.video","l":"DenseOpticalFlow"},{"p":"org.opencv.features2d","l":"DescriptorMatcher"},{"p":"org.opencv.dnn","l":"DetectionModel"},{"p":"org.opencv.objdetect","l":"DetectorParameters"},{"p":"org.opencv.objdetect","l":"Dictionary"},{"p":"org.opencv.dnn","l":"DictValue"},{"p":"org.opencv.video","l":"DISOpticalFlow"},{"p":"org.opencv.core","l":"DMatch"},{"p":"org.opencv.dnn","l":"Dnn"},{"p":"org.opencv.ml","l":"DTrees"},{"p":"org.opencv.ml","l":"EM"},{"p":"org.opencv.objdetect","l":"FaceDetectorYN"},{"p":"org.opencv.objdetect","l":"FaceRecognizerSF"},{"p":"org.opencv.video","l":"FarnebackOpticalFlow"},{"p":"org.opencv.features2d","l":"FastFeatureDetector"},{"p":"org.opencv.features2d","l":"Feature2D"},{"p":"org.opencv.features2d","l":"Features2d"},{"p":"org.opencv.features2d","l":"FlannBasedMatcher"},{"p":"org.opencv.android","l":"FpsMeter"},{"p":"org.opencv.imgproc","l":"GeneralizedHough"},{"p":"org.opencv.imgproc","l":"GeneralizedHoughBallard"},{"p":"org.opencv.imgproc","l":"GeneralizedHoughGuil"},{"p":"org.opencv.features2d","l":"GFTTDetector"},{"p":"org.opencv.objdetect","l":"GraphicalCodeDetector"},{"p":"org.opencv.objdetect","l":"GridBoard"},{"p":"org.opencv.objdetect","l":"HOGDescriptor"},{"p":"org.opencv.dnn","l":"Image2BlobParams"},{"p":"org.opencv.imgcodecs","l":"Imgcodecs"},{"p":"org.opencv.imgproc","l":"Imgproc"},{"p":"org.opencv.imgproc","l":"IntelligentScissorsMB"},{"p":"org.opencv.videoio","l":"IStreamReader"},{"p":"org.opencv.android","l":"JavaCamera2View"},{"p":"org.opencv.android","l":"JavaCamera2View.JavaCameraSizeAccessor"},{"p":"org.opencv.android","l":"JavaCameraView.JavaCameraSizeAccessor"},{"p":"org.opencv.android","l":"JavaCameraView"},{"p":"org.opencv.video","l":"KalmanFilter"},{"p":"org.opencv.features2d","l":"KAZE"},{"p":"org.opencv.core","l":"KeyPoint"},{"p":"org.opencv.dnn","l":"KeypointsModel"},{"p":"org.opencv.ml","l":"KNearest"},{"p":"org.opencv.dnn","l":"Layer"},{"p":"org.opencv.imgproc","l":"LineSegmentDetector"},{"p":"org.opencv.android","l":"CameraBridgeViewBase.ListItemAccessor"},{"p":"org.opencv.ml","l":"LogisticRegression"},{"p":"org.opencv.core","l":"Mat"},{"p":"org.opencv.core","l":"MatOfByte"},{"p":"org.opencv.core","l":"MatOfDMatch"},{"p":"org.opencv.core","l":"MatOfDouble"},{"p":"org.opencv.core","l":"MatOfFloat"},{"p":"org.opencv.core","l":"MatOfFloat4"},{"p":"org.opencv.core","l":"MatOfFloat6"},{"p":"org.opencv.core","l":"MatOfInt"},{"p":"org.opencv.core","l":"MatOfInt4"},{"p":"org.opencv.core","l":"MatOfKeyPoint"},{"p":"org.opencv.core","l":"MatOfPoint"},{"p":"org.opencv.core","l":"MatOfPoint2f"},{"p":"org.opencv.core","l":"MatOfPoint3"},{"p":"org.opencv.core","l":"MatOfPoint3f"},{"p":"org.opencv.core","l":"MatOfRect"},{"p":"org.opencv.core","l":"MatOfRect2d"},{"p":"org.opencv.core","l":"MatOfRotatedRect"},{"p":"org.opencv.photo","l":"MergeDebevec"},{"p":"org.opencv.photo","l":"MergeExposures"},{"p":"org.opencv.photo","l":"MergeMertens"},{"p":"org.opencv.photo","l":"MergeRobertson"},{"p":"org.opencv.core","l":"Core.MinMaxLocResult"},{"p":"org.opencv.ml","l":"Ml"},{"p":"org.opencv.dnn","l":"Model"},{"p":"org.opencv.imgproc","l":"Moments"},{"p":"org.opencv.features2d","l":"MSER"},{"p":"org.opencv.android","l":"NativeCameraView"},{"p":"org.opencv.dnn","l":"Net"},{"p":"org.opencv.ml","l":"NormalBayesClassifier"},{"p":"org.opencv.objdetect","l":"Objdetect"},{"p":"org.opencv.osgi","l":"OpenCVInterface"},{"p":"org.opencv.android","l":"OpenCVLoader"},{"p":"org.opencv.osgi","l":"OpenCVNativeLoader"},{"p":"org.opencv.android","l":"NativeCameraView.OpenCvSizeAccessor"},{"p":"org.opencv.features2d","l":"ORB"},{"p":"org.opencv.ml","l":"ParamGrid"},{"p":"org.opencv.photo","l":"Photo"},{"p":"org.opencv.core","l":"Point"},{"p":"org.opencv.core","l":"Point3"},{"p":"org.opencv.objdetect","l":"QRCodeDetector"},{"p":"org.opencv.objdetect","l":"QRCodeDetectorAruco"},{"p":"org.opencv.objdetect","l":"QRCodeDetectorAruco_Params"},{"p":"org.opencv.objdetect","l":"QRCodeEncoder"},{"p":"org.opencv.objdetect","l":"QRCodeEncoder_Params"},{"p":"org.opencv","l":"R"},{"p":"org.opencv.core","l":"Range"},{"p":"org.opencv.core","l":"Rect"},{"p":"org.opencv.core","l":"Rect2d"},{"p":"org.opencv.objdetect","l":"RefineParameters"},{"p":"org.opencv.android","l":"CameraBridgeViewBase.RotatedCameraFrame"},{"p":"org.opencv.core","l":"RotatedRect"},{"p":"org.opencv.ml","l":"RTrees"},{"p":"org.opencv.core","l":"Scalar"},{"p":"org.opencv.dnn","l":"SegmentationModel"},{"p":"org.opencv.features2d","l":"SIFT"},{"p":"org.opencv.features2d","l":"SimpleBlobDetector"},{"p":"org.opencv.features2d","l":"SimpleBlobDetector_Params"},{"p":"org.opencv.core","l":"Size"},{"p":"org.opencv.video","l":"SparseOpticalFlow"},{"p":"org.opencv.video","l":"SparsePyrLKOpticalFlow"},{"p":"org.opencv.ml","l":"StatModel"},{"p":"org.opencv.calib3d","l":"StereoBM"},{"p":"org.opencv.calib3d","l":"StereoMatcher"},{"p":"org.opencv.calib3d","l":"StereoSGBM"},{"p":"org.opencv.imgproc","l":"Subdiv2D"},{"p":"org.opencv.ml","l":"SVM"},{"p":"org.opencv.ml","l":"SVMSGD"},{"p":"org.opencv.core","l":"TermCriteria"},{"p":"org.opencv.dnn","l":"TextDetectionModel"},{"p":"org.opencv.dnn","l":"TextDetectionModel_DB"},{"p":"org.opencv.dnn","l":"TextDetectionModel_EAST"},{"p":"org.opencv.dnn","l":"TextRecognitionModel"},{"p":"org.opencv.core","l":"TickMeter"},{"p":"org.opencv.photo","l":"Tonemap"},{"p":"org.opencv.photo","l":"TonemapDrago"},{"p":"org.opencv.photo","l":"TonemapMantiuk"},{"p":"org.opencv.photo","l":"TonemapReinhard"},{"p":"org.opencv.video","l":"Tracker"},{"p":"org.opencv.video","l":"TrackerDaSiamRPN"},{"p":"org.opencv.video","l":"TrackerDaSiamRPN_Params"},{"p":"org.opencv.video","l":"TrackerGOTURN"},{"p":"org.opencv.video","l":"TrackerGOTURN_Params"},{"p":"org.opencv.video","l":"TrackerMIL"},{"p":"org.opencv.video","l":"TrackerMIL_Params"},{"p":"org.opencv.video","l":"TrackerNano"},{"p":"org.opencv.video","l":"TrackerNano_Params"},{"p":"org.opencv.video","l":"TrackerVit"},{"p":"org.opencv.video","l":"TrackerVit_Params"},{"p":"org.opencv.ml","l":"TrainData"},{"p":"org.opencv.core","l":"Mat.Tuple2"},{"p":"org.opencv.core","l":"Mat.Tuple3"},{"p":"org.opencv.core","l":"Mat.Tuple4"},{"p":"org.opencv.calib3d","l":"UsacParams"},{"p":"org.opencv.android","l":"Utils"},{"p":"org.opencv.video","l":"VariationalRefinement"},{"p":"org.opencv.video","l":"Video"},{"p":"org.opencv.videoio","l":"VideoCapture"},{"p":"org.opencv.videoio","l":"Videoio"},{"p":"org.opencv.videoio","l":"VideoWriter"}];updateSearchResults();