apply plugin: 'com.android.application'

android {
    namespace 'org.opencv.samples.puzzle15'
    compileSdkVersion 34
    defaultConfig {
        applicationId "org.opencv.samples.puzzle15"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 301
        versionName "3.01"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            manifest.srcFile 'AndroidManifest.xml'
        }
    }
}

dependencies {
    //implementation fileTree(dir: 'libs', include: ['*.jar'])
    if (gradle.opencv_source == 'sdk_path') {
        println 'Using OpenCV from SDK'
        implementation project(':opencv')
    } else if (gradle.opencv_source == 'maven_local' || gradle.opencv_source == 'maven_central') {
        println 'Using OpenCV from Maven repo'
        implementation 'org.opencv:opencv:4.11.0'
    }
}
