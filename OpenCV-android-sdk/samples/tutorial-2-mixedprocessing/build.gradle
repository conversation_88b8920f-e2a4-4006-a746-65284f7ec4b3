apply plugin: 'com.android.application'

android {
    namespace 'org.opencv.samples.tutorial2'
    compileSdkVersion 34
    defaultConfig {
        applicationId "org.opencv.samples.tutorial2"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 301
        versionName "3.01"

        externalNativeBuild {
            cmake {
                if (gradle.opencv_source == "sdk_path") {
                    arguments "-DOpenCV_DIR=" + project(':opencv').projectDir + "/native/jni",
                              "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON",
                              "-DOPENCV_FROM_SDK=TRUE",
"-DANDROID_TOOLCHAIN=clang",
"-DANDROID_STL=c++_shared"
                } else {
                    arguments "-DOPENCV_VERSION_MAJOR=4",
                              "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON",
                              "-DOPENCV_FROM_SDK=FALSE",
"-DANDROID_TOOLCHAIN=clang",
"-DANDROID_STL=c++_shared"
                }
                targets "mixed_sample"
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            manifest.srcFile 'AndroidManifest.xml'
        }
    }
    externalNativeBuild {
        cmake {
             path 'jni/CMakeLists.txt'
        }
    }
    buildFeatures {
        if (gradle.opencv_source == "maven_local" || gradle.opencv_source == "maven_central") {
            prefab true
        }
    }
}

dependencies {
    //implementation fileTree(dir: 'libs', include: ['*.jar'])
    if (gradle.opencv_source == "sdk_path") {
        println 'Using OpenCV from SDK'
        implementation project(':opencv')
    } else if (gradle.opencv_source == "maven_local" || gradle.opencv_source == "maven_central") {
        println 'Using OpenCV from Maven repo'
        implementation 'org.opencv:opencv:4.11.0'
    }
}
