
rootProject.name = 'opencv_samples'

gradle.ext {
    // possible options: 'maven_central', 'maven_local', 'sdk_path'
    opencv_source = 'sdk_path'
}

if (gradle.opencv_source == 'maven_local') {
    gradle.ext {
        opencv_maven_path = '<path_to_maven_repo>'
    }
}

if (gradle.opencv_source == 'sdk_path') {
    def opencvsdk = '../'
    //def opencvsdk='/<path to OpenCV-android-sdk>'
    //println opencvsdk
    include ':opencv'
    project(':opencv').projectDir = new File(opencvsdk + '/sdk')
}

include ':15-puzzle'

include ':face-detection'

include ':qr-detection'

include ':image-manipulations'

include ':camera-calibration'

include ':color-blob-detection'

include ':mobilenet-objdetect'

include ':video-recorder'

include ':tutorial-1-camerapreview'

include ':tutorial-2-mixedprocessing'

include ':tutorial-3-cameracontrol'

include ':tutorial-4-opencl'
