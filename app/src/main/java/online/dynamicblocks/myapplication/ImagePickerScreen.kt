package online.dynamicblocks.myapplication

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/**
 * Check if two images are from the same hand (hand recognition test)
 */
fun isSameHand(imageName1: String, imageName2: String): Boolean {
    // Extract the hand ID (number before FLASH_ON/OFF)
    val handId1 = imageName1.substringBefore("_FLASH_").trim()
    val handId2 = imageName2.substringBefore("_FLASH_").trim()

    // Check if same hand ID but different flash settings
    val isDifferentFlash = (imageName1.contains("FLASH_ON") && imageName2.contains("FLASH_OFF")) ||
                          (imageName1.contains("FLASH_OFF") && imageName2.contains("FLASH_ON"))

    return handId1 == handId2 && isDifferentFlash
}

/**
 * Main screen for interactive image picking and phase correlation
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImagePickerScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    val imagePickerActivity = remember { 
        ImagePickerActivity().apply { 
            initialize(ImageLoader(context)) 
        } 
    }
    
    var availableImages by remember { mutableStateOf<List<ImagePreview>>(emptyList()) }
    var selectedImage1 by remember { mutableStateOf<ImagePreview?>(null) }
    var selectedImage2 by remember { mutableStateOf<ImagePreview?>(null) }
    var correlationResult by remember { mutableStateOf<PhaseCorrelationResult?>(null) }
    var advancedCorrelationResult by remember { mutableStateOf<AdvancedPhaseCorrelationResult?>(null) }
    var testResult by remember { mutableStateOf<String?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var isCalculating by remember { mutableStateOf(false) }
    var isAdvancedCalculating by remember { mutableStateOf(false) }
    // Always use standard mode (advanced mode toggle removed)
    val useAdvancedMode = false
    
    // Load images on first composition
    LaunchedEffect(Unit) {
        isLoading = true
        try {
            availableImages = imagePickerActivity.getAvailableImagesWithPreview()
        } catch (e: Exception) {
            // Handle error
        } finally {
            isLoading = false
        }
    }
    
    if (isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(16.dp))
                Text("Loading images...")
            }
        }
    } else {
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Text(
                text = "🔬 Interactive Phase Correlation",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            // Selection section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Image 1 selection
                Card(
                    modifier = Modifier.weight(1f),
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedImage1 != null) Color(0xFFE3F2FD) else MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "📷 Image 1",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        
                        if (selectedImage1 != null) {
                            Text(
                                text = selectedImage1!!.displayName,
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF1976D2)
                            )
                            if (selectedImage1 == selectedImage2) {
                                Text(
                                    text = "Same as Image 2",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = Color(0xFF4CAF50)
                                )
                            }
                        } else {
                            Text(
                                text = "Tap to select",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                // VS indicator
                Box(
                    modifier = Modifier.align(Alignment.CenterVertically)
                ) {
                    Text(
                        text = "VS",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                // Image 2 selection
                Card(
                    modifier = Modifier.weight(1f),
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedImage2 != null) Color(0xFFE8F5E8) else MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "📷 Image 2",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        
                        if (selectedImage2 != null) {
                            Text(
                                text = selectedImage2!!.displayName,
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF388E3C)
                            )
                            if (selectedImage1 == selectedImage2) {
                                Text(
                                    text = "Same as Image 1",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = Color(0xFF4CAF50)
                                )
                            }
                        } else {
                            Text(
                                text = "Tap to select",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            // Test type indicators
            if (selectedImage1 != null && selectedImage2 != null) {
                when {
                    selectedImage1 == selectedImage2 -> {
                        // Same image test
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFE8F5E8)
                            )
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "🔍",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Same Image Test - Should give high correlation (~100%)",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2E7D32)
                                )
                            }
                        }
                    }
                    isSameHand(selectedImage1!!.name, selectedImage2!!.name) -> {
                        // Same hand test (hand recognition)
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFFFF3E0)
                            )
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "🤚",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Column {
                                    Text(
                                        text = "Same Hand Test (Hand Recognition)",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color(0xFFE65100),
                                        fontWeight = FontWeight.Medium
                                    )
                                    Text(
                                        text = "Same hand, different lighting - Should give >70% correlation",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFFE65100)
                                    )
                                }
                            }
                        }
                    }
                }
            }



            // Calculate button
            if (useAdvancedMode) {
                // Advanced mode: Camera vs Multiple User Images
                Button(
                    onClick = {
                        if (selectedImage1 != null && availableImages.isNotEmpty()) {
                            isAdvancedCalculating = true
                            coroutineScope.launch {
                                try {
                                    // Use first image as camera, rest as user images
                                    val userImages = availableImages.filter { it != selectedImage1 }
                                    advancedCorrelationResult = imagePickerActivity.runPhaseCorrelate(
                                        selectedImage1!!,
                                        userImages,
                                        inputSize = 512,
                                        minDistance = 0.4
                                    )
                                } catch (e: Exception) {
                                    // Handle error
                                } finally {
                                    isAdvancedCalculating = false
                                }
                            }
                        }
                    },
                    enabled = selectedImage1 != null && !isAdvancedCalculating,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (isAdvancedCalculating) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Processing with rotation...")
                        }
                    } else {
                        Text("🚀 Customer's run_phaseCorrelate (${availableImages.size - 1} user images)")
                    }
                }
            } else {
                // Standard mode: Two selected images
                Button(
                    onClick = {
                        if (selectedImage1 != null && selectedImage2 != null) {
                            isCalculating = true
                            coroutineScope.launch {
                                try {
                                    correlationResult = imagePickerActivity.calculateCorrelation(
                                        selectedImage1!!,
                                        selectedImage2!!
                                    )
                                } catch (e: Exception) {
                                    // Handle error
                                } finally {
                                    isCalculating = false
                                }
                            }
                        }
                    },
                    enabled = selectedImage1 != null && selectedImage2 != null && !isCalculating,
                    modifier = Modifier.fillMaxWidth()
                ) {
                if (isCalculating) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Calculating...")
                    }
                } else {
                    val buttonText = when {
                        selectedImage1 == selectedImage2 -> "🔍 Test Same Image (Should be ~100%)"
                        selectedImage1 != null && selectedImage2 != null &&
                        isSameHand(selectedImage1!!.name, selectedImage2!!.name) -> "🤚 Test Hand Recognition (Should be >70%)"
                        else -> "🔬 Calculate Phase Correlation"
                    }
                    Text(buttonText)
                }
            }



            // OpenCV Verification Test button
            Spacer(modifier = Modifier.height(8.dp))
            Button(
                onClick = {
                    isCalculating = true
                    coroutineScope.launch {
                        try {
                            val verificationTest = OpenCVVerificationTest()
                            val result = verificationTest.runOpenCVVerificationTest()
                            testResult = result
                            correlationResult = null // Clear previous correlation result
                        } catch (e: Exception) {
                            testResult = "❌ OpenCV Verification Test failed: ${e.message}"
                        } finally {
                            isCalculating = false
                        }
                    }
                },
                enabled = !isCalculating,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
            ) {
                Text("🔬 Test OpenCV Implementation")
            }

            // Result section
            correlationResult?.let { result ->
                CorrelationResultCard(
                    result = result,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Test result section
            testResult?.let { result ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF3E5F5))
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "OpenCV Verification Test Results",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF7B1FA2)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = result,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }

            // Advanced result section
            advancedCorrelationResult?.let { result ->
                AdvancedCorrelationResultCard(
                    result = result,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // Available images list
            Text(
                text = "📁 Available Images (${availableImages.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            // Available images with fixed height for better scrolling
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp), // Fixed height to prevent layout issues
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(availableImages) { imagePreview ->
                    ImageSelectionCard(
                        imagePreview = imagePreview,
                        isSelected = selectedImage1 == imagePreview || selectedImage2 == imagePreview,
                        onSelect = {
                            when {
                                selectedImage1 == null -> {
                                    // First selection - set as Image 1
                                    selectedImage1 = imagePreview
                                }
                                selectedImage2 == null -> {
                                    // Second selection - set as Image 2 (allow same image)
                                    selectedImage2 = imagePreview
                                }
                                selectedImage1 == imagePreview && selectedImage2 == imagePreview -> {
                                    // Both slots have same image - clear Image 2
                                    selectedImage2 = null
                                }
                                selectedImage1 == imagePreview -> {
                                    // Image 1 selected - move Image 2 to Image 1, clear Image 2
                                    selectedImage1 = selectedImage2
                                    selectedImage2 = null
                                }
                                selectedImage2 == imagePreview -> {
                                    // Image 2 selected - clear Image 2
                                    selectedImage2 = null
                                }
                                else -> {
                                    // Both slots filled with different images - replace Image 1
                                    selectedImage1 = imagePreview
                                }
                            }

                            // Clear result when selection changes
                            correlationResult = null
                        }
                    )
                }
            }
        }
    }
    }
}
