package online.dynamicblocks.myapplication

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/** Check if two images are from the same hand (hand recognition test) */
fun isSameHand(imageName1: String, imageName2: String): Bo<PERSON>an {
    // Extract the hand ID (number before FLASH_ON/OFF)
    val handId1 = imageName1.substringBefore("_FLASH_").trim()
    val handId2 = imageName2.substringBefore("_FLASH_").trim()

    // Check if same hand ID but different flash settings
    val isDifferentFlash =
            (imageName1.contains("FLASH_ON") && imageName2.contains("FLASH_OFF")) ||
                    (imageName1.contains("FLASH_OFF") && imageName2.contains("FLASH_ON"))

    return handId1 == handId2 && isDifferentFlash
}

/** Main screen for interactive image picking and phase correlation */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImagePickerScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    val imagePickerActivity = remember {
        ImagePickerActivity().apply { initialize(ImageLoader(context)) }
    }

    var availableImages by remember { mutableStateOf<List<ImagePreview>>(emptyList()) }
    var selectedImage1 by remember { mutableStateOf<ImagePreview?>(null) }
    var selectedImage2 by remember { mutableStateOf<ImagePreview?>(null) }
    var correlationResult by remember { mutableStateOf<PhaseCorrelationResult?>(null) }
    var advancedCorrelationResult by remember {
        mutableStateOf<AdvancedPhaseCorrelationResult?>(null)
    }
    var testResult by remember { mutableStateOf<String?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var isCalculating by remember { mutableStateOf(false) }
    var isAdvancedCalculating by remember { mutableStateOf(false) }
    // Always use standard mode (advanced mode toggle removed)
    val useAdvancedMode = false

    // State for popup
    var showImagePicker by remember { mutableStateOf(false) }
    var pickingForImage1 by remember { mutableStateOf(true) }

    // Load images on first composition
    LaunchedEffect(Unit) {
        isLoading = true
        try {
            availableImages = imagePickerActivity.getAvailableImagesWithPreview()
        } catch (e: Exception) {
            // Handle error
        } finally {
            isLoading = false
        }
    }

    if (isLoading) {
        Box(modifier = modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(16.dp))
                Text("Loading images...")
            }
        }
    } else {
        Column(
                modifier =
                        modifier.fillMaxSize().padding(16.dp).verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Text(
                    text = "🔬 Interactive Phase Correlation",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
            )
            // Image selection section with clickable areas
            Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Image 1 picker area
                ImagePickerCard(
                        modifier = Modifier.weight(1f),
                        title = "📷 Image 1",
                        selectedImage = selectedImage1,
                        otherImage = selectedImage2,
                        backgroundColor = Color(0xFFE3F2FD),
                        textColor = Color(0xFF1976D2),
                        onClick = {
                            pickingForImage1 = true
                            showImagePicker = true
                        }
                )

                // VS indicator
                Box(modifier = Modifier.align(Alignment.CenterVertically)) {
                    Text(
                            text = "VS",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                    )
                }

                // Image 2 picker area
                ImagePickerCard(
                        modifier = Modifier.weight(1f),
                        title = "📷 Image 2",
                        selectedImage = selectedImage2,
                        otherImage = selectedImage1,
                        backgroundColor = Color(0xFFE8F5E8),
                        textColor = Color(0xFF388E3C),
                        onClick = {
                            pickingForImage1 = false
                            showImagePicker = true
                        }
                )
            }

            // Test type indicators
            if (selectedImage1 != null && selectedImage2 != null) {
                when {
                    selectedImage1 == selectedImage2 -> {
                        // Same image test
                        Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
                        ) {
                            Row(
                                    modifier = Modifier.padding(12.dp),
                                    verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(text = "🔍", style = MaterialTheme.typography.titleMedium)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                        text =
                                                "Same Image Test - Should give high correlation (~100%)",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color(0xFF2E7D32)
                                )
                            }
                        }
                    }
                    isSameHand(selectedImage1!!.name, selectedImage2!!.name) -> {
                        // Same hand test (hand recognition)
                        Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFF3E0))
                        ) {
                            Row(
                                    modifier = Modifier.padding(12.dp),
                                    verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(text = "🤚", style = MaterialTheme.typography.titleMedium)
                                Spacer(modifier = Modifier.width(8.dp))
                                Column {
                                    Text(
                                            text = "Same Hand Test (Hand Recognition)",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = Color(0xFFE65100),
                                            fontWeight = FontWeight.Medium
                                    )
                                    Text(
                                            text =
                                                    "Same hand, different lighting - Should give >70% correlation",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = Color(0xFFE65100)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // Calculate button
            if (useAdvancedMode) {
                // Advanced mode: Camera vs Multiple User Images
                Button(
                        onClick = {
                            if (selectedImage1 != null && availableImages.isNotEmpty()) {
                                isAdvancedCalculating = true
                                coroutineScope.launch {
                                    try {
                                        // Use first image as camera, rest as user images
                                        val userImages =
                                                availableImages.filter { it != selectedImage1 }
                                        advancedCorrelationResult =
                                                imagePickerActivity.runPhaseCorrelate(
                                                        selectedImage1!!,
                                                        userImages,
                                                        inputSize = 512,
                                                        minDistance = 0.4
                                                )
                                    } catch (e: Exception) {
                                        // Handle error
                                    } finally {
                                        isAdvancedCalculating = false
                                    }
                                }
                            }
                        },
                        enabled = selectedImage1 != null && !isAdvancedCalculating,
                        modifier = Modifier.fillMaxWidth()
                ) {
                    if (isAdvancedCalculating) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Processing with rotation...")
                        }
                    } else {
                        Text(
                                "🚀 Customer's run_phaseCorrelate (${availableImages.size - 1} user images)"
                        )
                    }
                }
            } else {
                // Standard mode: Two selected images
                Button(
                        onClick = {
                            if (selectedImage1 != null && selectedImage2 != null) {
                                isCalculating = true
                                coroutineScope.launch {
                                    try {
                                        // Gọi trực tiếp cv::phaseCorrelate - đơn giản nhất
                                        correlationResult =
                                                imagePickerActivity.calculatePhaseCorrelationDirect(
                                                        selectedImage1!!,
                                                        selectedImage2!!
                                                )
                                    } catch (e: Exception) {
                                        // Handle error
                                    } finally {
                                        isCalculating = false
                                    }
                                }
                            }
                        },
                        enabled =
                                selectedImage1 != null && selectedImage2 != null && !isCalculating,
                        modifier = Modifier.fillMaxWidth()
                ) {
                    if (isCalculating) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Calculating...")
                        }
                    } else {

                        Text("🔬 Calculate Phase Correlation!!!")
                    }
                }

                // OpenCV Verification Test button
                Spacer(modifier = Modifier.height(8.dp))

                // Result section
                correlationResult?.let { result ->
                    CorrelationResultCard(result = result, modifier = Modifier.fillMaxWidth())
                }

                // Test result section
                testResult?.let { result ->
                    Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = Color(0xFFF3E5F5))
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                    text = "OpenCV Verification Test Results",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFF7B1FA2)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(text = result, style = MaterialTheme.typography.bodySmall)
                        }
                    }
                }

                // Advanced result section
                advancedCorrelationResult?.let { result ->
                    AdvancedCorrelationResultCard(
                            result = result,
                            modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }

    // Image picker popup dialog
    if (showImagePicker) {
        ImagePickerDialog(
                images = availableImages,
                onImageSelected = { selectedImage ->
                    if (pickingForImage1) {
                        selectedImage1 = selectedImage
                    } else {
                        selectedImage2 = selectedImage
                    }
                    // Clear results when selection changes
                    correlationResult = null
                    advancedCorrelationResult = null
                    showImagePicker = false
                },
                onDismiss = { showImagePicker = false }
        )
    }
}

/** Composable for image picker card */
@Composable
fun ImagePickerCard(
        modifier: Modifier = Modifier,
        title: String,
        selectedImage: ImagePreview?,
        otherImage: ImagePreview?,
        backgroundColor: Color,
        textColor: Color,
        onClick: () -> Unit
) {
    Card(
            modifier = modifier.clickable { onClick() },
            colors =
                    CardDefaults.cardColors(
                            containerColor =
                                    if (selectedImage != null) backgroundColor
                                    else MaterialTheme.colorScheme.surface
                    ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
                modifier = Modifier.fillMaxWidth().padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            if (selectedImage != null) {
                // Show selected image preview
                Image(
                        bitmap = selectedImage.bitmap.asImageBitmap(),
                        contentDescription = selectedImage.displayName,
                        modifier = Modifier.size(80.dp).clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                        text = selectedImage.displayName,
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor,
                        fontWeight = FontWeight.Medium
                )

                if (selectedImage == otherImage) {
                    Text(
                            text = "Same as other image",
                            style = MaterialTheme.typography.labelSmall,
                            color = Color(0xFF4CAF50)
                    )
                }
            } else {
                // Show placeholder
                Box(
                        modifier =
                                Modifier.size(80.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(Color.Gray.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.Center
                ) { Text(text = "📷", style = MaterialTheme.typography.headlineMedium) }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                        text = "Tap to select",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/** Composable for image picker dialog */
@Composable
fun ImagePickerDialog(
        images: List<ImagePreview>,
        onImageSelected: (ImagePreview) -> Unit,
        onDismiss: () -> Unit
) {
    AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                        text = "📁 Select Image (${images.size} available)",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                )
            },
            text = {
                LazyColumn(
                        modifier = Modifier.fillMaxWidth().height(400.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(images) { imagePreview ->
                        ImageDialogItem(
                                imagePreview = imagePreview,
                                onClick = { onImageSelected(imagePreview) }
                        )
                    }
                }
            },
            confirmButton = { TextButton(onClick = onDismiss) { Text("Cancel") } }
    )
}

/** Composable for image item in dialog */
@Composable
fun ImageDialogItem(imagePreview: ImagePreview, onClick: () -> Unit) {
    Card(
            modifier = Modifier.fillMaxWidth().clickable { onClick() },
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
                modifier = Modifier.fillMaxWidth().padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
        ) {
            // Image preview
            Image(
                    bitmap = imagePreview.bitmap.asImageBitmap(),
                    contentDescription = imagePreview.displayName,
                    modifier = Modifier.size(60.dp).clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(12.dp))

            // Image info
            Column(modifier = Modifier.weight(1f)) {
                Text(
                        text = imagePreview.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                )
                Text(
                        text = "${imagePreview.width} × ${imagePreview.height}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Arrow icon
            Text(
                    text = "→",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
            )
        }
    }
}
