#include <jni.h>
#include <string>
#include <vector>
#include <algorithm>
#include <android/log.h>
#include <android/bitmap.h>
#include "phase_correlation_wrapper.h"

#define LOG_TAG "PhaseCorrelationNative"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
using namespace cv;

// Forward declarations from phasecore_custom.cpp
extern cv::Point2d phaseCorrelateCustom(cv::InputArray _src1, cv::InputArray _src2, cv::InputArray _window, double * _maxValue);
// Use OpenCV standard createHanningWindow (already included in opencv2/imgproc.hpp)
#endif

extern "C" JNIEXPORT jlong JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateLearn(
        JNIEnv *env,
        jclass clazz,
        jobject bitmap) {

#ifdef USE_OPENCV
    AndroidBitmapInfo info;
    void* pixels;

    // Get bitmap info
    if (AndroidBitmap_getInfo(env, bitmap, &info) < 0) {
        LOGE("Failed to get bitmap info");
        return -1;
    }

    // Lock bitmap pixels
    if (AndroidBitmap_lockPixels(env, bitmap, &pixels) < 0) {
        LOGE("Failed to lock bitmap pixels");
        return -1;
    }

    // Convert bitmap to OpenCV Mat
    Mat src;
    if (info.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        Mat rgba(info.height, info.width, CV_8UC4, pixels);
        cvtColor(rgba, src, COLOR_RGBA2GRAY);
    } else if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        Mat rgb565(info.height, info.width, CV_8UC2, pixels);
        cvtColor(rgb565, src, COLOR_BGR5652GRAY);
    } else {
        LOGE("Unsupported bitmap format");
        AndroidBitmap_unlockPixels(env, bitmap);
        return -1;
    }

    // Unlock bitmap pixels
    AndroidBitmap_unlockPixels(env, bitmap);

    // Convert to double precision
    Mat src_64F;
    src.convertTo(src_64F, CV_64F);

    // Call phase correlation learn
    jlong handle = phaseCorrelateLearnWrapper(src_64F);

    LOGI("phaseCorrelateLearn completed with handle: %lld", (long long)handle);
    return handle;
#else
    LOGE("OpenCV not available - phase correlation not supported");
    return -1;
#endif
}

extern "C" JNIEXPORT jlong JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateLearnFromBitmap(
        JNIEnv *env,
        jclass clazz,
        jobject bitmap) {

#ifdef USE_OPENCV
    LOGI("=== PHASE CORRELATION LEARN FROM BITMAP WITH MAT PREPROCESSING ===");

    AndroidBitmapInfo info;
    void* pixels;

    // Get bitmap info
    if (AndroidBitmap_getInfo(env, bitmap, &info) < 0) {
        LOGE("Failed to get bitmap info");
        return -1;
    }

    // Lock bitmap pixels
    if (AndroidBitmap_lockPixels(env, bitmap, &pixels) < 0) {
        LOGE("Failed to lock bitmap pixels");
        return -1;
    }

    // Convert bitmap to OpenCV Mat
    Mat src;
    if (info.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        Mat rgba(info.height, info.width, CV_8UC4, pixels);
        cvtColor(rgba, src, COLOR_RGBA2GRAY);
    } else if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        Mat rgb565(info.height, info.width, CV_8UC2, pixels);
        cvtColor(rgb565, src, COLOR_BGR5652GRAY);
    } else {
        LOGE("Unsupported bitmap format");
        AndroidBitmap_unlockPixels(env, bitmap);
        return -1;
    }

    // Unlock bitmap pixels
    AndroidBitmap_unlockPixels(env, bitmap);

    LOGI("Bitmap converted to Mat: %dx%d", src.cols, src.rows);

    // Bước 1: Chuyển ảnh Bitmap thành Mat đã xử lý (preprocessing)
    // Resize to 512x512 (like customer's inputSize)
    Mat resized;
    resize(src, resized, Size(512, 512));

    // Convert to CV_32FC1 and normalize to [0..1] (like customer's preprocessing)
    Mat processed;
    resized.convertTo(processed, CV_32FC1, 1.0 / 255.0);

    LOGI("Mat preprocessing completed: %dx%d -> %dx%d, type: %d",
         src.cols, src.rows, processed.cols, processed.rows, processed.type());

    // Convert to double precision for phase correlation wrapper
    Mat src_64F;
    processed.convertTo(src_64F, CV_64F);

    // Bước 2: Gửi Mat vào phase correlation wrapper
    jlong handle = phaseCorrelateLearnWrapper(src_64F);

    LOGI("phaseCorrelateLearnFromBitmap completed with handle: %lld", (long long)handle);
    return handle;
#else
    LOGE("OpenCV not available - phase correlation not supported");
    return -1;
#endif
}

extern "C" JNIEXPORT jlong JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateLearnFromMat(
        JNIEnv *env,
        jclass clazz,
        jlong matAddr) {

#ifdef USE_OPENCV
    LOGI("=== PHASE CORRELATION LEARN FROM MAT ADDRESS ===");

    // Get Mat from native address
    Mat* matPtr = (Mat*)matAddr;
    if (matPtr == nullptr) {
        LOGE("Invalid Mat address");
        return -1;
    }

    Mat src = *matPtr;
    if (src.empty()) {
        LOGE("Empty Mat");
        return -1;
    }

    LOGI("Mat received: %dx%d, type: %d", src.cols, src.rows, src.type());

    // Convert to double precision for phase correlation wrapper
    Mat src_64F;
    src.convertTo(src_64F, CV_64F);

    // Call phase correlation learn
    jlong handle = phaseCorrelateLearnWrapper(src_64F);

    LOGI("phaseCorrelateLearnFromMat completed with handle: %lld", (long long)handle);
    return handle;
#else
    LOGE("OpenCV not available - phase correlation not supported");
    return -1;
#endif
}

extern "C" JNIEXPORT jdoubleArray JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateProc(
        JNIEnv *env,
        jclass clazz,
        jlong handle1,
        jlong handle2,
        jint rows,
        jint cols) {

#ifdef USE_OPENCV
    double correlationValue;
    Point2d result = phaseCorrelateProcWrapper(handle1, handle2, rows, cols, &correlationValue);

    // Create result array
    jdoubleArray resultArray = env->NewDoubleArray(3);
    if (resultArray == nullptr) {
        LOGE("Failed to create result array");
        return nullptr;
    }

    jdouble values[3] = {result.x, result.y, correlationValue};
    env->SetDoubleArrayRegion(resultArray, 0, 3, values);

    LOGI("phaseCorrelateProc completed: x=%.2f, y=%.2f, corr=%.2f",
         result.x, result.y, correlationValue);

    return resultArray;
#else
    LOGE("OpenCV not available - phase correlation not supported");

    double correlationValue;
    auto result = phaseCorrelateProcWrapper(handle1, handle2, rows, cols, &correlationValue);

    // Create result array
    jdoubleArray resultArray = env->NewDoubleArray(3);
    if (resultArray == nullptr) {
        return nullptr;
    }

    jdouble values[3] = {result.x, result.y, correlationValue};
    env->SetDoubleArrayRegion(resultArray, 0, 3, values);

    return resultArray;
#endif
}

extern "C" JNIEXPORT void JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_releaseHandle(
        JNIEnv *env,
        jclass clazz,
        jlong handle) {

    releaseHandleWrapper(handle);
    LOGI("Handle %lld released", (long long)handle);
}

extern "C" JNIEXPORT jdoubleArray JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateCustomDirect(
        JNIEnv *env,
        jclass clazz,
        jobject bitmap1,
        jobject bitmap2) {

#ifdef USE_OPENCV
    try {
        LOGI("=== DIRECT CALL TO phaseCorrelateCustom ===");

        // Convert bitmap1 to Mat
        AndroidBitmapInfo bitmapInfo1;
        void* bitmapPixels1;

        if (AndroidBitmap_getInfo(env, bitmap1, &bitmapInfo1) < 0) {
            LOGE("Failed to get bitmap1 info");
            return nullptr;
        }

        if (AndroidBitmap_lockPixels(env, bitmap1, &bitmapPixels1) < 0) {
            LOGE("Failed to lock bitmap1 pixels");
            return nullptr;
        }

        Mat src1(bitmapInfo1.height, bitmapInfo1.width, CV_8UC4, bitmapPixels1);
        Mat gray1;
        cvtColor(src1, gray1, COLOR_RGBA2GRAY);
        gray1.convertTo(gray1, CV_32F, 1.0/255.0);
        AndroidBitmap_unlockPixels(env, bitmap1);

        // Convert bitmap2 to Mat
        AndroidBitmapInfo bitmapInfo2;
        void* bitmapPixels2;

        if (AndroidBitmap_getInfo(env, bitmap2, &bitmapInfo2) < 0) {
            LOGE("Failed to get bitmap2 info");
            return nullptr;
        }

        if (AndroidBitmap_lockPixels(env, bitmap2, &bitmapPixels2) < 0) {
            LOGE("Failed to lock bitmap2 pixels");
            return nullptr;
        }

        Mat src2(bitmapInfo2.height, bitmapInfo2.width, CV_8UC4, bitmapPixels2);
        Mat gray2;
        cvtColor(src2, gray2, COLOR_RGBA2GRAY);
        gray2.convertTo(gray2, CV_32F, 1.0/255.0);
        AndroidBitmap_unlockPixels(env, bitmap2);

        LOGI("Direct call: Images converted to CV_32F (%dx%d)", gray1.cols, gray1.rows);

        // Create Hanning window using OpenCV standard function
        Mat window;
        cv::createHanningWindow(window, gray1.size(), CV_32F);

        // Direct call to OpenCV phaseCorrelate (like in sample code)
        double correlationValue;
        Point2d result = cv::phaseCorrelate(gray1, gray2, window, &correlationValue);

        LOGI("Direct OpenCV phaseCorrelate result (like sample code):");
        LOGI("  Offset: x=%.6f, y=%.6f", result.x, result.y);
        LOGI("  Correlation: %.6f (%.1f%%)", correlationValue, correlationValue * 100);

        // Create result array
        jdoubleArray resultArray = env->NewDoubleArray(3);
        if (resultArray == nullptr) {
            LOGE("Failed to create result array");
            return nullptr;
        }

        jdouble values[3] = {result.x, result.y, correlationValue};
        env->SetDoubleArrayRegion(resultArray, 0, 3, values);

        return resultArray;

    } catch (const std::exception& e) {
        LOGE("Exception in phaseCorrelateCustomDirect: %s", e.what());
        return nullptr;
    }
#else
    LOGE("OpenCV not available - direct phase correlation not supported");
    return nullptr;
#endif
}

// Advanced phase correlation with rotation and multiple images
extern "C" JNIEXPORT jdoubleArray JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_runAdvancedPhaseCorrelate(
    JNIEnv *env, jclass clazz, jobject camPicture, jobjectArray userImages, jint inputSize, jdouble minDistance) {

#ifdef USE_OPENCV
    LOGI("=== ADVANCED PHASE CORRELATION START ===");
    LOGI("Input size: %d, Min distance: %f", inputSize, minDistance);

    try {
        // Get camera picture
        AndroidBitmapInfo camBitmapInfo;
        void* camBitmapPixels;

        if (AndroidBitmap_getInfo(env, camPicture, &camBitmapInfo) < 0) {
            LOGE("Failed to get camera bitmap info");
            return env->NewDoubleArray(0);
        }

        if (AndroidBitmap_lockPixels(env, camPicture, &camBitmapPixels) < 0) {
            LOGE("Failed to lock camera bitmap pixels");
            return env->NewDoubleArray(0);
        }

        Mat camSrc(camBitmapInfo.height, camBitmapInfo.width, CV_8UC4, camBitmapPixels);
        Mat camGray;
        cvtColor(camSrc, camGray, COLOR_RGBA2GRAY);
        camGray.convertTo(camGray, CV_32F, 1.0/255.0);
        AndroidBitmap_unlockPixels(env, camPicture);

        // Resize camera image to input size
        Mat camPictureResized;
        resize(camGray, camPictureResized, Size(inputSize, inputSize));

        LOGI("Camera image processed: %dx%d -> %dx%d", camGray.cols, camGray.rows, inputSize, inputSize);

        // Get number of user images
        jsize userImageCount = env->GetArrayLength(userImages);
        LOGI("Processing %d user images", userImageCount);

        double maxCorrelation = 0.0;
        Point2d bestOffset(0, 0);
        bool foundMatch = false;
        int maxImages = std::min(2, (int)userImageCount); // Limit to 2 images like original code

        for (int i = 0; i < maxImages; i++) {
            jobject userImage = env->GetObjectArrayElement(userImages, i);
            if (userImage == nullptr) continue;

            // Process user image
            AndroidBitmapInfo userBitmapInfo;
            void* userBitmapPixels;

            if (AndroidBitmap_getInfo(env, userImage, &userBitmapInfo) < 0) {
                LOGE("Failed to get user bitmap info for image %d", i);
                env->DeleteLocalRef(userImage);
                continue;
            }

            if (AndroidBitmap_lockPixels(env, userImage, &userBitmapPixels) < 0) {
                LOGE("Failed to lock user bitmap pixels for image %d", i);
                env->DeleteLocalRef(userImage);
                continue;
            }

            Mat userSrc(userBitmapInfo.height, userBitmapInfo.width, CV_8UC4, userBitmapPixels);
            Mat userGray;
            cvtColor(userSrc, userGray, COLOR_RGBA2GRAY);
            userGray.convertTo(userGray, CV_32F, 1.0/255.0);
            AndroidBitmap_unlockPixels(env, userImage);

            // Resize user image to input size
            Mat userImageResized;
            resize(userGray, userImageResized, Size(inputSize, inputSize));

            // Create Hanning window
            Mat window;
            cv::createHanningWindow(window, userImageResized.size(), CV_32F);

            // Initial phase correlation without rotation
            double correlationValue;
            Point2d offset = cv::phaseCorrelate(camPictureResized, userImageResized, window, &correlationValue);

            LOGI("Image %d initial correlation: %f at offset (%.2f, %.2f)", i, correlationValue, offset.x, offset.y);

            if (correlationValue > maxCorrelation) {
                maxCorrelation = correlationValue;
                bestOffset = offset;
            }

            // Check early termination conditions
            if (minDistance < 0.4 && correlationValue > 0.25) {
                LOGI("Early termination: minDistance=%.2f, correlation=%.2f", minDistance, correlationValue);
                foundMatch = true;
                break;
            }

            // If correlation is low, try rotation (like customer's code)
            if (correlationValue < 0.4) {
                LOGI("Low correlation (%.2f), trying rotation for image %d", correlationValue, i);

                // Try 4 main rotations: 0, 90, 180, 270 degrees
                std::vector<int> rotations = {0, 90, 180, 270};

                for (int rotation : rotations) {
                    Mat rotatedImage;
                    if (rotation == 0) {
                        rotatedImage = userImageResized.clone();
                    } else if (rotation == 90) {
                        cv::rotate(userImageResized, rotatedImage, ROTATE_90_CLOCKWISE);
                    } else if (rotation == 180) {
                        cv::rotate(userImageResized, rotatedImage, ROTATE_180);
                    } else if (rotation == 270) {
                        cv::rotate(userImageResized, rotatedImage, ROTATE_90_COUNTERCLOCKWISE);
                    }

                    // Try small angle variations (-3 to +3 degrees in 0.15 steps like customer code)
                    double angleOffset = 0.15;
                    int maxLoop = 28; // Like customer's max_loop
                    bool isBreak = false;

                    for (int ii = 0; ii < maxLoop && !isBreak; ii++) {
                        double angle = 0.0;
                        if (ii % 2 == 0) {
                            angle = angleOffset;
                            angleOffset += 0.15;
                        } else {
                            angle = -angleOffset;
                        }

                        if (std::abs(angle) < 0.01) {
                            continue; // Skip angle = 0
                        }

                        Mat finalImage;
                        Point2f center(rotatedImage.cols/2.0, rotatedImage.rows/2.0);
                        Mat rotMatrix = getRotationMatrix2D(center, angle, 1.0);
                        warpAffine(rotatedImage, finalImage, rotMatrix, rotatedImage.size());

                        // Phase correlate with rotated image
                        Mat rotWindow;
                        cv::createHanningWindow(rotWindow, finalImage.size(), CV_32F);
                        double rotCorrelation;
                        Point2d rotOffset = cv::phaseCorrelate(camPictureResized, finalImage, rotWindow, &rotCorrelation);

                        if (rotCorrelation > maxCorrelation) {
                            maxCorrelation = rotCorrelation;
                            bestOffset = rotOffset;
                            LOGI("Better correlation found: %.3f at rotation %d + %.1f degrees", rotCorrelation, rotation, angle);
                        }

                        // Check termination conditions (like customer's code)
                        if ((minDistance <= 0.3) ||
                            (minDistance <= 0.4 && rotCorrelation > 0.27) ||
                            (minDistance <= 0.55 && rotCorrelation > 0.35) ||
                            (rotCorrelation > 0.5)) {
                            LOGI("Rotation termination: correlation=%.3f", rotCorrelation);
                            isBreak = true;
                            foundMatch = true;
                            break;
                        }
                    }
                    if (isBreak) break;
                }
                if (foundMatch) break;
            } else {
                foundMatch = true;
                break;
            }

            env->DeleteLocalRef(userImage);
        }

        LOGI("=== ADVANCED PHASE CORRELATION RESULT ===");
        LOGI("Max correlation: %.3f", maxCorrelation);
        LOGI("Best offset: (%.2f, %.2f)", bestOffset.x, bestOffset.y);
        LOGI("Found match: %s", foundMatch ? "true" : "false");

        // Return result array: [maxCorrelation, xOffset, yOffset, foundMatch]
        jdoubleArray result = env->NewDoubleArray(4);
        if (result != nullptr) {
            jdouble resultData[4];
            resultData[0] = maxCorrelation;
            resultData[1] = bestOffset.x;
            resultData[2] = bestOffset.y;
            resultData[3] = foundMatch ? 1.0 : 0.0;
            env->SetDoubleArrayRegion(result, 0, 4, resultData);
        }

        return result;

    } catch (const std::exception& e) {
        LOGE("Exception in advanced phase correlation: %s", e.what());
        return env->NewDoubleArray(0);
    }
#else
    LOGE("OpenCV not available - runAdvancedPhaseCorrelate not implemented");
    return env->NewDoubleArray(0);
#endif
}

extern "C" JNIEXPORT jdoubleArray JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_phaseCorrelateDirect(
    JNIEnv* env, jclass clazz,
    jobject bitmap1, jobject bitmap2) {

#ifdef USE_OPENCV
    LOGI("=== DIRECT PHASE CORRELATE CALL ===");

    AndroidBitmapInfo info1, info2;
    void* pixels1;
    void* pixels2;

    // Lấy thông tin và khóa pixels của bitmap1
    if (AndroidBitmap_getInfo(env, bitmap1, &info1) < 0 ||
        AndroidBitmap_lockPixels(env, bitmap1, &pixels1) < 0) {
        LOGE("Failed to get bitmap1 info or lock pixels");
        return nullptr;
    }

    // Lấy thông tin và khóa pixels của bitmap2
    if (AndroidBitmap_getInfo(env, bitmap2) < 0 ||
        AndroidBitmap_lockPixels(env, bitmap2, &pixels2) < 0) {
        LOGE("Failed to get bitmap2 info or lock pixels");
        AndroidBitmap_unlockPixels(env, bitmap1);
        return nullptr;
    }

    // Chuyển bitmap sang Mat và convert sang grayscale
    Mat src1, src2;
    if (info1.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        Mat rgba1(info1.height, info1.width, CV_8UC4, pixels1);
        cvtColor(rgba1, src1, COLOR_RGBA2GRAY);
    } else if (info1.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        Mat rgb1(info1.height, info1.width, CV_8UC2, pixels1);
        cvtColor(rgb1, src1, COLOR_BGR5652GRAY);
    } else {
        LOGE("Unsupported bitmap1 format: %d", info1.format);
        AndroidBitmap_unlockPixels(env, bitmap1);
        AndroidBitmap_unlockPixels(env, bitmap2);
        return nullptr;
    }

    if (info2.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        Mat rgba2(info2.height, info2.width, CV_8UC4, pixels2);
        cvtColor(rgba2, src2, COLOR_RGBA2GRAY);
    } else if (info2.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        Mat rgb2(info2.height, info2.width, CV_8UC2, pixels2);
        cvtColor(rgb2, src2, COLOR_BGR5652GRAY);
    } else {
        LOGE("Unsupported bitmap2 format: %d", info2.format);
        AndroidBitmap_unlockPixels(env, bitmap1);
        AndroidBitmap_unlockPixels(env, bitmap2);
        return nullptr;
    }

    // Mở khóa pixels
    AndroidBitmap_unlockPixels(env, bitmap1);
    AndroidBitmap_unlockPixels(env, bitmap2);

    LOGI("Bitmaps converted to grayscale: %dx%d and %dx%d", src1.cols, src1.rows, src2.cols, src2.rows);

    // Kiểm tra kích thước phải giống nhau
    if (src1.size() != src2.size()) {
        LOGE("Input images must have the same size for phaseCorrelate");
        return nullptr;
    }

    // Convert to CV_32FC1 và chuẩn hóa về [0..1]
    Mat proc1, proc2;
    src1.convertTo(proc1, CV_32FC1, 1.0 / 255.0);
    src2.convertTo(proc2, CV_32FC1, 1.0 / 255.0);

    LOGI("Images converted to CV_32FC1 and normalized");

    // Gọi hàm phaseCorrelate
    double maxVal = 0.0;
    Point2d shift = cv::phaseCorrelate(proc1, proc2, Mat(), &maxVal);

    LOGI("Direct phaseCorrelate result:");
    LOGI("  Shift: x=%.6f, y=%.6f", shift.x, shift.y);
    LOGI("  Correlation: %.6f (%.1f%%)", maxVal, maxVal * 100);

    // Chuẩn bị kết quả trả về
    jdouble result[3] = {shift.x, shift.y, maxVal};
    jdoubleArray outArray = env->NewDoubleArray(3);
    if (outArray == nullptr) {
        LOGE("Failed to allocate jdoubleArray");
        return nullptr;
    }

    env->SetDoubleArrayRegion(outArray, 0, 3, result);
    return outArray;

#else
    LOGE("OpenCV not available - phaseCorrelateDirect not supported");
    return nullptr;
#endif
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_yourpackage_phasecorrelation_PhaseCorrelationNative_isNativeLibraryLoaded(
        JNIEnv *env,
        jclass clazz) {

#ifdef USE_OPENCV
    LOGI("Native library is loaded with OpenCV support");
    return JNI_TRUE;
#else
    LOGI("Native library is loaded but OpenCV is not available");
    return JNI_FALSE;
#endif
}
