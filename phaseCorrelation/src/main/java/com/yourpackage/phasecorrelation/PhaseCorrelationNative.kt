package com.yourpackage.phasecorrelation

import android.graphics.Bitmap
import android.util.Log

/** Native interface for Phase Correlation operations */
class PhaseCorrelationNative {

    companion object {
        private const val TAG = "PhaseCorrelationNative"

        private var isLibraryLoaded = false

        init {
            try {
                // Try to load OpenCV first (optional)
                try {
                    System.loadLibrary("opencv_java4")
                    Log.d(TAG, "OpenCV library loaded successfully")
                } catch (e: UnsatisfiedLinkError) {
                    Log.w(TAG, "OpenCV library not found - continuing without OpenCV support")
                }

                // Load our native library
                System.loadLibrary("phasecorrelation")
                isLibraryLoaded = true
                Log.d(TAG, "Phase correlation native library loaded successfully")
            } catch (e: UnsatisfiedLinkError) {
                Log.e(TAG, "Failed to load phase correlation native library", e)
                isLibraryLoaded = false
            }
        }

        /**
         * Initialize Phase Correlation with image data
         * @param imageData Bitmap image data
         * @return Handle to the processed data, or -1 if failed
         */
        @JvmStatic external fun phaseCorrelateLearn(imageData: Bitmap): Long

        /**
         * Process phase correlation between two learned images
         * @param handle1 Handle from first phaseCorrelateLearn call
         * @param handle2 Handle from second phaseCorrelateLearn call
         * @param rows Original image rows
         * @param cols Original image cols
         * @return Array containing [x_offset, y_offset, correlation_value]
         */
        @JvmStatic
        external fun phaseCorrelateProc(
                handle1: Long,
                handle2: Long,
                rows: Int,
                cols: Int
        ): DoubleArray

        /**
         * Release memory for a learned image handle
         * @param handle Handle to release
         */
        @JvmStatic external fun releaseHandle(handle: Long)

        /**
         * Direct call to OpenCV phaseCorrelate function (like sample code) Uses Hanning window and
         * standard OpenCV implementation
         * @param image1 First image bitmap
         * @param image2 Second image bitmap
         * @return Array containing [x_offset, y_offset, correlation_value] or null if failed
         */
        @JvmStatic
        external fun phaseCorrelateCustomDirect(image1: Bitmap, image2: Bitmap): DoubleArray?

        /**
         * Check if native library is loaded
         * @return true if loaded successfully
         */
        @JvmStatic external fun isNativeLibraryLoaded(): Boolean

        /**
         * Advanced phase correlation with rotation and multiple image support
         * @param camPicture Current camera image
         * @param userImages Array of user reference images
         * @param inputSize Target size for processing
         * @param minDistance Minimum distance threshold
         * @return Array containing [maxCorrelation, xOffset, yOffset, foundMatch] or null if failed
         */
        @JvmStatic
        external fun runAdvancedPhaseCorrelate(
                camPicture: Bitmap,
                userImages: Array<Bitmap>,
                inputSize: Int,
                minDistance: Double
        ): DoubleArray?

        /** Check if library was loaded during initialization */
        @JvmStatic fun isLibraryInitialized(): Boolean = isLibraryLoaded
    }
}

/** High-level wrapper for Phase Correlation operations */
class PhaseCorrelation {

    data class CorrelationResult(
            val xOffset: Double,
            val yOffset: Double,
            val correlationValue: Double
    )

    /**
     * Calculate phase correlation between two images (original complex method)
     * @param image1 First image
     * @param image2 Second image
     * @return Correlation result or null if failed
     */
    fun calculatePhaseCorrelation(image1: Bitmap, image2: Bitmap): CorrelationResult? {
        return try {
            val handle1 = PhaseCorrelationNative.phaseCorrelateLearn(image1)
            val handle2 = PhaseCorrelationNative.phaseCorrelateLearn(image2)

            if (handle1 == -1L || handle2 == -1L) {
                Log.e("PhaseCorrelation", "Failed to learn images")
                return null
            }

            val result =
                    PhaseCorrelationNative.phaseCorrelateProc(
                            handle1,
                            handle2,
                            image1.height,
                            image1.width
                    )

            // Clean up handles
            PhaseCorrelationNative.releaseHandle(handle1)
            PhaseCorrelationNative.releaseHandle(handle2)

            if (result.size >= 3) {
                CorrelationResult(result[0], result[1], result[2])
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e("PhaseCorrelation", "Error calculating phase correlation", e)
            null
        }
    }

    /**
     * DIRECT CALL to OpenCV phaseCorrelate function - MATCHES SAMPLE CODE This directly calls
     * OpenCV standard phaseCorrelate with Hanning window Same as: retVal =
     * phaseCorrelate(camPicture1, grayImage1, window, response)
     * @param image1 First image
     * @param image2 Second image
     * @return Correlation result or null if failed
     */
    fun calculatePhaseCorrelationDirect(image1: Bitmap, image2: Bitmap): CorrelationResult? {
        return try {
            Log.d("PhaseCorrelation", "=== DIRECT CALL TO OpenCV phaseCorrelate (like sample) ===")
            Log.d("PhaseCorrelation", "Image1: ${image1.width}x${image1.height}")
            Log.d("PhaseCorrelation", "Image2: ${image2.width}x${image2.height}")

            val result = PhaseCorrelationNative.phaseCorrelateCustomDirect(image1, image2)

            if (result != null && result.size >= 3) {
                Log.d(
                        "PhaseCorrelation",
                        "Direct OpenCV result: x=${result[0]}, y=${result[1]}, corr=${result[2]}"
                )
                CorrelationResult(result[0], result[1], result[2])
            } else {
                Log.e("PhaseCorrelation", "Direct OpenCV call failed or returned invalid result")
                null
            }
        } catch (e: Exception) {
            Log.e("PhaseCorrelation", "Error in direct OpenCV phase correlation", e)
            null
        }
    }

    /**
     * Advanced phase correlation with rotation and multiple images (like customer's
     * run_phaseCorrelate)
     * @param camPicture Current camera image
     * @param userImages List of user reference images
     * @param inputSize Target size for processing (default 512)
     * @param minDistance Minimum distance threshold (default 0.4)
     * @return AdvancedCorrelationResult or null if failed
     */
    fun runAdvancedPhaseCorrelate(
            camPicture: Bitmap,
            userImages: List<Bitmap>,
            inputSize: Int = 512,
            minDistance: Double = 0.4
    ): AdvancedCorrelationResult? {
        return try {
            Log.d("PhaseCorrelation", "=== ADVANCED PHASE CORRELATION (Customer Style) ===")
            Log.d("PhaseCorrelation", "Camera: ${camPicture.width}x${camPicture.height}")
            Log.d("PhaseCorrelation", "User images: ${userImages.size}")
            Log.d("PhaseCorrelation", "Input size: $inputSize, Min distance: $minDistance")

            val result =
                    PhaseCorrelationNative.runAdvancedPhaseCorrelate(
                            camPicture,
                            userImages.toTypedArray(),
                            inputSize,
                            minDistance
                    )

            if (result != null && result.size >= 4) {
                Log.d(
                        "PhaseCorrelation",
                        "Advanced result: max=${result[0]}, x=${result[1]}, y=${result[2]}, match=${result[3]}"
                )
                AdvancedCorrelationResult(
                        maxCorrelation = result[0],
                        xOffset = result[1],
                        yOffset = result[2],
                        foundMatch = result[3] > 0.5,
                        processingTime = 0 // Will be calculated in native code
                )
            } else {
                Log.e(
                        "PhaseCorrelation",
                        "Advanced phase correlation failed or returned invalid result"
                )
                null
            }
        } catch (e: Exception) {
            Log.e("PhaseCorrelation", "Error in advanced phase correlation", e)
            null
        }
    }

    /** Check if the native library is available */
    fun isAvailable(): Boolean {
        return try {
            if (!PhaseCorrelationNative.isLibraryInitialized()) {
                Log.d("PhaseCorrelation", "Native library not initialized")
                return false
            }
            PhaseCorrelationNative.isNativeLibraryLoaded()
        } catch (e: Exception) {
            Log.e("PhaseCorrelation", "Error checking library availability", e)
            false
        }
    }
}

/** Result class for advanced phase correlation */
data class AdvancedCorrelationResult(
        val maxCorrelation: Double,
        val xOffset: Double,
        val yOffset: Double,
        val foundMatch: Boolean,
        val processingTime: Long
)
